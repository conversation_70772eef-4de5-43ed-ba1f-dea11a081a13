# -*- coding: utf-8 -*-

from odoo import models
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def pack_and_validate_picking(self, picking):
        if self.state != 'assigned':
            raise UserError("Picking %s should be in 'Ready' stage." % picking.name)
        res = self.action_put_in_pack()
        package_wiz = self.env['choose.delivery.package'].browse(res['res_id'])
        package_wiz.action_put_in_pack()
        package_wiz.picking_id.with_context(skip_sms=True).button_validate()
