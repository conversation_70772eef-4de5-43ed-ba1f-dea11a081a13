<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record model="ir.rule" id="sale_target_comp_rule">
            <field name="name">Sales Target multi-company</field>
            <field name="model_id" ref="model_sale_target"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>


        <record model="ir.rule" id="sale_target_rule_all">
            <field name="name">Sales Target All</field>
            <field name="model_id" ref="model_sale_target"/>
            <field name="groups" eval="[(4, ref('ame_base.group_sales_director'))]"/>
            <field name="domain_force">[(1,'=',1)]</field>
        </record>

        <record model="ir.rule" id="sale_target_rule_finance">
            <field name="name">Sales Target Finance</field>
            <field name="model_id" ref="model_sale_target"/>
            <field name="groups" eval="[(4, ref('ame_base.group_finance_controller'))]"/>
            <field name="domain_force">[(1,'=',1)]</field>
             <field name="perm_read" eval="True"/>
             <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- sale_target_rule_team_lead moved to ame_crm -->

        <record model="ir.rule" id="sale_target_rule_user">
            <field name="name">Sales Target User</field>
            <field name="model_id" ref="model_sale_target"/>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
            <field name="domain_force">[('target_line.salesman_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record model="ir.rule" id="sale_target_rule_bm">
            <field name="name">Sales Target BM</field>
            <field name="model_id" ref="model_sale_target"/>
            <field name="groups" eval="[(4, ref('ame_base.group_branch_manager'))]"/>
            <field name="domain_force">[(1,'=',1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record model="ir.rule" id="sale_target_line_rule_all">
            <field name="name">Sales Target Line All</field>
            <field name="model_id" ref="model_sale_target_lines"/>
            <field name="groups" eval="[(4, ref('ame_base.group_sales_director')), (4, ref('ame_base.group_finance_controller')), (4, ref('sales_team.group_sale_manager')), (4, ref('ame_base.group_branch_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <!-- sale_target_line_rule_assistant moved to ame_crm -->

        <record model="ir.rule" id="sale_target_line_rule_user">
            <field name="name">Sales Target Line User</field>
            <field name="model_id" ref="model_sale_target_lines"/>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
            <field name="domain_force">[('salesman_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>


    </data>
</odoo>