<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>

        <record id="view_emp_bank_report_wiz" model="ir.ui.view">
            <field name="name">emp.bank.report.wiz.form</field>
            <field name="model">emp.bank.report.wiz</field>
            <field name="arch" type="xml">
                <form string="Report">
                    <group>
                        <field name="filter_by" widget="radio" options="{'horizontal': true}"/>
                        <field name="employee_ids" widget="many2many_tags" invisible="filter_by != 'employee'" required="filter_by == 'employee'" options="{'no_open':True, 'no_create':True}"/>
                        <field name="department_ids" widget="many2many_tags" options="{'no_open':True, 'no_create':True}" domain="[('company_attendance_id', '=', company_id)]" invisible="filter_by != 'dep'" required="filter_by == 'dep'"/>
                        <field name="company_id" options="{'no_open':True, 'no_create':True}" invisible="filter_by != 'company'" required="filter_by == 'company'"/>
                    </group>
                    <footer>
                        <button name="button_print" string="Generate" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_emp_bank_report_wiz" model="ir.actions.act_window">
            <field name="name">Employee Bank Details</field>
            <field name="res_model">emp.bank.report.wiz</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_emp_bank_report_wiz"/>
            <field name="target">new</field>
        </record>


        <menuitem id="menu_action_emp_bank_report_wiz" action="action_emp_bank_report_wiz" parent="hr.hr_menu_hr_reports" sequence="2" groups="hr.group_hr_manager"/>

    </data>
</odoo>