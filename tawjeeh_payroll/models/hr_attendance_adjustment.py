from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from odoo.addons.tawjeeh_base.utils import convert_date_to_local, get_float_from_time, get_time_from_float
from odoo.addons.tawjeeh_payroll.utils import get_payroll_period
from datetime import datetime, timedelta
import pytz


class HRAttendanceAdjustment(models.Model):
    _name = 'hr.attendance.adjustment'
    _description = "Daily Attendance Sheet"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'department_id'
    _order = 'date_from desc'

    def _get_date(self):
        today = fields.Date.context_today(self)
        return today

    def search_employee_id(self, operator, value):
        attendance_ids = []

        if not value:
            return [('id', 'in', attendance_ids)]

        employees = self.env['hr.employee'].search([('name', operator, value.upper()), ('company_id', '=', self.env.company.id)])
        if not employees:
            return [('id', 'in', attendance_ids)]

        employee_ids = employees.ids
        self._cr.execute("""
            SELECT att_line.adjustment_id
            FROM hr_attendance_adjustment_line AS att_line
            LEFT JOIN hr_attendance_adjustment adj ON (adj.id = att_line.adjustment_id)
            WHERE att_line.employee_id IN %s AND adj.company_id = %s
        """, (tuple(employee_ids), self.env.company.id))
        res = self._cr.fetchall()
        if res:
            attendance_ids = [att_id[0] for att_id in res]
        return [('id', 'in', attendance_ids)]

    filter_employee_id = fields.Char('Employee', search='search_employee_id', compute='search_employee_id', store=False)
    department_id = fields.Many2one('hr.department', 'Department', required=True)
    date_from = fields.Date(string="Date", required=True, default=_get_date)
    line_ids = fields.One2many('hr.attendance.adjustment.line', 'adjustment_id', string='Attendances', readonly=True)
    company_id = fields.Many2one('res.company', string='Company', required=True, default=lambda self: self.env.company)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('to_confirm', 'To Dept.Manager'),
        ('to_hr', 'To HR'),
        ('cancel', 'Cancelled'),
        ('done', 'Approved')], default='draft', tracking=True,
        string='Status', required=True, readonly=True, index=True,
        help=' * The \'Draft\' status is used when a HR user is creating a new  attendance sheet. '
             '\n* The \'To HR\' status is used when  attendance sheet is confirmed by Dept.Manager.'
             '\n* The \'Approved\' status is used when  attendance sheet is accepted by the HR Manager.')

    @api.constrains('date_from', 'department_id')
    def _check_duplicate(self):
        for sheet in self:
            self._cr.execute("SELECT id FROM hr_attendance_adjustment WHERE department_id=%s AND date_from=%s \
                    AND state != 'cancel' AND id <> %s", (sheet.department_id.id, sheet.date_from, sheet.id))
            if self._cr.fetchall():
                return ValidationError(_('Only one sheet per department per day allowed'))

    @api.ondelete(at_uninstall=False)
    def _unlink_check(self):
        for rec in self:
            if rec.state not in ['cancel', 'draft']:
                raise ValidationError('In order to delete, you must cancel it first.')

    def button_confirm(self):
        for request in self:
            status = []
            to_fix = False
            for line in request.line_ids:
                work_time = line.work_time + line.time_plus - line.time_minus
                if work_time > 0 and line.status == 'ab':
                    line.status = 'present'
                if line.to_fix:
                    status.append('fix')
                    to_fix = True
                else:
                    status.append(line.status)
            if request.state == 'draft':
                if len(set(status)) == 1 and to_fix is False and (request.department_id.att_sheet_auto_approve_mgr or request.department_id.att_sheet_auto_approve_hr):
                    if request.department_id.att_sheet_auto_approve_mgr:
                        request.write({'state': 'to_hr'})
                    if request.department_id.att_sheet_auto_approve_hr:
                        request.check_before_date()
                        request.check_after_date()
                        request.line_ids.make_late_in_entry()
                        request.line_ids.write({'is_approved': True})
                        request.write({'state': 'done'})
                else:
                    request.write({'state': 'to_confirm'})
            elif request.state == 'to_confirm':
                if request.user_has_groups('tawjeeh_payroll.group_attendance_sheet_dep_manager') and (request.department_id.manager_id.user_id.id == self.env.user.id or request.department_id.att_sheet_dept_approver.user_id.id == self.env.user.id):
                    if len(set(status)) == 1 and to_fix is False and request.department_id.att_sheet_auto_approve_hr:
                        request.check_before_date()
                        request.check_after_date()
                        request.line_ids.make_late_in_entry()
                        request.line_ids.write({'is_approved': True})
                        request.write({'state': 'done'})
                    else:
                        request.write({'state': 'to_hr'})
                elif request.department_id.asst_appv_daily_attend:
                    if self.env.user.id in request.department_id.manager_ids.ids:
                        if len(set(status)) == 1 and to_fix is False and request.department_id.att_sheet_auto_approve_hr:
                            request.check_before_date()
                            request.check_after_date()
                            request.line_ids.make_late_in_entry()
                            request.line_ids.write({'is_approved': True})
                            request.write({'state': 'done'})
                        else:
                            request.write({'state': 'to_hr'})
                    else:
                        raise UserError("You don't have the rights to approve")
                else:
                    raise UserError("You don't have the rights to approve")
            elif request.state == 'to_hr' and request.user_has_groups('hr.group_hr_manager'):
                request.check_before_date()
                request.check_after_date()
                request.line_ids.make_late_in_entry()
                request.line_ids.write({'is_approved': True})
                request.write({'state': 'done'})

    def button_cancel(self):
        self.write({'state': 'cancel'})

    def action_approve(self):
        state_1 = False
        department_1 = False
        for request in self:
            if not state_1:
                state_1 = request.state
            if not department_1:
                department_1 = request.department_id.id
            if department_1 and department_1 != request.department_id.id:
                raise UserError(_('Selected sheets should be in same department!'))
            if state_1 and state_1 != request.state:
                raise UserError(_('Selected sheets should be in same state!'))
        self.button_confirm()

    def button_to_confirm(self):
        self.ensure_one()
        self.check_after_date()
        self.line_ids.remove_late_in_entry()
        self.write({'state': 'to_confirm'})

    def button_to_hr(self):
        self.ensure_one()
        self.check_after_date()
        self.line_ids.remove_late_in_entry()
        self.write({'state': 'to_hr'})

    def check_after_date(self):
        sheets = self.env['hr.attendance.adjustment'].sudo().search([('department_id', '=', self.department_id.id), ('date_from', '>', self.date_from), ('state', '=', 'done')])
        if sheets:
            raise UserError("Please send back all the Daily Attendance Sheets after %s" % self.date_from)

    def check_before_date(self):
        sheets = self.env['hr.attendance.adjustment'].sudo().search([('department_id', '=', self.department_id.id), ('date_from', '<', self.date_from), ('state', 'not in', ['cancel', 'done'])])
        if sheets:
            raise UserError("Please confirm all the Daily Attendance Sheets before %s" % self.date_from)

    def _get_emp_leaves(self, emp, start_datetime=None, end_datetime=None):
        leaves = []
        leave_obj = self.env['hr.leave']
        leave_ids = leave_obj.search([
            ('employee_id', '=', emp.id),
            ('is_encashment', '=', False),
            ('state', '=', 'validate')])

        for leave in leave_ids:
            date_from = fields.Datetime.from_string(leave.date_from)
            if end_datetime and date_from > end_datetime:
                continue
            date_to = fields.Datetime.from_string(leave.date_to)
            if start_datetime and date_to < start_datetime:
                continue
            leaves.append((date_from, date_to, leave))
        return leaves

    def get_attendances(self):
        def interval_without_leaves(contract, interval, leave_intervals):
            intervals = contract.resource_calendar_id.interval_remove_leaves(interval, leave_intervals)
            return intervals

        for sheet in self:
            sheet_lines = []
            # clear the lines
            sheet.line_ids.unlink()
            from_date = sheet.date_from
            day_start = datetime.combine(from_date, datetime.min.time())
            day_end = fields.Datetime.to_datetime(day_start).replace(hour=23, minute=59, second=59, microsecond=999999)
            public_holiday = self.env['hr.public.holiday'].get_public_holiday(from_date, sheet.department_id)
            # find all department employees
            employees = self.env['hr.employee'].sudo().search([('department_id', '=', sheet.department_id.id), ('exclude_attendance', '=', False)])
            for emp in employees:
                contract_ids = emp._get_contracts(from_date, from_date, states=['open', 'close'])

                if not emp.tz:
                    raise ValidationError("Please add time zone for the employee %s" % emp.name)
                else:
                    tz = pytz.timezone(emp.tz)

                if not contract_ids:
                    values = {
                        'date': from_date,
                        'employee_id': emp.id,
                        'adjustment_id': sheet.id,
                        'status': '',
                        'note2': 'Contract not found'
                    }
                    sheet_lines.append((0, 0, values))
                    continue
                contract = contract_ids[0]

                if contract.resource_calendar_id:
                    calender_id = contract.resource_calendar_id
                else:
                    values = {
                        'date': from_date,
                        'employee_id': emp.id,
                        'adjustment_id': sheet.id,
                        'contract_id': contract.id,
                        'status': '',
                        'note2': 'Working hours not found'
                    }
                    sheet_lines.append((0, 0, values))
                    continue

                if contract.att_policy_id:
                    policy_id = contract.att_policy_id
                else:
                    values = {
                        'date': from_date,
                        'employee_id': emp.id,
                        'adjustment_id': sheet.id,
                        'contract_id': contract.id,
                        'status': '',
                        'note2': 'Attendance policy not found'
                    }
                    sheet_lines.append((0, 0, values))
                    continue

                late_in = timedelta(hours=00, minutes=00, seconds=00)
                worktime = timedelta(hours=00, minutes=00, seconds=00)
                reserved_intervals = []

                work_intervals = calender_id.get_work_intervals(day_start, day_end)
                attendance_intervals, att_ids = self.env['biometric.attendance'].get_attendance_intervals(emp, day_start, day_end)
                leave_records = self._get_emp_leaves(emp, day_start, day_end)
                leaves = [(l[0], l[1]) for l in leave_records]
                overtime_policy = self.env['hr.attendance.policy'].get_overtime(policy_id)
                expected_work_hours, overtime_after = calender_id.get_work_hours(day_start, day_end)
                slh_time = 0
                status_text = ''
                note_mgr = ""
                missing_check_io = False

                if work_intervals:
                    if public_holiday:
                        values = {}
                        status = 'ph'
                        status_text = 'Public Holiday'
                        for l in leave_records:
                            status = 'leave'
                            status_text = l[2].holiday_status_id.name
                            values['note1'] = l[2].report_note

                        if attendance_intervals:
                            for attendance_interval in attendance_intervals:
                                worktime += attendance_interval[1] - attendance_interval[0]
                            float_worktime = worktime.total_seconds() / 3600.0

                            if float_worktime <= overtime_policy['ph_after']:
                                float_overtime = 0
                            else:
                                float_overtime = float_worktime * overtime_policy['ph_rate']

                            ac_sign_in = pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz)
                            ac_sign_out = pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz)

                            values.update({
                                'date': from_date,
                                'employee_id': emp.id,
                                'ac_sign_in': get_float_from_time(ac_sign_in),
                                'ac_sign_out': get_float_from_time(ac_sign_out),
                                'work_time': float_worktime,
                                'overtime': float_overtime,
                                'adjustment_id': sheet.id,
                                'status': status,
                                'status_text': status_text,
                                'related_att_ids': [(6, 0, [att.id for att in att_ids])],
                                'contract_id': contract.id,
                                'working_hours_id': calender_id.id
                            })
                            sheet_lines.append((0, 0, values))
                        else:
                            values.update({
                                'date': from_date,
                                'employee_id': emp.id,
                                'adjustment_id': sheet.id,
                                'status': status,
                                'status_text': status_text,
                                'contract_id': contract.id,
                                'working_hours_id': calender_id.id
                            })
                            sheet_lines.append((0, 0, values))
                    else:
                        for i, work_interval in enumerate(work_intervals):
                            att_work_intervals = []
                            diff_intervals = []
                            late_in_interval = []
                            diff_time = timedelta(hours=00, minutes=00, seconds=00)
                            late_in = timedelta(hours=00, minutes=00, seconds=00)
                            early_out = timedelta(hours=00, minutes=00, seconds=00)
                            overtime = timedelta(hours=00, minutes=00, seconds=00)
                            worktime = timedelta(hours=00, minutes=00, seconds=00)
                            float_early_out = 0.0

                            for j, att_interval in enumerate(attendance_intervals):
                                # max(pl_in, actual_in) < min(pl_out, actual_out)
                                if max(work_interval[0], att_interval[0]) < min(work_interval[1], att_interval[1]):
                                    current_att_interval = att_interval
                                    if i + 1 < len(work_intervals):
                                        next_work_interval = work_intervals[i + 1]
                                        if max(next_work_interval[0], current_att_interval[0]) < min(next_work_interval[1], current_att_interval[1]):
                                            split_att_interval = (next_work_interval[0], current_att_interval[1])
                                            current_att_interval = (current_att_interval[0], next_work_interval[0])
                                            attendance_intervals[j] = current_att_interval
                                            attendance_intervals.insert(j + 1, split_att_interval)
                                    att_work_intervals.append(current_att_interval)
                            reserved_intervals += att_work_intervals
                            pl_sign_in = get_float_from_time(pytz.utc.localize(work_interval[0]).astimezone(tz))
                            pl_sign_out = get_float_from_time(pytz.utc.localize(work_interval[1]).astimezone(tz))
                            ac_sign_in = 0
                            ac_sign_out = 0
                            status = "present"

                            if att_work_intervals:
                                if len(att_work_intervals) > 1:
                                    # print("there is more than one interval for that work interval")
                                    # late_in_interval = (work_interval[0], att_work_intervals[0][0])  # pl_in, actual_in
                                    late_in_interval = (work_interval[0], attendance_intervals[0][0].replace(second=0))  # pl_in, actual_in

                                    overtime_interval = (work_interval[1], att_work_intervals[-1][1])
                                    if overtime_interval[1] < overtime_interval[0]:
                                        overtime = timedelta(hours=0, minutes=0, seconds=0)
                                    else:
                                        overtime = overtime_interval[1] - overtime_interval[0]

                                    if late_in_interval[1] < late_in_interval[0]:  # actual_in > pl_in
                                        overtime += late_in_interval[0] - late_in_interval[1]

                                    remain_interval = (attendance_intervals[0][1], work_interval[1])
                                    for att_work_interval in attendance_intervals:
                                        worktime += att_work_interval[1] - att_work_interval[0]
                                        # actual_out <=
                                        if att_work_interval[1] <= remain_interval[0]:
                                            continue
                                        # actual_in <=
                                        if att_work_interval[0] >= remain_interval[1]:
                                            break
                                        if remain_interval[0] < att_work_interval[0] < remain_interval[1]:
                                            diff_intervals.append((remain_interval[0], att_work_interval[0]))
                                            remain_interval = (att_work_interval[1], remain_interval[1])
                                    if remain_interval and remain_interval[0] <= work_interval[1]:
                                        diff_intervals.append((remain_interval[0], work_interval[1]))

                                else:
                                    # late_in_interval = (work_interval[0], att_work_intervals[0][0])  # pl_in, actual_in
                                    late_in_interval = (work_interval[0], attendance_intervals[0][0].replace(second=0))  # pl_in, actual_in
                                    for att_work_interval in attendance_intervals:
                                        worktime += att_work_interval[1] - att_work_interval[0]
                                    # worktime += att_work_intervals[0][1] - att_work_intervals[0][0]

                                    # late out
                                    overtime_interval = (work_interval[1], att_work_intervals[-1][1])  # pl_out, actual_out
                                    if overtime_interval[1] < overtime_interval[0]:  # actual_out < pl_out
                                        overtime = timedelta(hours=0, minutes=0, seconds=0)
                                        diff_intervals.append((overtime_interval[1], overtime_interval[0]))  # actual_out < pl_out
                                    else:
                                        overtime = overtime_interval[1] - overtime_interval[0]

                                    # early in
                                    if late_in_interval[1] < late_in_interval[0]:  # actual_in > pl_in
                                        overtime += late_in_interval[0] - late_in_interval[1]
                            else:
                                late_in_interval = []
                                diff_intervals.append((work_interval[0], work_interval[1]))
                                if not attendance_intervals:
                                    status = "ab"
                                    status_text = 'Absence'

                            if diff_intervals:
                                for diff_in in diff_intervals:
                                    if leaves:
                                        status = "leave"
                                        diff_clean_intervals = interval_without_leaves(contract, diff_in, leaves)
                                        for diff_clean in diff_clean_intervals:
                                            diff_time += diff_clean[1] - diff_clean[0]
                                    else:
                                        diff_time += diff_in[1] - diff_in[0]

                            late_in_gap = timedelta(hours=0, minutes=0, seconds=0)
                            if late_in_interval:
                                # print "hey iam in late in " + str(late_in_interval)
                                if late_in_interval[1] < late_in_interval[0]:
                                    late_in = timedelta(hours=0, minutes=0, seconds=0)
                                else:
                                    if leaves:
                                        late_clean_intervals = interval_without_leaves(contract, late_in_interval, leaves)
                                        # print"lat after clean " + str(late_clean_intervals)
                                        for late_clean in late_clean_intervals:
                                            for l in leave_records:
                                                if l[2].holiday_status_id.name == "PERSONAL PERMISSION" and late_clean[0] == l[1]:
                                                    late_in_gap += late_clean[1] - late_clean[0]
                                            late_in += late_clean[1] - late_clean[0]
                                    else:
                                        late_in = late_in_interval[1] - late_in_interval[0]

                            float_late = late_in.total_seconds() / 3600.0
                            if late_in_gap:
                                late_in_time = late_in_gap.total_seconds() / 3600.0
                            else:
                                late_in_time = self.env['hr.attendance.policy'].get_late(policy_id, float_late)

                            if attendance_intervals:
                                ac_sign_in = get_float_from_time(pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz))
                                ac_sign_out = get_float_from_time(pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz))

                                # check for early out
                                if contract.apply_early_out and pl_sign_out > ac_sign_out > pl_sign_in:
                                    early_out_intervals = (attendance_intervals[-1][1].replace(second=0), work_interval[1])
                                    if leaves:
                                        early_out_intervals = interval_without_leaves(contract, early_out_intervals, leaves)
                                        for early_clean in early_out_intervals:
                                            early_out += early_clean[1] - early_clean[0]
                                        float_early_out = early_out.total_seconds() / 3600.0
                                    else:
                                        float_early_out = pl_sign_out - ac_sign_out

                            # check time worked outside interval
                            out_work_intervals = [x for x in attendance_intervals if x not in reserved_intervals]
                            if out_work_intervals:
                                for att_out in out_work_intervals:
                                    # print "out interval" + str(att_out)
                                    overtime += att_out[1] - att_out[0]

                            float_worktime = worktime.total_seconds() / 3600.0

                            if contract.timing_policy in ['any', 'both']:
                                if contract.timing_policy == 'any':
                                    if ac_sign_in or ac_sign_out:
                                        float_worktime = expected_work_hours
                                        worktime = timedelta(hours=expected_work_hours)
                                elif contract.timing_policy == 'both':
                                    float_worktime = ac_sign_out - ac_sign_in
                                    worktime = timedelta(hours=float_worktime)
                                elif contract.timing_policy == 'normal':
                                    if float_worktime <= 0.0 and status == "":
                                        missing_check_io = True

                            # validate overtime hours
                            shift_worktime = worktime - overtime
                            if shift_worktime < timedelta(hours=expected_work_hours):
                                work_diff = timedelta(hours=expected_work_hours) - shift_worktime
                                overtime = overtime - work_diff

                            float_overtime = overtime.total_seconds() / 3600.0
                            if float_overtime <= overtime_policy['wd_after']:
                                float_overtime = 0
                            else:
                                float_overtime = float_overtime * overtime_policy['wd_rate']

                            values = {}

                            for l in leave_records:
                                status = 'leave'
                                if l[2].holiday_status_id.is_short_leave:
                                    # TODO: check what does it do with include_leaves
                                    number_of_hours = l[2].with_context(include_leaves=False)._get_duration()[1]
                                    slh_time += number_of_hours
                                status_text = l[2].holiday_status_id.name
                                note_mgr = l[2].report_note

                            values.update({
                                'date': from_date,
                                'employee_id': emp.id,
                                'pl_sign_in': pl_sign_in,
                                'pl_sign_out': pl_sign_out,
                                'rwt': expected_work_hours,
                                'ac_sign_in': ac_sign_in,
                                'ac_sign_out': ac_sign_out,
                                'late_in': late_in_time,
                                'early_out': float_early_out,
                                'slh_time': slh_time,
                                'work_time': float_worktime,
                                'overtime': float_overtime,
                                'status': status,
                                'status_text': status_text,
                                'adjustment_id': sheet.id,
                                'related_att_ids': [(6, 0, [att.id for att in att_ids])],
                                'contract_id': contract.id,
                                'working_hours_id': calender_id.id,
                                'note1': note_mgr,
                                'note2': missing_check_io and "Check In/Out missing" or "",
                                'missing_check_io': missing_check_io
                            })
                            sheet_lines.append((0, 0, values))
                else:
                    # WEEKEND
                    values = {}
                    status = 'weekend'
                    status_text = 'Weekend'
                    for l in leave_records:
                        status = 'leave'
                        status_text = l[2].holiday_status_id.name
                        note_mgr = l[2].report_note

                    if attendance_intervals:
                        for attendance_interval in attendance_intervals:
                            worktime += attendance_interval[1] - attendance_interval[0]

                        ac_sign_in = pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz)
                        ac_sign_out = pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz)
                        float_worktime = worktime.total_seconds() / 3600.0

                        if float_worktime <= overtime_policy['wd_after']:
                            float_overtime = 0
                        else:
                            float_overtime = float_worktime * overtime_policy['wd_rate']

                        values.update({
                            'date': from_date,
                            'employee_id': emp.id,
                            'ac_sign_in': get_float_from_time(ac_sign_in),
                            'ac_sign_out': get_float_from_time(ac_sign_out),
                            'work_time': float_worktime,
                            'overtime': float_overtime,
                            'adjustment_id': sheet.id,
                            'status': status,
                            'status_text': status_text,
                            'note1': note_mgr,
                            'related_att_ids': [(6, 0, [att.id for att in att_ids])],
                            'contract_id': contract.id,
                            'working_hours_id': calender_id.id
                        })
                        sheet_lines.append((0, 0, values))
                    else:
                        values.update({
                            'date': from_date,
                            'employee_id': emp.id,
                            'adjustment_id': sheet.id,
                            'status': status,
                            'status_text': status_text,
                            'note1': note_mgr,
                            'contract_id': contract.id,
                            'working_hours_id': calender_id.id
                        })
                        sheet_lines.append((0, 0, values))

            sheet.sudo().line_ids = sheet_lines

    def button_refresh_att(self):
        self.ensure_one()
        for line in self.line_ids:
            line.button_refresh_att()

    def button_send_back(self):
        self.ensure_one()
        view_id = self.env.ref('tawjeeh_payroll.view_hr_attendance_adjustment_send_back').id
        return {
            'name': 'Reason For Sending Back',
            'res_model': 'hr.attendance.adjustment.send_back',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'views': [(view_id, 'form')],
            'view_id': view_id,
            'target': 'new',
        }

    @api.model
    def attendance_cron(self, company_ids=False, att_date=False):
        sheet_obj = self.env['hr.attendance.adjustment']
        dep_domain = [('manager_id', '!=', False), ('company_attendance_id', '!=', False), ('exclude_attendance', '=', False)]
        if company_ids:
            dep_domain.append(('company_attendance_id', 'in', company_ids))

        departments = self.env['hr.department'].search(dep_domain)
        if att_date:
            batch_date = fields.Date.from_string(att_date)
        else:
            yesterday_local = convert_date_to_local(datetime.today(), 'Asia/Dubai') - timedelta(days=1)
            batch_date = yesterday_local.date()

        for dep in departments:
            sheet = sheet_obj.search([('date_from', '=', batch_date), ('department_id', '=', dep.id)])
            if not sheet:
                sheet = sheet_obj.create({
                    'department_id': dep.id,
                    'date_from': batch_date,
                    'company_id': dep.company_attendance_id.id})
                sheet.get_attendances()
                sheet.with_context(skip_probation_check=True, skip_jd_check=True).button_confirm()


class HRAttendanceAdjustmentLine(models.Model):
    _name = 'hr.attendance.adjustment.line'
    _description = "HR Attendance Adjustment Line"

    @api.depends('rwt', 'slh_time', 'status', 'work_time', 'time_plus', 'time_minus', 'late_in')
    def _compute_need_fix(self):
        for line in self:
            line.to_fix = False
            if line.status == 'leave' and not line.slh_time:
                continue
            expected_work_hours = line.rwt

            work_time = line.work_time + line.slh_time + line.time_plus - line.time_minus
            if work_time > 23.99:
                work_time = 23.99
            line.total_work_time = work_time
            work_time = line._get_time_from_float(work_time)
            work_hour = line._get_time_from_float(expected_work_hours)

            if work_time < work_hour:
                line.to_fix = True
            if (line.sudo().contract_id.apply_time_late_rule or line.sudo().contract_id.apply_late_fq_rule) and line.late_in:
                line.to_fix = True

    @api.depends('adjustment_id')
    def get_line_number(self):
        for att in self.mapped('adjustment_id'):
            number = 1
            for line in att.line_ids:
                line.line_number = number
                number += 1

    line_number = fields.Integer(string='S.No.', compute='get_line_number', store=True)
    adjustment_id = fields.Many2one('hr.attendance.adjustment', string='Reference', required=True, ondelete='cascade', index=True, copy=False)
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, index=True)
    identification_id = fields.Char(string='Employee ID', related='employee_id.identification_id', readonly=True)
    date = fields.Date("Date", index=True)
    pl_sign_in = fields.Float("Shift In", readonly=True)
    pl_sign_out = fields.Float("Shift Out", readonly=True)
    ac_sign_in = fields.Float("Sign In", readonly=True)
    ac_sign_out = fields.Float("Sign Out", readonly=True)
    rwt = fields.Float("RWT", readonly=True)
    late_in = fields.Float("Late In", readonly=True)
    early_out = fields.Float("Early Out", readonly=True)
    slh_time = fields.Float("SLH", readonly=True)
    work_time = fields.Float("WT", readonly=True)
    time_plus = fields.Float('H+', readonly=True, help="HH:MM")
    time_minus = fields.Float('H-', readonly=True, help="HH:MM")
    total_work_time = fields.Float(compute='_compute_need_fix', string="Total WT", store=True)
    overtime = fields.Float("SOT", readonly=True)
    overtime_actual = fields.Float("AOT", readonly=True)
    note1 = fields.Text("Note by Mgr", readonly=True)
    note2 = fields.Text("Note by HR", readonly=True)
    contract_id = fields.Many2one('hr.contract', string='Contract', ondelete='restrict', readonly=True, index=True)
    working_hours_id = fields.Many2one('resource.calendar', 'Working Schedule', index=True)
    status = fields.Selection(selection=[('ab', 'Absence'), ('weekend', 'Weekend'), ('ph', 'Public Holiday'),
                                         ('leave', 'Leave'), ('present', 'Present')],
                              string="Status", required=False, readonly=True)
    status_text = fields.Text(string="Status", readonly=True)
    company_id = fields.Many2one(related='adjustment_id.company_id', string='Company', store=True, readonly=True)
    to_fix = fields.Boolean(compute='_compute_need_fix', store=True)
    related_att_ids = fields.Many2many('biometric.attendance', 'att_adjustment_sheet_biometric_rel', 'sheet_line_id',
                                       'att_id', 'Related Attendances', readonly=True)
    late_history_ids = fields.Many2many('hr.late.frequency.history', compute='get_late_history')
    late_history_time_ids = fields.Many2many('hr.late.time.history', compute='get_late_history')
    state = fields.Selection(related='adjustment_id.state', string='adjustment Status', readonly=True, copy=False,
                             store=True, index=True)
    is_approved = fields.Boolean("Approved?", copy=False)
    missing_check_io = fields.Boolean("Missing check in/out")

    def _get_time_from_float(self, float_time):
        self.ensure_one
        try:
            str_off_time = get_time_from_float(float_time)
            str_off_time = datetime.strptime(str_off_time, "%H:%M").time()
        except ValueError:
            raise ValidationError('Invalid time %s for employee %s!' % (str_off_time, self.employee_id.name_get()[0][1]))
        return str_off_time

    def get_late_history(self):
        for line in self:
            sheet_date = fields.Date.from_string(line.date)
            payroll_start, payroll_end = get_payroll_period(sheet_date, line.company_id)
            history_ids = self.env['hr.late.frequency.history'].search([('employee_id', '=', line.employee_id.id), ('late_date', '>=', payroll_start), ('late_date', '<=', payroll_end)])
            history_time_ids = self.env['hr.late.time.history'].search([('employee_id', '=', line.employee_id.id), ('late_date', '>=', payroll_start), ('late_date', '<=', payroll_end)])
            line.late_history_ids = history_ids.ids
            line.late_history_time_ids = history_time_ids.ids

    def make_late_in_entry(self):
        for line in self:
            if line.late_in:
                if line.sudo().contract_id.apply_late_fq_rule:
                    sheet_date = fields.Date.from_string(line.date)
                    payroll_start, payroll_end = get_payroll_period(sheet_date, line.company_id)
                    last_late_frequency = line.find_last_late_frequency(payroll_start, payroll_end)
                    if last_late_frequency:
                        # check date
                        if payroll_start <= sheet_date <= payroll_end:
                            if last_late_frequency == 'f1':
                                line.create_late_in_entry('f2')
                            elif last_late_frequency == 'f2':
                                line.create_late_in_entry('f3')
                            elif last_late_frequency == 'f3':
                                line.create_late_in_entry('f4')
                            elif last_late_frequency == 'f4':
                                line.create_late_in_entry('f5')
                            elif last_late_frequency == 'f5':
                                line.create_late_in_entry('f5')
                        else:
                            line.create_late_in_entry('f1')
                    else:
                        line.create_late_in_entry('f1')
                elif line.sudo().contract_id.apply_time_late_rule:
                    self.env['hr.late.time.history'].create({
                        'late_date': line.date,
                        'pl_sign_in': line.pl_sign_in,
                        'ac_sign_in': line.ac_sign_in,
                        'rwt': line.rwt,
                        'late_in': line.late_in,
                        'employee_id': line.employee_id.id,
                        'adjustment_line_id': line.id,
                        'company_id': line.company_id.id
                    })

    def find_last_late_frequency(self, payroll_start, payroll_end):
        history_obj = self.env['hr.late.frequency.history']

        late_conf = False
        late_confs = self.env['hr.late.conf'].search([('late_id', '=', self.contract_id.late_conf_id.id)])
        for conf in late_confs:
            if conf.time_start <= round(self.late_in, 2) <= conf.time_end:
                late_conf = conf
                break
            else:
                diff = abs(self.late_in - conf.time_start)
                if diff <= 0.01 and round(self.late_in, 2) <= conf.time_end:
                    late_conf = conf
                    break
        if late_conf:
            history = history_obj.search(
                [('employee_id', '=', self.employee_id.id), ('time_start', '=', late_conf.time_start),
                 ('time_end', '=', late_conf.time_end),
                 ('late_date', '>=', payroll_start),
                 ('late_date', '<=', payroll_end)], order='late_date desc')
            if history:
                return history[0].frequency
        return False

    def create_late_in_entry(self, frequency):
        late_conf = False
        late_confs = self.env['hr.late.conf'].search(
            [('frequency', '=', frequency), ('late_id', '=', self.contract_id.late_conf_id.id)])
        for conf in late_confs:
            if conf.time_start <= round(self.late_in, 2) <= conf.time_end:
                late_conf = conf
                break
            else:
                diff = abs(self.late_in - conf.time_start)
                if diff <= 0.01 and round(self.late_in, 2) <= conf.time_end:
                    late_conf = conf
                    break
        if late_conf:
            self.env['hr.late.frequency.history'].create({
                'late_date': self.date,
                'frequency': frequency,
                'time_start': late_conf.time_start,
                'time_end': late_conf.time_end,
                'employee_id': self.employee_id.id,
                'adjustment_line_id': self.id,
                'company_id': self.company_id.id
            })

    def remove_late_in_entry(self):
        for line in self:
            history = self.env['hr.late.frequency.history'].search(
                [('employee_id', '=', line.employee_id.id), ('late_date', '=', line.date)])
            if history:
                history.unlink()

            history = self.env['hr.late.time.history'].search(
                [('employee_id', '=', line.employee_id.id), ('late_date', '=', line.date)])
            if history:
                history.unlink()

    def action_att_data_change(self):
        view_id = self.env.ref('tawjeeh_payroll.att_data_change_from_view').id
        return {
            'name': 'Change Hours',
            'res_model': 'attendance.adjustment.line.change',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'views': [(view_id, 'form')],
            'view_id': view_id,
            'target': 'new',
        }

    def action_approve(self):
        if self.state == 'to_hr' and self.user_has_groups('!hr.group_hr_manager'):
            raise UserError("Only HR can approve at this stage.")
        self.write({'is_approved': True})

    def button_refresh_att(self):
        self.ensure_one()
        from_date = datetime.combine(self.date, datetime.min.time())
        # find all department employees
        contract_ids = self.employee_id.sudo()._get_contracts(from_date, from_date, states=['open', 'close'])
        values = {
            'ac_sign_in': 0,
            'ac_sign_out': 0,
            'work_time': 0,
            'overtime': 0,
            'status': '',
            'status_text': '',
            'is_approved': False
        }
        if not contract_ids:
            values['note2'] = 'Contract not found'
            if 'refresh_work_time_only' not in self.env.context:
                self.write(values)
            return

        contract = contract_ids[0]
        if contract.resource_calendar_id:
            calendar_id = contract.resource_calendar_id
        else:
            values['contract_id'] = contract.id
            values['note2'] = 'Working hours not found'
            if 'refresh_work_time_only' not in self.env.context:
                self.write(values)
            return

        if contract.att_policy_id:
            policy_id = contract.att_policy_id
        else:
            values['contract_id'] = contract.id
            values['note2'] = 'Attendance policy not found'
            if 'refresh_work_time_only' not in self.env.context:
                self.write(values)
            return

        late_in = timedelta(hours=00, minutes=00, seconds=00)
        worktime = timedelta(hours=00, minutes=00, seconds=00)
        reserved_intervals = []

        day_start = from_date
        day_end = fields.Datetime.to_datetime(day_start).replace(hour=23, minute=59, second=59, microsecond=999999)

        def interval_without_leaves(contract_id, interval, leave_intervals):
            intervals = contract_id.sudo().resource_calendar_id.interval_remove_leaves(interval, leave_intervals)
            return intervals

        Adjustment = self.env['hr.attendance.adjustment']
        public_holiday = self.env['hr.public.holiday'].get_public_holiday(from_date, self.adjustment_id.department_id)
        work_intervals = calendar_id.get_work_intervals(day_start, day_end)
        attendance_intervals, att_ids = self.env['biometric.attendance'].get_attendance_intervals(self.employee_id, day_start, day_end)
        leave_records = Adjustment._get_emp_leaves(self.employee_id, day_start, day_end)
        leaves = [(l[0], l[1]) for l in leave_records]
        overtime_policy = self.env['hr.attendance.policy'].get_overtime(policy_id)
        expected_work_hours, overtime_after = calendar_id.get_work_hours(day_start, day_end)
        slh_time = 0
        status_text = ''
        note_mgr = ""
        missing_check_io = False

        if not self.employee_id.tz:
            raise ValidationError("Please add time zone for the employee %s" % self.employee_id.name)
        else:
            tz = pytz.timezone(self.employee_id.tz)

        if work_intervals:
            if public_holiday:
                status = 'ph'
                status_text = 'Public Holiday'
                for l in leave_records:
                    status = 'leave'
                    status_text = l[2].holiday_status_id.name
                    note_mgr = l[2].sudo().report_note

                if attendance_intervals:
                    for attendance_interval in attendance_intervals:
                        worktime += attendance_interval[1] - attendance_interval[0]
                    float_worktime = worktime.total_seconds() / 3600.0

                    if float_worktime <= overtime_policy['ph_after']:
                        float_overtime = 0
                    else:
                        float_overtime = float_worktime * overtime_policy['ph_rate']

                    ac_sign_in = pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz)
                    ac_sign_out = pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz)

                    values['ac_sign_in'] = get_float_from_time(ac_sign_in)
                    values['ac_sign_out'] = get_float_from_time(ac_sign_out)
                    values['work_time'] = float_worktime
                    values['overtime'] = float_overtime
                    values['time_plus'] = 0
                    values['time_minus'] = 0
                    values['status'] = status
                    values['status_text'] = status_text
                    if not self.note1:
                        values['note1'] = note_mgr
                    if not self.note2:
                        values['note2'] = ''
                    values['contract_id'] = contract.id
                    values['working_hours_id'] = calendar_id.id
                    values['related_att_ids'] = [(6, 0, [att.id for att in att_ids])]
                    if 'refresh_work_time_only' not in self.env.context:
                        self.write(values)
                    else:
                        self.write({'work_time': float_worktime})
                else:
                    values['status'] = status
                    values['status_text'] = status_text
                    if not self.note1:
                        values['note1'] = note_mgr
                    if not self.note2:
                        values['note2'] = ''
                    values['contract_id'] = contract.id
                    values['working_hours_id'] = calendar_id.id
                    if 'refresh_work_time_only' not in self.env.context:
                        self.write(values)
            else:
                for i, work_interval in enumerate(work_intervals):
                    att_work_intervals = []
                    diff_intervals = []
                    late_in_interval = []
                    diff_time = timedelta(hours=00, minutes=00, seconds=00)
                    late_in = timedelta(hours=00, minutes=00, seconds=00)
                    early_out = timedelta(hours=00, minutes=00, seconds=00)
                    overtime = timedelta(hours=00, minutes=00, seconds=00)
                    worktime = timedelta(hours=00, minutes=00, seconds=00)
                    float_early_out = 0.0

                    for j, att_interval in enumerate(attendance_intervals):
                        # max(pl_in, actual_in) < min(pl_out, actual_out)
                        if max(work_interval[0], att_interval[0]) < min(work_interval[1], att_interval[1]):
                            current_att_interval = att_interval
                            if i + 1 < len(work_intervals):
                                next_work_interval = work_intervals[i + 1]
                                if max(next_work_interval[0], current_att_interval[0]) < min(next_work_interval[1],
                                                                                             current_att_interval[1]):
                                    split_att_interval = (next_work_interval[0], current_att_interval[1])
                                    current_att_interval = (current_att_interval[0], next_work_interval[0])
                                    attendance_intervals[j] = current_att_interval
                                    attendance_intervals.insert(j + 1, split_att_interval)
                            att_work_intervals.append(current_att_interval)
                    reserved_intervals += att_work_intervals
                    pl_sign_in = get_float_from_time(pytz.utc.localize(work_interval[0]).astimezone(tz))
                    pl_sign_out = get_float_from_time(pytz.utc.localize(work_interval[1]).astimezone(tz))
                    ac_sign_in = 0
                    ac_sign_out = 0
                    status = 'present'

                    if att_work_intervals:
                        if len(att_work_intervals) > 1:
                            # print("there is more than one interval for that work interval")
                            # late_in_interval = (work_interval[0], att_work_intervals[0][0])  # pl_in, actual_in
                            late_in_interval = (
                            work_interval[0], attendance_intervals[0][0].replace(second=0))  # pl_in, actual_in

                            overtime_interval = (work_interval[1], att_work_intervals[-1][1])
                            if overtime_interval[1] < overtime_interval[0]:
                                overtime = timedelta(hours=0, minutes=0, seconds=0)
                            else:
                                overtime = overtime_interval[1] - overtime_interval[0]

                            if late_in_interval[1] < late_in_interval[0]:  # actual_in < pl_in
                                overtime += late_in_interval[0] - late_in_interval[1]

                            remain_interval = (attendance_intervals[0][1], work_interval[1])
                            for att_work_interval in attendance_intervals:
                                worktime += att_work_interval[1] - att_work_interval[0]
                                # actual_out <=
                                if att_work_interval[1] <= remain_interval[0]:
                                    continue
                                # actual_in <=
                                if att_work_interval[0] >= remain_interval[1]:
                                    break
                                if remain_interval[0] < att_work_interval[0] < remain_interval[1]:
                                    diff_intervals.append((remain_interval[0], att_work_interval[0]))
                                    remain_interval = (att_work_interval[1], remain_interval[1])
                            if remain_interval and remain_interval[0] <= work_interval[1]:
                                diff_intervals.append((remain_interval[0], work_interval[1]))

                        else:
                            # late_in_interval = (work_interval[0], att_work_intervals[0][0])  # pl_in, actual_in
                            late_in_interval = (
                            work_interval[0], attendance_intervals[0][0].replace(second=0))  # pl_in, actual_in
                            for att_work_interval in attendance_intervals:
                                worktime += att_work_interval[1] - att_work_interval[0]
                            # worktime += att_work_intervals[0][1] - att_work_intervals[0][0]

                            # late out
                            overtime_interval = (work_interval[1], att_work_intervals[-1][1])  # pl_out, actual_out
                            if overtime_interval[1] < overtime_interval[0]:  # actual_out < pl_out
                                overtime = timedelta(hours=0, minutes=0, seconds=0)
                                diff_intervals.append((overtime_interval[1], overtime_interval[0]))  # actual_out < pl_out
                            else:
                                overtime = overtime_interval[1] - overtime_interval[0]

                            # early in
                            if late_in_interval[1] < late_in_interval[0]:  # actual_in > pl_in
                                overtime += late_in_interval[0] - late_in_interval[1]
                    else:
                        late_in_interval = []
                        diff_intervals.append((work_interval[0], work_interval[1]))
                        if not attendance_intervals:
                            status = "ab"
                            status_text = 'Absence'

                    if diff_intervals:
                        for diff_in in diff_intervals:
                            if leaves:
                                status = "leave"
                                diff_clean_intervals = interval_without_leaves(contract, diff_in, leaves)
                                for diff_clean in diff_clean_intervals:
                                    diff_time += diff_clean[1] - diff_clean[0]
                            else:
                                diff_time += diff_in[1] - diff_in[0]

                    late_in_gap = timedelta(hours=0, minutes=0, seconds=0)
                    if late_in_interval:
                        # print "hey iam in late in " + str(late_in_interval)
                        if late_in_interval[1] < late_in_interval[0]:
                            late_in = timedelta(hours=0, minutes=0, seconds=0)
                        else:
                            if leaves:
                                late_clean_intervals = interval_without_leaves(contract, late_in_interval, leaves)
                                # print"lat after clean " + str(late_clean_intervals)
                                for late_clean in late_clean_intervals:
                                    for l in leave_records:
                                        if l[2].holiday_status_id.name == "PERSONAL PERMISSION" and late_clean[0] == l[1]:
                                            late_in_gap += late_clean[1] - late_clean[0]
                                    late_in += late_clean[1] - late_clean[0]
                            else:
                                late_in = late_in_interval[1] - late_in_interval[0]

                    float_late = late_in.total_seconds() / 3600.0
                    if late_in_gap:
                        late_in_time = late_in_gap.total_seconds() / 3600.0
                    else:
                        late_in_time = self.env['hr.attendance.policy'].get_late(policy_id, float_late)

                    if attendance_intervals:
                        ac_sign_in = get_float_from_time(pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz))
                        ac_sign_out = get_float_from_time(pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz))

                        # check for early out
                        if contract.apply_early_out and pl_sign_out > ac_sign_out > pl_sign_in:
                            early_out_intervals = (attendance_intervals[-1][1].replace(second=0), work_interval[1])
                            if leaves:
                                early_out_intervals = interval_without_leaves(contract, early_out_intervals, leaves)
                                for early_clean in early_out_intervals:
                                    early_out += early_clean[1] - early_clean[0]
                                float_early_out = early_out.total_seconds() / 3600.0
                            else:
                                float_early_out = pl_sign_out - ac_sign_out

                    # check time worked outside interval
                    out_work_intervals = [x for x in attendance_intervals if x not in reserved_intervals]
                    if out_work_intervals:
                        for att_out in out_work_intervals:
                            # print "out interval" + str(att_out)
                            overtime += att_out[1] - att_out[0]

                    float_worktime = worktime.total_seconds() / 3600.0

                    if contract.timing_policy in ['any', 'both']:
                        if contract.timing_policy == 'any':
                            if ac_sign_in or ac_sign_out:
                                float_worktime = expected_work_hours
                                worktime = timedelta(hours=expected_work_hours)
                        elif contract.timing_policy == 'both':
                            float_worktime = ac_sign_out - ac_sign_in
                            worktime = timedelta(hours=float_worktime)
                        elif contract.timing_policy == 'normal':
                            if float_worktime <= 0.0 and status == "":
                                missing_check_io = True

                    # validate overtime hours
                    shift_worktime = worktime - overtime
                    if shift_worktime < timedelta(hours=expected_work_hours):
                        work_diff = timedelta(hours=expected_work_hours) - shift_worktime
                        overtime = overtime - work_diff

                    float_overtime = overtime.total_seconds() / 3600.0
                    if float_overtime <= overtime_policy['wd_after']:
                        float_overtime = 0
                    else:
                        float_overtime = float_overtime * overtime_policy['wd_rate']

                    for l in leave_records:
                        status = 'leave'
                        if l[2].holiday_status_id.is_short_leave:
                            number_of_hours = l[2].with_context(include_leaves=False)._get_duration()[1]
                            slh_time += number_of_hours
                        status_text = l[2].holiday_status_id.name
                        note_mgr = l[2].sudo().report_note

                    values['pl_sign_in'] = pl_sign_in
                    values['pl_sign_out'] = pl_sign_out
                    values['rwt'] = expected_work_hours
                    values['ac_sign_in'] = ac_sign_in
                    values['ac_sign_out'] = ac_sign_out
                    values['late_in'] = late_in_time
                    values['early_out'] = float_early_out
                    values['slh_time'] = slh_time
                    values['work_time'] = float_worktime
                    values['overtime'] = float_overtime
                    values['time_plus'] = 0
                    values['time_minus'] = 0
                    values['status'] = status
                    values['status_text'] = status_text
                    values['related_att_ids'] = [(6, 0, [att.id for att in att_ids])]
                    values['contract_id'] = contract.id
                    values['working_hours_id'] = calendar_id.id
                    if not self.note1:
                        values['note1'] = note_mgr
                    if not self.note2:
                        values['note2'] = missing_check_io and "Check In/Out missing" or ""
                    if 'refresh_work_time_only' not in self.env.context:
                        self.write(values)
                    else:
                        self.write({'work_time': float_worktime})
        else:
            # WEEKEND
            status = 'weekend'
            status_text = 'Weekend'
            for l in leave_records:
                status = 'leave'
                status_text = l[2].holiday_status_id.name
                note_mgr = l[2].sudo().report_note

            if attendance_intervals:
                for attendance_interval in attendance_intervals:
                    worktime += attendance_interval[1] - attendance_interval[0]

                ac_sign_in = pytz.utc.localize(attendance_intervals[0][0]).astimezone(tz)
                ac_sign_out = pytz.utc.localize(attendance_intervals[-1][1]).astimezone(tz)
                float_worktime = worktime.total_seconds() / 3600.0

                if float_worktime <= overtime_policy['wd_after']:
                    float_overtime = 0
                else:
                    float_overtime = float_worktime * overtime_policy['wd_rate']

                values['ac_sign_in'] = get_float_from_time(ac_sign_in)
                values['ac_sign_out'] = get_float_from_time(ac_sign_out)
                values['work_time'] = float_worktime
                values['overtime'] = float_overtime
                values['time_plus'] = 0
                values['time_minus'] = 0
                values['status'] = status
                values['status_text'] = status_text
                if not self.note1:
                    values['note1'] = note_mgr
                if not self.note2:
                    values['note2'] = ''
                values['related_att_ids'] = [(6, 0, [att.id for att in att_ids])]
                values['contract_id'] = contract.id
                values['working_hours_id'] = calendar_id.id
                if 'refresh_work_time_only' not in self.env.context:
                    self.write(values)
                else:
                    self.write({'work_time': float_worktime})
            else:
                values['status'] = status
                values['status_text'] = status_text
                if not self.note1:
                    values['note1'] = note_mgr
                if not self.note2:
                    values['note2'] = ''
                values['contract_id'] = contract.id
                values['working_hours_id'] = calendar_id.id
                if 'refresh_work_time_only' not in self.env.context:
                    self.write(values)

    def refresh_work_time(self):
        for line in self:
            if line.work_time <= 0.0:
                line.with_context(refresh_work_time_only=True).button_refresh_att()
