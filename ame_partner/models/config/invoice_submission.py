from odoo import fields, models, api


class InvoiceSubmissionType(models.Model):
    _name = 'invoice.submission.type'
    _description = 'Invoice Submission Type'
    _inherit = 'mail.thread'

    name = fields.Char(string="Name", required=True, copy=False, index=True, tracking=True)
    company_id = fields.Many2one("res.company", string="Company", default=lambda self: self.env.company, readonly=True, required=True, tracking=True, index=True)

