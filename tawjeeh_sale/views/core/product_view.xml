<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="product_template_search_view_inherit" model="ir.ui.view">
            <field name="name">product.template.search.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <filter name="consumable" position="after">
                    <filter name="center_service" string="Center Service" domain="[('is_center_service','=',True)]"/>
                </filter>
                <group position="inside">
                    <filter name="trans_type_groupby" string="Transaction Type" context="{'group_by':'service_transaction_type_id'}"/>
                </group>
            </field>
        </record>

        <record id="view_expense_account_custom_form" model="ir.ui.view">
            <field name="name">product.category.custom.inherit</field>
            <field name="model">product.category</field>
            <field name="inherit_id" ref="account.view_category_property_form"/>
            <field name="arch" type="xml">
                <field name="property_account_expense_categ_id" position="after">
                    <field name="expense_comm_id" string="Local Staff Commision Expense Account"/>
                    <field name="expense_acc3_id" string="Local Staff Commision Accrual"/>
                    <field name="expense_pro_accural_id"/>
                    <field name="expense_pro_comm_id"/>
                    <field name="payback_account_id"/>
                </field>
                <field name="parent_id" position="after">
                    <field name="exclude_typing_fee"/>
                    <field name="company_id" required="1"/>
                </field>
                <group name="account_property" position="after">
                    <group>
                        <group string="General">
                            <field name="auto_close_service_invoice"/>
                            <field name="closing_typist_id" required="auto_close_service_invoice"/>
                        </group>
                    </group>
                </group>
            </field>
        </record>

        <menuitem name="Servicing Analysis" action="sale.action_order_report_all" id="sale.menu_reporting_sales" parent="sale.menu_sale_report" sequence="10"/>

        <record id="view_expense_product_custom_form" model="ir.ui.view">
            <field name="name">product.template.custom.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='options']" position="inside">
                    <span class="d-inline-block">
                        <field name="is_credit_card_charge"/>
                        <label for="is_credit_card_charge"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_center_service"/>
                        <label for="is_center_service"/>
                    </span>
                    <span class="d-inline-block" invisible="not is_center_service">
                        <field name="need_wallet_govfee"/>
                        <label for="need_wallet_govfee"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="need_trans_no"/>
                        <label for="need_trans_no"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="exclude_tax_govfee"/>
                        <label for="exclude_tax_govfee"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="exclude_tax_noqodi"/>
                        <label for="exclude_tax_noqodi"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="charge_only_typing_fee"/>
                        <label for="charge_only_typing_fee"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_dxb_insurance_service"/>
                        <label for="is_dxb_insurance_service"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_g2g3_service"/>
                        <label for="is_g2g3_service"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_dxb_insurance_service_420"/>
                        <label for="is_dxb_insurance_service_420"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_dxb_insurance_service_315"/>
                        <label for="is_dxb_insurance_service_315"/>
                    </span>
                    <span class="d-inline-block">
                        <field name="is_dxb_insurance_service_105"/>
                        <label for="is_dxb_insurance_service_105"/>
                    </span>
                </xpath>

                <page name="general_information" position="after">
                    <page string="Tawjeeh" name="taw_jeeh">
                        <group>
                            <group>
                                <field name="transaction_count"/>
                                <field name="fees" string="GOV.Fees"/>
                                <field name="typing_fees" string="Typing Fees"/>
                                <field name="additional_charges"/>
                                <field name="staff_comm2"/>
                                <field name="staff_comm"/>
                                <field name="g2g3_charge" readonly="not is_g2g3_service" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                                <field name="additional_typing_fees"/>
                                <field name="cogs_amount"/>
                                <field name="allow_amount_change"/>
                                <field name="eligible_for_bonus"/>
                            </group>
                            <group>
                                <field name="eligible_for_manager_comm"/>
                                <field name="eligible_for_supervisor_comm"/>
                                <field name="hide_from_invoice"/>
                                <field name="is_not_allow_manual"/>
                                <field name="credit_card_percentage"/>
                                <field name="cc_charge_account_id" options="{'no_create': True}" groups="base.group_erp_manager"/>
                            </group>
                        </group>
                        <separator string="Discount History"/>
                        <field name="discount_line_ids" nolabel="1">
                            <tree class="line-number" editable="bottom">
                                <field name="line_number" class="line-number"/>
                                <field name="product_tmpl_id" column_invisible="1"/>
                                <field name="discount" required="1"/>
                                <field name="date_from" required="1"/>
                                <field name="date_to" required="1"/>
                            </tree>
                            <form>
                                <group>
                                    <group>
                                        <field name="product_tmpl_id" invisible="1"/>
                                        <field name="discount"/>
                                        <field name="date_from"/>
                                        <field name="date_to"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                </page>
                <field name="categ_id" position="after">
                    <field name="service_transaction_type_id"/>
                </field>
            </field>
        </record>

        <record id="view_product_template_form_inherit" model="ir.ui.view">
            <field name="name">Product Template Accounts</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="account.product_template_form_view"/>
            <field name="arch" type="xml">
                <field name="property_account_income_id" position="replace">
                    <field name="property_account_income_id" domain="[('deprecated','=',False)]" groups="account.group_account_user"/>
                    <field name="rel_property_stock_account_output_categ_id" string="Sales Balancing Account"/>
                </field>
                <field name="property_account_expense_id" position="replace">
                    <field name="property_account_expense_id" domain="[('deprecated','=',False)]" groups="account.group_account_user"/>
                    <field name="rel_expense_comm_id"/>
                    <field name="rel_expense_acc3_id"/>
                </field>
            </field>
        </record>

        <record id="product_template_tree_view" model="ir.ui.view">
            <field name="name">product.template.tree</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <field name="default_code" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="list_price" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="standard_price" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="uom_id" position="after">
                    <field name="fees"/>
                    <field name="additional_charges"/>
                    <field name="g2g3_charge"/>
                    <field name="typing_fees"/>
                    <field name="cogs_amount"/>
                    <field name="staff_comm2"/>
                    <field name="staff_comm"/>
                    <field name="property_account_income_id"/>
                    <field name="property_account_expense_id"/>
                    <field name="rel_expense_comm_id"/>
                    <field name="rel_expense_acc3_id"/>
                    <field name="rel_property_stock_account_output_categ_id" string="Sales Balancing Account"/>
                </field>
                <field name="categ_id" position="after">
                    <field name="service_transaction_type_id"/>
                </field>
            </field>
        </record>

        <!--        <record id="view_stock_product_template_tree_taw" model="ir.ui.view">-->
        <!--            <field name="name">product.template.stock.tree.inherit.taw</field>-->
        <!--            <field name="model">product.template</field>-->
        <!--            <field name="inherit_id" ref="stock.view_stock_product_template_tree"/>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <tree position="attributes">-->
        <!--                    <attribute name="decoration-danger">False</attribute>-->
        <!--                    <attribute name="decoration-info">False</attribute>-->
        <!--                </tree>-->
        <!--                <field name="qty_available" position="attributes">-->
        <!--                    <attribute name="invisible">True</attribute>-->
        <!--                </field>-->
        <!--                <field name="virtual_available" position="attributes">-->
        <!--                    <attribute name="invisible">True</attribute>-->
        <!--                </field>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <record id="product_template_action_master_list" model="ir.actions.act_window">-->
        <!--            <field name="name">Services Master List</field>-->
        <!--            <field name="type">ir.actions.act_window</field>-->
        <!--            <field name="res_model">product.template</field>-->
        <!--            <field name="view_mode">kanban,tree,form</field>-->
        <!--            <field name="view_type">form</field>-->
        <!--            <field name="view_id" ref="product.product_template_kanban_view"/>-->
        <!--            <field name="context">{"search_default_filter_to_sell":1, "create": 0, "edit": 0, "delete": 0}</field>-->
        <!--            <field name="help" type="html">-->
        <!--                <p class="oe_view_nocontent_create">-->
        <!--                    Click to define a new product.-->
        <!--                </p><p>-->
        <!--                    You must define a product for everything you sell, whether it's a physical product, a consumable or a service you offer to customers.-->
        <!--                </p><p>-->
        <!--                    The product form contains information to simplify the sale process: price, notes in the quotation, accounting data, procurement methods, etc.-->
        <!--                </p>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <record id="product_template_action_sellable" model="ir.actions.act_window">-->
        <!--            <field name="name">Sellable Products</field>-->
        <!--            <field name="type">ir.actions.act_window</field>-->
        <!--            <field name="res_model">product.template</field>-->
        <!--            <field name="view_mode">kanban,tree,form</field>-->
        <!--            <field name="view_type">form</field>-->
        <!--            <field name="view_id" ref="product.product_template_kanban_view"/>-->
        <!--            <field name="context">{"search_default_filter_to_sell":1, "create": 0, "edit": 0, "delete": 0}</field>-->
        <!--            <field name="help" type="html">-->
        <!--                <p class="oe_view_nocontent_create">-->
        <!--                    Click to define a new product.-->
        <!--                </p><p>-->
        <!--                    You must define a product for everything you sell, whether it's a physical product, a consumable or a service you offer to customers.-->
        <!--                </p><p>-->
        <!--                    The product form contains information to simplify the sale process: price, notes in the quotation, accounting data, procurement methods, etc.-->
        <!--                </p>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <menuitem id="account.menu_product_template_action" name="Sellable Products"-->
        <!--            parent="account.menu_finance_receivables"-->
        <!--            action="product_template_action_sellable" sequence="110"/>-->
        <!--&lt;!&ndash;        <record id="product.product_template_action" model="ir.actions.act_window">&ndash;&gt;-->
        <!--&lt;!&ndash;            <field name="name">Services Master List</field>&ndash;&gt;-->
        <!--&lt;!&ndash;        </record>&ndash;&gt;-->


        <!--        <record id="purchase.product_normal_action_puchased" model="ir.actions.act_window">-->
        <!--            <field name="name">Products</field>-->
        <!--            <field name="type">ir.actions.act_window</field>-->
        <!--            <field name="res_model">product.template</field>-->
        <!--            <field name="view_type">form</field>-->
        <!--            <field name="view_mode">kanban,tree,form</field>-->
        <!--            <field name="context">{"search_default_filter_to_purchase":1, "create": 0, "edit": 0, "delete": 0}</field>-->
        <!--            <field name="search_view_id" eval="False"/> &lt;!&ndash; Force empty &ndash;&gt;-->
        <!--            <field name="view_id" eval="False"/> &lt;!&ndash; Force empty &ndash;&gt;-->
        <!--            <field name="help" type="html">-->
        <!--              <p class="oe_view_nocontent_create">-->
        <!--                Click to define a new product.-->
        <!--              </p><p>-->
        <!--                You must define a product for everything you purchase, whether-->
        <!--                it's a physical product, a consumable or services you buy to-->
        <!--                subcontractors.-->
        <!--              </p><p>-->
        <!--                The product form contains detailed information to improve the-->
        <!--                purchase process: prices, procurement logistics, accounting data,-->
        <!--                available vendors, etc.-->
        <!--              </p>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <menuitem action="product_template_action_master_list"-->
        <!--                  id="sale.menu_product_template_action"-->
        <!--                  parent="sales_team.menu_sales"-->
        <!--                  sequence="13"-->
        <!--                  string="Services Master List"/>-->

        <!--        &lt;!&ndash;    S E R V I C E   T R A N S A C T I O N   T Y P E    &ndash;&gt;-->
        <!--        <record id="service_transaction_type_form_view" model="ir.ui.view">-->
        <!--            <field name="name">service.transaction.type form</field>-->
        <!--            <field name="model">service.transaction.type</field>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <form string="Service Transaction Type">-->
        <!--                    <sheet>-->
        <!--                        <group>-->
        <!--                            <field name="name" required="1"/>-->
        <!--                            <field name="company_id" required="1"/>-->
        <!--                        </group>-->
        <!--                        <notebook>-->
        <!--                            <page>-->
        <!--                                <field name="product_template_ids" nolabel="1" readonly="1">-->
        <!--                                    <tree string="Services" create="0" edit="0" delete="0">-->
        <!--                                        <field name="name" string="Services"/>-->
        <!--                                    </tree>-->
        <!--                                </field>-->
        <!--                            </page>-->
        <!--                        </notebook>-->
        <!--                    </sheet>-->
        <!--                    <div class="oe_chatter">-->
        <!--                        <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>-->
        <!--                        <field name="message_ids" widget="mail_thread"/>-->
        <!--                    </div>-->
        <!--                </form>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <record id="service_transaction_type_form_action" model="ir.actions.act_window">-->
        <!--            <field name="name">Service Transaction Types</field>-->
        <!--            <field name="type">ir.actions.act_window</field>-->
        <!--            <field name="res_model">service.transaction.type</field>-->
        <!--            <field name="view_type">form</field>-->
        <!--            <field name="view_mode">tree,form</field>-->
        <!--            <field name="help" type="html">-->
        <!--                <p class="oe_view_nocontent_create">-->
        <!--                    Click to add a new transaction type for a service.-->
        <!--                </p>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <menuitem action="tawjeeh_operations.service_transaction_type_form_action"-->
        <!--                  id="menu_service_transaction_type_form_action"-->
        <!--                  parent="sale.prod_config_main"-->
        <!--                  sequence="10"/>-->

        <!--        <menuitem action="tawjeeh_operations.service_transaction_type_form_action"-->
        <!--                  id="menu_service_transaction_type_form_action"-->
        <!--                  parent="stock.menu_stock_config_settings"-->
        <!--                  sequence="60"/>-->

    </data>
</odoo>