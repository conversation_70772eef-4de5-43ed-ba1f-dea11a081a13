<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="service_delivery_tree" model="ir.ui.view">
            <field name="name">service.delivery.tree</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <tree string="Service Operations" create="false" decoration-info="state=='draft'" default_order="sale_date_order desc">
                    <field name="trans_no"/>
                    <field name="rec_no"/>
                    <field name="urn_no"/>
                    <field name="noqodi_rec_no" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                    <field name="partner_id"/>
                    <field name="customer_mobile"/>
                    <field name="worker_name"/>
                    <field name="service_product_id"/>
                    <field name="parent_categ_id"/>
                    <field name="gov_fees"/>
                    <field name="typing_fees"/>
                    <field name="typing_discount"/>
                    <field name="applied_cc_charges" groups="tawjeeh_base.group_view_cc_charges"/>
                    <field name="noqodi_amount" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                    <field name="additional_charges"/>
                    <field name="g2g3_charge_amount" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                    <field name="price_tax"/>
                    <field name="service_total"/>
                    <field name="sale_user_id"/>
                    <field name="typist_comm"/>
                    <field name="transaction_type_id"/>
                    <field name="sale_pro_id"/>
                    <field name="sale_executive_id"/>
                    <field name="sale_client_ecard_ref"/>
                    <field name="sale_date_order"/>
                    <field name="sale_id"/>
                    <field name="invoice_id"/>
                    <field name="invoice_type"/>
                    <field name="payment_status"/>
                    <field name="payment_ref"/>
                    <field name="record_mode"/>
                    <field name="state"/>
                    <field name="date_validated" column_invisible="1"/>
                    <field name="currency_id" column_invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="service_delivery_form" model="ir.ui.view">
            <field name="name">service.delivery.form</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <form string="Service Operations" create="false">
                    <header>
                        <button name="button_confirm" type="object" invisible="state not in ['draft']" string="Validate" class="oe_highlight" groups="sales_team.group_sale_manager,tawjeeh_sale.group_sale_order_representative"/>
                        <button name="button_cancel_delivery" type="object" invisible="state not in ['draft']" string="Cancel" confirm="Do you want to cancel?" groups="sales_team.group_sale_salesman"/>
                        <button name="button_cancel_delivery" type="object" invisible="state not in ['done']" string="Cancel" confirm="Do you want to cancel?" groups="sales_team.group_sale_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,done"/>
                    </header>
                    <sheet>
                        <h1 class="hidden-xs">
                            <field name="name" invisible="name == '/'" readonly="1"/>
                        </h1>
                        <group>
                            <group>
                                <field name="mohre_transaction_id" invisible="1"/>
                                <field name="trans_no" default_focus="1" readonly="state == 'cancel' or record_mode == 'auto'"/>
                                <field name="rec_no" string="Receipt Number" readonly="state == 'cancel' or record_mode == 'auto'"/>
                                <field name="urn_no" readonly="state == 'cancel' or record_mode == 'auto'"/>
                                <field name="noqodi_rec_no" groups="tawjeeh_sale.group_noqodi_wallet_sale" readonly="state == 'cancel' or record_mode == 'auto'"/>
                                <field name="applied_cc_charges" groups="tawjeeh_base.group_view_cc_charges"/>
                                <field name="noqodi_amount" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                                <field name="additional_charges"/>
                                <field name="g2g3_charge_amount" groups="tawjeeh_sale.group_noqodi_wallet_sale"/>
                                <field name="service_product_id"/>
                                <field name="transaction_type_id"/>
                                <field name="worker_name" readonly="1" groups="!base.group_no_one" force_save="1"/>
                                <field name="worker_name" readonly="0" groups="base.group_no_one" force_save="1"/>
                            </group>
                            <group>
                                <field name="sale_date_order"/>
                                <field name="date_validated"/>
                                <field name="sale_id"/>
                                <field name="customer_mobile" string="Customer Mobile"/>
                                <field name="sale_user_id"/>
                                <field name="sale_pro_id"/>
                                <field name="sale_executive_id"/>
                                <field name="invoice_id"/>
                                <field name="invoice_type"/>
                                <field name="payment_status"/>
                                <field name="payment_ref"/>
                                <field name="record_mode"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                        <field name="activity_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="view_service_delivery_pivot">
            <field name="name">service.delivery.pivot</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <pivot string="Service Operations" display_quantity="1">
                    <field name="gov_fees" type="measure"/>
                    <field name="typing_fees" type="measure"/>
                    <field name="price_tax" type="measure"/>
                    <field name="sale_user_id"/>
                </pivot>
            </field>
        </record>

        <record model="ir.ui.view" id="view_service_delivery_graph">
            <field name="name">service.delivery.graph</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <graph string="Service Operations" type="pie">
                    <field name="service_product_id"/>
                    <field name="typing_fees" type="measure"/>
                </graph>
            </field>
        </record>

        <record model="ir.ui.view" id="view_service_delivery_kanban">
            <field name="name">service.delivery.kanban</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="rec_no"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="col-xs-6 text-end text-right">
                                        <field name="payment_status" widget="kanban_label_selection" options="{'classes': {'unpaid': 'warning', 'paid': 'success'}}"/>
                                    </div>
                                </div>
                                <div class="oe_kanban_details">
                                    <field name="service_product_id"/>
                                    <div t-if="record.rec_no.value">
                                        <ul>
                                            <li>Receipt Number: <field name="rec_no"/></li>
                                            <li>Transaction No: <field name="trans_no"/></li>
                                        </ul>
                                    </div>
                                    <div name="tags"/>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_service_delivery_calendar" model="ir.ui.view">
            <field name="name">service.delivery.calendar</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <calendar color="service_product_id" date_start="date_validated">
                    <field name="sale_user_id"/>
                    <field name="payment_status"/>
                </calendar>
            </field>
        </record>

        <record id="service_delivery_search" model="ir.ui.view">
            <field name="name">service.delivery.search</field>
            <field name="model">service.delivery</field>
            <field name="arch" type="xml">
                <search string="Service Operations">
                    <field name="name" string="Service List" filter_domain="['|',('name','ilike', self),('sale_id','ilike',self)]"/>
                    <field name="trans_no"/>
                    <field name="rec_no"/>
                    <field name="urn_no"/>
                    <field name="sale_user_id"/>
                    <field name="sale_id"/>
                    <field name="partner_id" filter_domain="[('partner_id','child_of',self)]"/>
                    <field name="service_product_id"/>
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]" help="Draft"/>
                    <filter name="done" string="Done" domain="[('state','=','done'), ('invoice_type', '=', 'out_invoice')]" help="already processed"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('sale_date_order','&gt;=',time.strftime('%%Y-%%m-%%d'))]"/>
                    <filter string="Yesterday" name="yesterday" domain="[('sale_date_order','&gt;=', ((context_today()-datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00'))),                                      ('sale_date_order','&lt;=', ((context_today()+datetime.timedelta(days=0)).strftime('%Y-%m-%d 00:00:00')))]"/>
                    <filter string="This Month" name="thismonth" domain="[('sale_date_order','&gt;=',time.strftime('%%Y-%%m-01'))]"/>
                    <group expand="0" string="Group By">
                        <filter name="gb_service_product_id" string="Service" domain="[]" context="{'group_by':'service_product_id'}"/>
                        <filter name="gb_transaction_type_id" string="Transaction Type" context="{'group_by': 'transaction_type_id'}"/>
                        <filter name="gb_payment_status" string="Payment Status" domain="[]" context="{'group_by':'payment_status'}"/>
                        <filter name="gb_sale_user_id" string="Typist" domain="[]" context="{'group_by':'sale_user_id'}"/>
                        <filter name="gb_sale_pro_id" string="Marketing Executive" domain="[]" context="{'group_by':'sale_pro_id'}"/>
                        <filter name="gb_sale_executive_id" string="Sale Executive" domain="[]" context="{'group_by':'sale_executive_id'}"/>
                        <filter name="gb_state" string="Status" domain="[]" context="{'group_by':'state'}"/>
                        <filter name="gb_sale_id" string="Sale" domain="[]" context="{'group_by':'sale_id'}"/>
                        <filter name="gb_record_mode" string="Mode" domain="[]" context="{'group_by':'record_mode'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_service_delivery" model="ir.actions.act_window">
            <field name="name">Service Operations</field>
            <field name="res_model">service.delivery</field>
            <field name="view_mode">tree,form,graph,pivot,kanban,calendar</field>
        </record>

        <menuitem id="menu_service_delivery" parent="sale.menu_sale_report" action="action_service_delivery" sequence="10"/>
    </data>
</odoo>