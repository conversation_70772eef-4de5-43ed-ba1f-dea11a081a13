from odoo import api, fields, models
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta


MAPPED_DAYOFWEEK = {
    '0': 'Monday',
    '1': 'Tuesday',
    '2': 'Wednesday',
    '3': 'Thursday',
    '4': 'Friday',
    '5': 'Saturday',
    '6': 'Sunday',
}


class ResourceCalendar(models.Model):
    _inherit = "resource.calendar"

    def check_off_days(self, user_id, activity_date):
        user = self.sudo().env['res.users'].browse(user_id)
        if not user:
            return activity_date
        return activity_date + relativedelta(days=self.get_next_off_days(user, activity_date))

    def is_off_day(self, user, current_date):
        resource_calendar = user.sudo().resource_calendar_id
        current_day = datetime.strptime(str(current_date), '%Y-%m-%d').strftime('%A')

        working_hour_details = []
        resource_calendar = resource_calendar.sudo()
        for line in resource_calendar.attendance_ids:
            if line.date_from and line.date_to and line.date_from <= current_date <= line.date_to:
                working_hour_details.append(line)
            elif not line.date_from and line.date_to and line.date_to >= current_date:
                working_hour_details.append(line)
            elif not line.date_from and not line.date_to:
                working_hour_details.append(line)

        if not working_hour_details:
            return False

        business_days = []
        for wh in working_hour_details:
            if wh.sudo().dayofweek not in business_days:
                business_days.append(MAPPED_DAYOFWEEK[wh.sudo().dayofweek])

        if current_day not in business_days:
            return True
        return False

    def get_next_off_days(self, user, activity_date):
        if not user or not activity_date:
            return 0
        activity_days = 0
        if self.is_off_day(user, activity_date):
            activity_days += 1
        for day in [1, 2, 3, 4, 5, 6]:
            if self.is_off_day(user, activity_date + relativedelta(days=day)):
                activity_days += 1
            else:
                break
        return activity_days
