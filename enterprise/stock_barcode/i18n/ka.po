# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_barcode
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <gmelita<PERSON>@live.com>, 2018\n"
"Language-Team: Georgian (https://www.transifex.com/odoo/teams/41243/ka/)\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-archive me-3\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-archive me-3\" title=\"Source Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\" attrs=\"{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Counted Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cubes me-3\" title=\"Locations\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Destination Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Destination Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker me-3\" title=\"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker text-center me-3\" title=\"Source Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-user-o me-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-user-o text-center me-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-trash-o\"/> Delete"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "A product tracked by serial numbers can't have multiple quantities for the same serial number."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Add Product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Add extra product?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "All products need to be packed"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow to validate a picking even if nothing was scanned yet (and so, do an immediate transfert)"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "An unexisting package type was scanned. This part of the barcode can't be processed."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
#, python-format
msgid "Apply"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#, python-format
msgid "Backorder"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#, python-format
msgid "Barcode"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.js:0
#, python-format
msgid "Barcode Manual Entry"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
#, python-format
msgid "Cancel"
msgstr "შეწყვეტა"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Cancel Transfer"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Cancel this operation?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__is_completed
msgid "Check if the quantity done matches the demand"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "შემქმნელი"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "შექმნის თარიღი"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Delete"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_dest_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#, python-format
msgid "Destination Location"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Destination location must be scanned"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "სახელი"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message?\n"
"                    It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Does the picker have to put in a package the scanned products? If yes, at which rate?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.js:0
#, python-format
msgid "Don't show this message again"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#, python-format
msgid "Done /"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Download"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Following backorder was created:"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Following backorders were created:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "General"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "იდენტიფიკატორი"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "If the source location must be scanned, then the destination location must either be scanned after each product or not scanned at all."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#, python-format
msgid "If you validate now, the remaining products will be added to a backorder."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.js:0
#, python-format
msgid "Incomplete Transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Inventory Adjustment"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Inventory Adjustments"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__is_completed
msgid "Is Completed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "ბოლოს განაახლა"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "ბოლოს განახლებულია"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.js:0
#, python-format
msgid "Leave it"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Mandatory Destination Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Mandatory Source Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No internal operation type. Please configure one in warehouse settings."
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No product or package found for barcode %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Not the expected scan"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.picking_view_kanban_inherit_barcode
msgid "Open picking"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
#, python-format
msgid "Open picking form"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Package type %s was correctly applied to the package %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
msgid "Picking"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Please scan destination location for %s before scanning other product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.js:0
#: code:addons/stock_barcode/static/src/main_menu.js:0
#, python-format
msgid "Please, Scan again!"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Press Validate or scan another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Barcodes PDF"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Delivery Slip"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Print Inventory"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Packages"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
#, python-format
msgid "Product"
msgstr "პროდუქტი"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Put In Pack"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.js:0
#, python-format
msgid "Remove it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Scan a"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan a location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a lot number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a lot number or a packages then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a lot number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a package or the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a package, the destination location or another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan a product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product before scanning a tracking number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product from %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan a product in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product or a package"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Scan a product or a package to filter the transfers."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product or another package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product or the destination location."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product, a package or the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a product, a package or the destination location."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a serial number or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a serial number or a package then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan a serial number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#, python-format
msgid "Scan a transfer or a product to filter your records"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#, python-format
msgid "Scan a transfer, a product or a package to filter your records"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Scan an"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan another serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan lot numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan more lot numbers"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan more lot numbers or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan more products in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan more products or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scan serial numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan the package %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan the source location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scan the source location or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scanned product %s is not reserved for this transfer. Are you sure you want to add it?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scanned product [%s] %s is not reserved for this transfer. Are you sure you want to add it?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible with the line's one (%s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible with the product's one (%s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#, python-format
msgid "Scrap"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#, python-format
msgid "Source Location"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#, python-format
msgid "Stay on transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "Tap to scan"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Technical field used to compute whether the \"Final Validation\" group should be displayed, solving combined groups/invisible complexity."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been validated"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "There is nothing to apply in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "There is nothing to print in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "This package is already scanned."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is already done"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "This serial number is already used."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#, python-format
msgid "To do"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "To use packages, enable 'Packages' in the settings"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
#, python-format
msgid "Type barcode you want to scan here"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
#, python-format
msgid "Use camera to scan"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Validate"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "We have created a few demo data with barcodes for you to explore the features. Print the"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Wrong Unit of Measure"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products or a package available at the picking location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You are supposed to scan %s or another source location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You must scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You must scan a product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "commands for Inventory"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "document"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/digipad.xml:0
#, python-format
msgid "o_digipad_button btn d-flex justify-content-center align-items-center border w-100 py-2"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "operation type"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "product"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to create a new transfer."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to know its content."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to open it."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu.xml:0
#, python-format
msgid "to show its location and quantity."
msgstr ""
