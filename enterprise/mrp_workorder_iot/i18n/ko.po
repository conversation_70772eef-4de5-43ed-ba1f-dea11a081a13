# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_iot
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr "추가 작업"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_quality_check__boxes
msgid "Boxes"
msgstr "박스"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__cancel
msgid "Cancel"
msgstr "취소"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clomo
msgid "Close MO"
msgstr "제조주문서 닫기"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clowo
msgid "Close WO"
msgstr "작업지시서 닫기"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_uid
msgid "Created by"
msgstr "작성자"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_date
msgid "Created on"
msgstr "작성일자"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__device_id
msgid "Device"
msgstr "장치"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__display_name
msgid "Display Name"
msgstr "표시명"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__finish
msgid "Finish"
msgstr "완료"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__id
msgid "ID"
msgstr "ID"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_device
msgid "IOT Device"
msgstr "IOT 장치"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr "IOT 트리거"

#. module: mrp_workorder_iot
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.iot_device_view_form
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.mrp_workcenter_view_form_iot
msgid "IoT Triggers"
msgstr "IOT 트리거"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__key
msgid "Key"
msgstr "키"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__next
msgid "Next"
msgstr "다음"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pack
msgid "Pack"
msgstr "묶음"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pause
msgid "Pause"
msgstr "일시 정지"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prev
msgid "Previous"
msgstr "이전"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-slip
msgid "Print Delivery Slip"
msgstr "배송 전표 인쇄"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print
msgid "Print Labels"
msgstr "라벨 인쇄"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-op
msgid "Print Operation"
msgstr "작업 인쇄"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_quality_check
msgid "Quality Check"
msgstr "품질 검사"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__record
msgid "Record Production"
msgstr "생산 기록"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__scrap
msgid "Scrap"
msgstr "폐기"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__sequence
msgid "Sequence"
msgstr "순서"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__skip
msgid "Skip"
msgstr "건너뛰기"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__picture
msgid "Take Picture"
msgstr "사진 찍기"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_device__trigger_ids
msgid "Trigger"
msgstr "트리거"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workcenter__trigger_ids
msgid "Triggers"
msgstr "트리거"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__validate
msgid "Validate"
msgstr "승인"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_mrp_workcenter
msgid "Work Center"
msgstr "작업장"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__workcenter_id
msgid "Workcenter"
msgstr "작업장"
