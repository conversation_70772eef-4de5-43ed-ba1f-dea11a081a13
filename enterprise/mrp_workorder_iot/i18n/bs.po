# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * quality_mrp_iot
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_quality_check__boxes
msgid "Boxes"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__cancel
msgid "Cancel"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clomo
msgid "Close MO"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clowo
msgid "Close WO"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_uid
msgid "Created by"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_date
msgid "Created on"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__device_id
msgid "Device"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__display_name
msgid "Display Name"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__finish
msgid "Finish"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__id
msgid "ID"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr ""

#. module: mrp_workorder_iot
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.iot_device_view_form
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.mrp_workcenter_view_form_iot
msgid "IoT Triggers"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__key
msgid "Key"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__next
msgid "Next"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pack
msgid "Pack"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pause
msgid "Pause"
msgstr ""

#. module: mrp_workorder_iot
#. odoo-javascript
#: code:addons/mrp_workorder_iot/static/src/pedal_status_button/pedal_status_button.xml:0
#, python-format
msgid "Pedal Status"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prev
msgid "Previous"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-slip
msgid "Print Delivery Slip"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print
msgid "Print Labels"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-op
msgid "Print Operation"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_quality_check
msgid "Quality Check"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__record
msgid "Record Production"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__scrap
msgid "Scrap"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__sequence
msgid "Sequence"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__skip
msgid "Skip"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__picture
msgid "Take Picture"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_device__trigger_ids
msgid "Trigger"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workcenter__trigger_ids
msgid "Triggers"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__validate
msgid "Validate"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_mrp_workcenter
msgid "Work Center"
msgstr "Radni centar"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__workcenter_id
msgid "Workcenter"
msgstr "Radni Centar"
