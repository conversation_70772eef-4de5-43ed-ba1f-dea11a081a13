# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign_itsme
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid ""
"<small>Name: The signatory has provided this identity through itsme®</small>"
msgstr "<small>姓名：簽署人已透過 itsme® 提供此身份</small>"

#. module: sign_itsme
#: model:sign.item.role,name:sign_itsme.sign_item_role_itsme_customer
msgid "Customer (identified with itsme®)"
msgstr "客戶（以 itsme® 識別身份）"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
#, python-format
msgid "Error"
msgstr "錯誤"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "額外身份驗證步驟"

#. module: sign_itsme
#: model:ir.model.fields,help:sign_itsme.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr "強制簽署人使用第二種方法進行身份驗證"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
#, python-format
msgid "Go Back"
msgstr "返回"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
#, python-format
msgid "Identification refused"
msgstr "身份驗證被拒絕"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
#, python-format
msgid "Identify with itsme"
msgstr "以 itsme 識別身份"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid "Name"
msgstr "名稱"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
#, python-format
msgid "Please confirm your identity to finalize your signature."
msgstr "請確認你的身份，以完成簽名流程。"

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/models/sign_request_item.py:0
#, python-format
msgid "Sign request item is not validated yet."
msgstr "簽名請求項目尚未驗證。"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_item_role
msgid "Signature Item Party"
msgstr "簽名項目方"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request
msgid "Signature Request"
msgstr "簽名請求"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request_item
msgid "Signature Request Item"
msgstr "簽名請求項目"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
#, python-format
msgid ""
"The itsme® identification data could not be forwarded to Odoo, the signature"
" could not be saved."
msgstr "itsme® 身份驗證數據未能轉發至 Odoo，簽名未能儲存。"

#. module: sign_itsme
#: model:ir.model.fields.selection,name:sign_itsme.selection__sign_item_role__auth_method__itsme
msgid "Via itsme®"
msgstr "透過 itsme®"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
#, python-format
msgid ""
"You have rejected the identification request or took too long to process it."
" You can try again to finalize your signature."
msgstr "你已拒絕身份驗證請求，或身份驗證處理時間過長。你可以重新嘗試完成簽名。"

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/controllers/main.py:0
#, python-format
msgid "itsme® IAP service could not be found."
msgstr "未能找到 itsme® 的 IAP 服務。"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_birthdate
msgid "itsme® Signer's Birthdate"
msgstr "itsme® 簽名者出生日期"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_name
msgid "itsme® Signer's Name"
msgstr "itsme® 簽名者姓名"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_validation_hash
msgid "itsme® Validation Token"
msgstr "itsme® 驗證權杖"
