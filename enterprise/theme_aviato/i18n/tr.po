# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_aviato
# 
# Translators:
# Halil, 2023
# Yedigen, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:21+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Yedigen, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid ""
"<b>A travel agent helps you to plan your <br/> trip from start to finish.</b>\n"
"        <br/>"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "<b><PERSON> and Marie</b> • From France"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "<b>Linda and Paulo</b> • From Italy"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "<b>Rose and Peter</b> • From United Kingdom"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "<em>Odoo Travel New York</em>"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"A heady mix of haunting ruins, awe-inspiring art and vibrant street life, "
"Italy’s hot-blooded capital is one of the world’s most romantic and "
"charismatic cities."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"As always, Odoo Travel has delivered a truly wonderful travel experience for"
" us. Norway was a great travel destination. The parts of the country we "
"visited were beautiful, people were friendly, everything we ate was "
"delicious, and there were many fun things to see and do."
msgstr ""

#. module: theme_aviato
#. odoo-javascript
#: code:addons/theme_aviato/static/src/js/tour.js:0
#: code:addons/theme_aviato/static/src/js/tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Arka Plan Şekli"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Book now"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Check out now and get $200 off your first booking."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Flight Booking"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Hotel Booking"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"It's difficult to describe how awesome the trip was. I feel like my life has"
" changed forever. It would take too long to describe day by day the sights, "
"sounds, architecture, art, food, history, and people that we came across. "
"Ireland was fantastic."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid "Itinerary Planning"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid ""
"Our team travels widely the globe to bring our clients first-hand knowledge "
"of upcoming destinations, new \"hot\" hotels and ships, local restaurants, "
"neighborhood hangouts, bars, pubs, the best. beaches and places to visit and"
" not to see."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "Our travel agencies"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid ""
"Our vision is to make travel planning stress free and make travel more fun. "
"No matter the type of trip you have in mind, we have the resources and "
"expertise necessary to create the perfect trip"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Paris"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Paris' monument-lined boulevards, museums, classical bistros and boutiques "
"are enhanced by a new wave of multimedia galleries, creative wine bars..."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Plan your trip with us"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Prague"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Prague is the equal of Paris in terms of beauty. Its history goes back a "
"millennium. And the beer? The best in Europe. Prague is the perfect city to "
"walk around."
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Rome"
msgstr ""

#. module: theme_aviato
#: model:ir.model,name:theme_aviato.model_theme_utils
msgid "Theme Utils"
msgstr "Tema Araçları"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Ticket Booking"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_title
msgid "Top Destinations"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Travel Now"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Travel to any corner <br/>of the world"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"We would like to thank Odoo Travel for organising wonderful tour for us in "
"Delhi, Agra and Rajasthan. Everything was very well organised. Thank you "
"very much for everything!"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid "What we can do, <b>for you</b>"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Win 200$"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "s_three_columns_1"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "s_three_columns_2"
msgstr ""

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "s_three_columns_3"
msgstr ""
