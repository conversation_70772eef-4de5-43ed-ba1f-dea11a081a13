# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_bpost
# 
# Translators:
# Wil Odoo, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a4
msgid "A4"
msgstr "A4"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a6
msgid "A6"
msgstr "A6"

#. module: delivery_bpost
#: model:ir.model.fields,help:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Allow deliveries on Saturday (extra charges apply)"
msgstr "允许周六发货（额外收费）"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"Authentication error -- wrong credentials\n"
"(Detailed error: %s)"
msgstr ""
"认证失败 -- 凭据有误\n"
"(错误信息: %s)"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_account_number
msgid "Bpost Account Number"
msgstr "Bpost 账户号码"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_delivery_nature
msgid "Bpost Delivery Nature"
msgstr "比利时邮政局"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_domestic_deliver_type
msgid "Bpost Domestic Deliver Type"
msgstr "比利时国内邮政类型"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_domestic
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
msgid "Bpost Domestic bpack 24h Pro"
msgstr "比利时国内bpack 24小时专线"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_international_deliver_type
msgid "Bpost International Deliver Type"
msgstr "比利时国际邮政类型"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_format
msgid "Bpost Label Format"
msgstr "比利时邮政标签格式"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_stock_type
msgid "Bpost Label Stock Type"
msgstr "比利时邮政标签库存类型"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Bpost Package Type"
msgstr "比利时邮政打包类型"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_parcel_return_instructions
msgid "Bpost Parcel Return Instructions"
msgstr "比利时邮政包裹返回指令"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_shipment_type
msgid "Bpost Shipment Type"
msgstr "比利时邮政运输类型"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_inter
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Bpost World Express Pro"
msgstr "比利时邮政国际速运专线"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "承运商"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__documents
msgid "DOCUMENTS"
msgstr "文档"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Delivery on Saturday"
msgstr "周六发货"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__abandoned
msgid "Destroy"
msgstr "销毁"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__domestic
msgid "Domestic"
msgstr "国内的"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__gift
msgid "GIFT"
msgstr "GIFT"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__goods
msgid "GOODS"
msgstr "物品"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__international
msgid "International"
msgstr "国际的"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Format"
msgstr "标签格式"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Type"
msgstr "标签类型"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__other
msgid "OTHER"
msgstr "其他"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Options"
msgstr "选项"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Packages over 30 Kg are not accepted by bpost."
msgstr "比利时邮政不接收超过30Kg的包裹。"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_developer_password
msgid "Passphrase"
msgstr "开发者令牌"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "请提供至少一件要运送的物品。"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "物流商"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Return labels created into bpost"
msgstr "在 bpost 中创建退货标签"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Return shipment created into bpost"
msgstr "向 bpost 创建退件"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rta
msgid "Return to sender by air"
msgstr "空运返回发货人"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rts
msgid "Return to sender by road"
msgstr "陆运返回发货人"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__sample
msgid "SAMPLE"
msgstr "样品"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "运输#%s已取消"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment created into bpost"
msgstr "在 bpost 中创建快件"

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "送货方式"

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_stock_package_type
msgid "Stock package type"
msgstr "库存包裹类型"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "The BPost shipping service is unresponsive, please retry later."
msgstr "比利时邮政送货服务没有响应，请稍后再试。"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The address of your company/warehouse is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr "您公司或仓库的地址有误(缺少字段%s)"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because all your products "
"are service."
msgstr "因为你的所有产品都是服务，所以无法计算出估计的运输价格。"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"因为缺少以下产品的重量，所以无法计算出估计的运输价格。\n"
" %s"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The recipient address is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""
"收件人地址有误(缺少字段 : \n"
"%s)"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Tracking Links"
msgstr "跟踪链接"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Tracking Number:"
msgstr "追踪号码："

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Tracking Numbers:"
msgstr "追踪号码："

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "You cannot compute a passphrase for non-bpost carriers."
msgstr "您不能为非比利时邮政承运商计算令牌。"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Your company/warehouse address must be in Belgium to ship with bpost"
msgstr "您的公司或仓库地址必须在比利时境内来使用Bpost运输"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_pro
msgid "bpack 24h Pro"
msgstr "bpack 24h 专业版"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_business
msgid "bpack 24h business"
msgstr "bpack 24 小时业务"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_bus
msgid "bpack Bus"
msgstr "bpack Bus"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_europe_business
msgid "bpack Europe Business"
msgstr "bpack 的欧洲业务"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_business
msgid "bpack World Business"
msgstr "bpack 国际业务"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_express_pro
msgid "bpack World Express Pro"
msgstr "bpack 国际速递专业版"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__delivery_type__bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__stock_package_type__package_carrier_type__bpost
msgid "bpost"
msgstr "bpost"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "bpost Configuration"
msgstr "Bpost配置"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_default_package_type_id
msgid "bpost Default Package Type"
msgstr "bpost 默认包类型"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost Domestic is used only to ship inside Belgium. Please change the "
"delivery method into bpost International."
msgstr "Bpost国内仅用于在比利时境内运输。请更改运输方式为Bpost国际。"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost International is used only to ship outside Belgium. Please change the "
"delivery method into bpost Domestic."
msgstr "Bpost国际仅用于在比利时境外运输。请更改运输方式为Bpost国内。"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_stock
msgid "bpost Shipping Methods"
msgstr "比利时邮政运输方式"

#. module: delivery_bpost
#. odoo-python
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "bpost did not return prices for this destination country."
msgstr "bpost 没有返回该目的地国家的价格。"
