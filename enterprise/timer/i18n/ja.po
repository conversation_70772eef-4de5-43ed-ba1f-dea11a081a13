# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "作成者"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "作成日"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "表示名"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_pause
msgid "Display Timer Pause"
msgstr "タイマーの一時停止を表示"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_resume
msgid "Display Timer Resume"
msgstr "タイマー再開の表示"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "表示タイマー開始プライマリ"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_stop
msgid "Display Timer Stop"
msgstr "表示タイマー停止"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "ID"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__is_timer_running
#: model:ir.model.fields,field_description:timer.field_timer_timer__is_timer_running
msgid "Is Timer Running"
msgstr "タイマーは作動していますか"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr "モデル、レコード、ユーザによるタイマーの発生は1回のみです。"

#. module: timer
#. odoo-python
#: code:addons/timer/models/timer_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "操作はサポートされていません"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_id
msgid "Res"
msgstr "リセット"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_model
msgid "Res Model"
msgstr "Resモデル"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
#, python-format
msgid "Start"
msgstr "開始"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
#, python-format
msgid "Stop"
msgstr "停止"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_pause
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_pause
msgid "Timer Last Pause"
msgstr "タイマーの最後の一時停止"

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr "タイマーMixin"

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr "タイマーモジュール"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_start
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_start
msgid "Timer Start"
msgstr "タイマースタート"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__user_id
msgid "User"
msgstr "ユーザ"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__user_timer_id
msgid "User Timer"
msgstr "ユーザータイマー"
