{"version": 5, "sheets": [{"id": "a9d3b61d-b996-4144-b391-a1b246e80c9b", "name": "Revenue by Team", "colNumber": 22, "rowNumber": 282, "rows": {}, "cols": {"0": {"size": 118.5205078125}, "1": {"size": 96.18310546875}, "2": {"size": 96.18310546875}, "3": {"size": 96.18310546875}, "4": {"size": 96.18310546875}, "5": {"size": 96.18310546875}, "6": {"size": 96.18310546875}, "7": {"size": 96.18310546875}, "8": {"size": 96.18310546875}}, "merges": ["A1:I2", "B3:C3", "D3:E3", "F3:G3", "H3:I3", "A3:A4"], "cells": {"A1": {"content": "=\"Monthly Revenue by Team - \"&FILTER.VALUE(\"Year\")", "style": 17}, "A2": {"content": "", "style": 17}, "B1": {"content": "", "style": 17}, "B2": {"content": "", "style": 17}, "C1": {"content": "", "style": 17}, "C2": {"content": "", "style": 17}, "H1": {"content": "", "style": 17}, "H2": {"content": "", "style": 17}, "I1": {"content": "", "style": 17}, "I2": {"content": "", "style": 17}, "A5": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A6": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A7": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A8": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A9": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A10": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B4": {"content": "MRR", "style": 35}, "C4": {"content": "NRR", "style": 36}, "B5": {"content": "=sum(B6:B25)", "style": 46, "format": "#,##0.00"}, "B6": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B7": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B8": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B9": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B10": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C5": {"content": "=sum(C6:C25)", "style": 46, "format": "#,##0.00"}, "C6": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C7": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C8": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C9": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C10": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H4": {"content": "MRR", "style": 35}, "I4": {"content": "NRR", "style": 36}, "H5": {"content": "=sum(H6:H25)", "style": 46, "format": "#,##0.00"}, "H6": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H7": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H8": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H9": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H10": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I5": {"content": "=sum(I6:I25)", "style": 46, "format": "#,##0.00"}, "I6": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I7": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I8": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I9": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I10": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D4": {"content": "MRR", "style": 35}, "E4": {"content": "NRR", "style": 36}, "F4": {"content": "MRR", "style": 35, "format": "0.00%"}, "G4": {"content": "NRR", "style": 36, "format": "0.00%"}, "D5": {"content": "=sum(D6:D25)", "style": 46, "format": "#,##0.00"}, "E5": {"content": "=sum(E6:E25)", "style": 46, "format": "#,##0.00"}, "F5": {"content": "=iferror(B5/D5,0)", "style": 46, "format": "0.00%"}, "G5": {"content": "=iferror(C5/E5,0)", "style": 46, "format": "0.00%"}, "D6": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E6": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F6": {"content": "=iferror(B6/D6,0)", "style": 20, "format": "0.00%"}, "G6": {"content": "=iferror(C6/E6,0)", "style": 20, "format": "0.00%"}, "D7": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E7": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F7": {"content": "=iferror(B7/D7,0)", "style": 20, "format": "0.00%"}, "G7": {"content": "=iferror(C7/E7,0)", "style": 20, "format": "0.00%"}, "D8": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E8": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F8": {"content": "=iferror(B8/D8,0)", "style": 20, "format": "0.00%"}, "G8": {"content": "=iferror(C8/E8,0)", "style": 20, "format": "0.00%"}, "D9": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E9": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F9": {"content": "=iferror(B9/D9,0)", "style": 20, "format": "0.00%"}, "G9": {"content": "=iferror(C9/E9,0)", "style": 20, "format": "0.00%"}, "D10": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E10": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F10": {"content": "=iferror(B10/D10,0)", "style": 20, "format": "0.00%"}, "G10": {"content": "=iferror(C10/E10,0)", "style": 20, "format": "0.00%"}, "J4": {"content": "", "style": 13}, "K4": {"content": "", "style": 13}, "L4": {"content": "", "style": 13}, "M4": {"content": "", "style": 13}, "N4": {"content": "", "style": 13}, "O4": {"content": "", "style": 13}, "P4": {"content": "", "style": 13}, "Q4": {"content": "", "style": 13}, "R4": {"content": "", "style": 13}, "S4": {"content": "", "style": 13}, "T4": {"content": "", "style": 13}, "U4": {"content": "", "style": 13}, "V4": {"content": "", "style": 13}, "A3": {"content": "", "style": 8}, "B3": {"content": "Actuals", "style": 9}, "D3": {"content": "Target", "style": 9}, "F3": {"content": "Performance", "style": 9, "format": "0.00%"}, "H3": {"content": "Forecasted", "style": 9}, "J3": {"content": "", "style": 10}, "K3": {"content": "", "style": 10}, "L3": {"content": "", "style": 10}, "M3": {"content": "", "style": 10}, "N3": {"content": "", "style": 10}, "O3": {"content": "", "style": 10}, "P3": {"content": "", "style": 10}, "Q3": {"content": "", "style": 10}, "R3": {"content": "", "style": 10}, "S3": {"content": "", "style": 10}, "T3": {"content": "", "style": 10}, "U3": {"content": "", "style": 10}, "V3": {"content": "", "style": 10}, "D1": {"content": "", "style": 18}, "D2": {"content": "", "style": 18}, "E1": {"content": "", "style": 18}, "E2": {"content": "", "style": 18}, "F1": {"content": "", "style": 18, "format": "0.00%"}, "F2": {"content": "", "style": 18, "format": "0.00%"}, "G1": {"content": "", "style": 18, "format": "0.00%"}, "G2": {"content": "", "style": 18, "format": "0.00%"}, "J5": {"content": "", "style": 7}, "K5": {"content": "", "style": 7}, "L5": {"content": "", "style": 7}, "M5": {"content": "", "style": 7}, "N5": {"content": "", "style": 7}, "O5": {"content": "", "style": 7}, "P5": {"content": "", "style": 7}, "Q5": {"content": "", "style": 7}, "R5": {"content": "", "style": 7}, "S5": {"content": "", "style": 7}, "T5": {"content": "", "style": 7}, "U5": {"content": "", "style": 7}, "V5": {"content": "", "style": 7}, "G3": {"content": "", "format": "0.00%"}, "A4": {"content": "", "style": 8}, "A26": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A27": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A28": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A29": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A30": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A31": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B26": {"content": "=sum(B27:B46)", "style": 46, "format": "#,##0.00"}, "B27": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B28": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B29": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B30": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B31": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C26": {"content": "=sum(C27:C46)", "style": 46, "format": "#,##0.00"}, "C27": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C28": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C29": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C30": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C31": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H26": {"content": "=sum(H27:H46)", "style": 46, "format": "#,##0.00"}, "H27": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H28": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H29": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H30": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H31": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I26": {"content": "=sum(I27:I46)", "style": 46, "format": "#,##0.00"}, "I27": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I28": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I29": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I30": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I31": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D26": {"content": "=sum(D27:D46)", "style": 46, "format": "#,##0.00"}, "E26": {"content": "=sum(E27:E46)", "style": 46, "format": "#,##0.00"}, "F26": {"content": "=iferror(B26/D26,0)", "style": 46, "format": "0.00%"}, "G26": {"content": "=iferror(C26/E26,0)", "style": 46, "format": "0.00%"}, "D27": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E27": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F27": {"content": "=iferror(B27/D27,0)", "style": 20, "format": "0.00%"}, "G27": {"content": "=iferror(C27/E27,0)", "style": 20, "format": "0.00%"}, "D28": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E28": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F28": {"content": "=iferror(B28/D28,0)", "style": 20, "format": "0.00%"}, "G28": {"content": "=iferror(C28/E28,0)", "style": 20, "format": "0.00%"}, "D29": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E29": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F29": {"content": "=iferror(B29/D29,0)", "style": 20, "format": "0.00%"}, "G29": {"content": "=iferror(C29/E29,0)", "style": 20, "format": "0.00%"}, "D30": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E30": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F30": {"content": "=iferror(B30/D30,0)", "style": 20, "format": "0.00%"}, "G30": {"content": "=iferror(C30/E30,0)", "style": 20, "format": "0.00%"}, "D31": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E31": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F31": {"content": "=iferror(B31/D31,0)", "style": 20, "format": "0.00%"}, "G31": {"content": "=iferror(C31/E31,0)", "style": 20, "format": "0.00%"}, "J26": {"content": "", "style": 7}, "K26": {"content": "", "style": 7}, "L26": {"content": "", "style": 7}, "M26": {"content": "", "style": 7}, "N26": {"content": "", "style": 7}, "O26": {"content": "", "style": 7}, "P26": {"content": "", "style": 7}, "Q26": {"content": "", "style": 7}, "R26": {"content": "", "style": 7}, "S26": {"content": "", "style": 7}, "T26": {"content": "", "style": 7}, "U26": {"content": "", "style": 7}, "V26": {"content": "", "style": 7}, "A47": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A48": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A49": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A50": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A51": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A52": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B47": {"content": "=sum(B48:B67)", "style": 46, "format": "#,##0.00"}, "B48": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B49": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B50": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B51": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B52": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C47": {"content": "=sum(C48:C67)", "style": 46, "format": "#,##0.00"}, "C48": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C49": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C50": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C51": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C52": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H47": {"content": "=sum(H48:H67)", "style": 46, "format": "#,##0.00"}, "H48": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H49": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H50": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H51": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H52": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I47": {"content": "=sum(I48:I67)", "style": 46, "format": "#,##0.00"}, "I48": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I49": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I50": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I51": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I52": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D47": {"content": "=sum(D48:D67)", "style": 46, "format": "#,##0.00"}, "E47": {"content": "=sum(E48:E67)", "style": 46, "format": "#,##0.00"}, "F47": {"content": "=iferror(B47/D47,0)", "style": 46, "format": "0.00%"}, "G47": {"content": "=iferror(C47/E47,0)", "style": 46, "format": "0.00%"}, "D48": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E48": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F48": {"content": "=iferror(B48/D48,0)", "style": 20, "format": "0.00%"}, "G48": {"content": "=iferror(C48/E48,0)", "style": 20, "format": "0.00%"}, "D49": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E49": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F49": {"content": "=iferror(B49/D49,0)", "style": 20, "format": "0.00%"}, "G49": {"content": "=iferror(C49/E49,0)", "style": 20, "format": "0.00%"}, "D50": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E50": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F50": {"content": "=iferror(B50/D50,0)", "style": 20, "format": "0.00%"}, "G50": {"content": "=iferror(C50/E50,0)", "style": 20, "format": "0.00%"}, "D51": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E51": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F51": {"content": "=iferror(B51/D51,0)", "style": 20, "format": "0.00%"}, "G51": {"content": "=iferror(C51/E51,0)", "style": 20, "format": "0.00%"}, "D52": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E52": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F52": {"content": "=iferror(B52/D52,0)", "style": 20, "format": "0.00%"}, "G52": {"content": "=iferror(C52/E52,0)", "style": 20, "format": "0.00%"}, "J47": {"content": "", "style": 7}, "K47": {"content": "", "style": 7}, "L47": {"content": "", "style": 7}, "M47": {"content": "", "style": 7}, "N47": {"content": "", "style": 7}, "O47": {"content": "", "style": 7}, "P47": {"content": "", "style": 7}, "Q47": {"content": "", "style": 7}, "R47": {"content": "", "style": 7}, "S47": {"content": "", "style": 7}, "T47": {"content": "", "style": 7}, "U47": {"content": "", "style": 7}, "V47": {"content": "", "style": 7}, "A68": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A69": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A70": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A71": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A72": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A73": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B68": {"content": "=sum(B69:B88)", "style": 46, "format": "#,##0.00"}, "B69": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B70": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B71": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B72": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B73": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C68": {"content": "=sum(C69:C88)", "style": 46, "format": "#,##0.00"}, "C69": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C70": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C71": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C72": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C73": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H68": {"content": "=sum(H69:H88)", "style": 46, "format": "#,##0.00"}, "H69": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H70": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H71": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H72": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H73": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I68": {"content": "=sum(I69:I88)", "style": 46, "format": "#,##0.00"}, "I69": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I70": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I71": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I72": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I73": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D68": {"content": "=sum(D69:D88)", "style": 46, "format": "#,##0.00"}, "E68": {"content": "=sum(E69:E88)", "style": 46, "format": "#,##0.00"}, "F68": {"content": "=iferror(B68/D68,0)", "style": 46, "format": "0.00%"}, "G68": {"content": "=iferror(C68/E68,0)", "style": 46, "format": "0.00%"}, "D69": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E69": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F69": {"content": "=iferror(B69/D69,0)", "style": 20, "format": "0.00%"}, "G69": {"content": "=iferror(C69/E69,0)", "style": 20, "format": "0.00%"}, "D70": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E70": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F70": {"content": "=iferror(B70/D70,0)", "style": 20, "format": "0.00%"}, "G70": {"content": "=iferror(C70/E70,0)", "style": 20, "format": "0.00%"}, "D71": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E71": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F71": {"content": "=iferror(B71/D71,0)", "style": 20, "format": "0.00%"}, "G71": {"content": "=iferror(C71/E71,0)", "style": 20, "format": "0.00%"}, "D72": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E72": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F72": {"content": "=iferror(B72/D72,0)", "style": 20, "format": "0.00%"}, "G72": {"content": "=iferror(C72/E72,0)", "style": 20, "format": "0.00%"}, "D73": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E73": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F73": {"content": "=iferror(B73/D73,0)", "style": 20, "format": "0.00%"}, "G73": {"content": "=iferror(C73/E73,0)", "style": 20, "format": "0.00%"}, "J68": {"content": "", "style": 7}, "K68": {"content": "", "style": 7}, "L68": {"content": "", "style": 7}, "M68": {"content": "", "style": 7}, "N68": {"content": "", "style": 7}, "O68": {"content": "", "style": 7}, "P68": {"content": "", "style": 7}, "Q68": {"content": "", "style": 7}, "R68": {"content": "", "style": 7}, "S68": {"content": "", "style": 7}, "T68": {"content": "", "style": 7}, "U68": {"content": "", "style": 7}, "V68": {"content": "", "style": 7}, "A89": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A90": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A91": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A92": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A93": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A94": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B89": {"content": "=sum(B90:B109)", "style": 46, "format": "#,##0.00"}, "B90": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B91": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B92": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B93": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B94": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C89": {"content": "=sum(C90:C109)", "style": 46, "format": "#,##0.00"}, "C90": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C91": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C92": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C93": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C94": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H89": {"content": "=sum(H90:H109)", "style": 46, "format": "#,##0.00"}, "H90": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H91": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H92": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H93": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H94": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I89": {"content": "=sum(I90:I109)", "style": 46, "format": "#,##0.00"}, "I90": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I91": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I92": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I93": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I94": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D89": {"content": "=sum(D90:D109)", "style": 46, "format": "#,##0.00"}, "E89": {"content": "=sum(E90:E109)", "style": 46, "format": "#,##0.00"}, "F89": {"content": "=iferror(B89/D89,0)", "style": 46, "format": "0.00%"}, "G89": {"content": "=iferror(C89/E89,0)", "style": 46, "format": "0.00%"}, "D90": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E90": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F90": {"content": "=iferror(B90/D90,0)", "style": 20, "format": "0.00%"}, "G90": {"content": "=iferror(C90/E90,0)", "style": 20, "format": "0.00%"}, "D91": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E91": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F91": {"content": "=iferror(B91/D91,0)", "style": 20, "format": "0.00%"}, "G91": {"content": "=iferror(C91/E91,0)", "style": 20, "format": "0.00%"}, "D92": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E92": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F92": {"content": "=iferror(B92/D92,0)", "style": 20, "format": "0.00%"}, "G92": {"content": "=iferror(C92/E92,0)", "style": 20, "format": "0.00%"}, "D93": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E93": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F93": {"content": "=iferror(B93/D93,0)", "style": 20, "format": "0.00%"}, "G93": {"content": "=iferror(C93/E93,0)", "style": 20, "format": "0.00%"}, "D94": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E94": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F94": {"content": "=iferror(B94/D94,0)", "style": 20, "format": "0.00%"}, "G94": {"content": "=iferror(C94/E94,0)", "style": 20, "format": "0.00%"}, "J89": {"content": "", "style": 7}, "K89": {"content": "", "style": 7}, "L89": {"content": "", "style": 7}, "M89": {"content": "", "style": 7}, "N89": {"content": "", "style": 7}, "O89": {"content": "", "style": 7}, "P89": {"content": "", "style": 7}, "Q89": {"content": "", "style": 7}, "R89": {"content": "", "style": 7}, "S89": {"content": "", "style": 7}, "T89": {"content": "", "style": 7}, "U89": {"content": "", "style": 7}, "V89": {"content": "", "style": 7}, "A110": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A111": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A112": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A113": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A114": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A115": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B110": {"content": "=sum(B111:B130)", "style": 46, "format": "#,##0.00"}, "B111": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B112": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B113": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B114": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B115": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C110": {"content": "=sum(C111:C130)", "style": 46, "format": "#,##0.00"}, "C111": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C112": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C113": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C114": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C115": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H110": {"content": "=sum(H111:H130)", "style": 46, "format": "#,##0.00"}, "H111": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H112": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H113": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H114": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H115": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I110": {"content": "=sum(I111:I130)", "style": 46, "format": "#,##0.00"}, "I111": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I112": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I113": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I114": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I115": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D110": {"content": "=sum(D111:D130)", "style": 46, "format": "#,##0.00"}, "E110": {"content": "=sum(E111:E130)", "style": 46, "format": "#,##0.00"}, "F110": {"content": "=iferror(B110/D110,0)", "style": 46, "format": "0.00%"}, "G110": {"content": "=iferror(C110/E110,0)", "style": 46, "format": "0.00%"}, "D111": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E111": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F111": {"content": "=iferror(B111/D111,0)", "style": 20, "format": "0.00%"}, "G111": {"content": "=iferror(C111/E111,0)", "style": 20, "format": "0.00%"}, "D112": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E112": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F112": {"content": "=iferror(B112/D112,0)", "style": 20, "format": "0.00%"}, "G112": {"content": "=iferror(C112/E112,0)", "style": 20, "format": "0.00%"}, "D113": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E113": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F113": {"content": "=iferror(B113/D113,0)", "style": 20, "format": "0.00%"}, "G113": {"content": "=iferror(C113/E113,0)", "style": 20, "format": "0.00%"}, "D114": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E114": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F114": {"content": "=iferror(B114/D114,0)", "style": 20, "format": "0.00%"}, "G114": {"content": "=iferror(C114/E114,0)", "style": 20, "format": "0.00%"}, "D115": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E115": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F115": {"content": "=iferror(B115/D115,0)", "style": 20, "format": "0.00%"}, "G115": {"content": "=iferror(C115/E115,0)", "style": 20, "format": "0.00%"}, "J110": {"content": "", "style": 7}, "K110": {"content": "", "style": 7}, "L110": {"content": "", "style": 7}, "M110": {"content": "", "style": 7}, "N110": {"content": "", "style": 7}, "O110": {"content": "", "style": 7}, "P110": {"content": "", "style": 7}, "Q110": {"content": "", "style": 7}, "R110": {"content": "", "style": 7}, "S110": {"content": "", "style": 7}, "T110": {"content": "", "style": 7}, "U110": {"content": "", "style": 7}, "V110": {"content": "", "style": 7}, "A131": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A132": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A133": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A134": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A135": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A136": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B131": {"content": "=sum(B132:B151)", "style": 46, "format": "#,##0.00"}, "B132": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B133": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B134": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B135": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B136": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C131": {"content": "=sum(C132:C151)", "style": 46, "format": "#,##0.00"}, "C132": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C133": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C134": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C135": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C136": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H131": {"content": "=sum(H132:H151)", "style": 46, "format": "#,##0.00"}, "H132": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H133": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H134": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H135": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H136": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I131": {"content": "=sum(I132:I151)", "style": 46, "format": "#,##0.00"}, "I132": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I133": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I134": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I135": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I136": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D131": {"content": "=sum(D132:D151)", "style": 46, "format": "#,##0.00"}, "E131": {"content": "=sum(E132:E151)", "style": 46, "format": "#,##0.00"}, "F131": {"content": "=iferror(B131/D131,0)", "style": 46, "format": "0.00%"}, "G131": {"content": "=iferror(C131/E131,0)", "style": 46, "format": "0.00%"}, "D132": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E132": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F132": {"content": "=iferror(B132/D132,0)", "style": 20, "format": "0.00%"}, "G132": {"content": "=iferror(C132/E132,0)", "style": 20, "format": "0.00%"}, "D133": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E133": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F133": {"content": "=iferror(B133/D133,0)", "style": 20, "format": "0.00%"}, "G133": {"content": "=iferror(C133/E133,0)", "style": 20, "format": "0.00%"}, "D134": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E134": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F134": {"content": "=iferror(B134/D134,0)", "style": 20, "format": "0.00%"}, "G134": {"content": "=iferror(C134/E134,0)", "style": 20, "format": "0.00%"}, "D135": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E135": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F135": {"content": "=iferror(B135/D135,0)", "style": 20, "format": "0.00%"}, "G135": {"content": "=iferror(C135/E135,0)", "style": 20, "format": "0.00%"}, "D136": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E136": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F136": {"content": "=iferror(B136/D136,0)", "style": 20, "format": "0.00%"}, "G136": {"content": "=iferror(C136/E136,0)", "style": 20, "format": "0.00%"}, "J131": {"content": "", "style": 7}, "K131": {"content": "", "style": 7}, "L131": {"content": "", "style": 7}, "M131": {"content": "", "style": 7}, "N131": {"content": "", "style": 7}, "O131": {"content": "", "style": 7}, "P131": {"content": "", "style": 7}, "Q131": {"content": "", "style": 7}, "R131": {"content": "", "style": 7}, "S131": {"content": "", "style": 7}, "T131": {"content": "", "style": 7}, "U131": {"content": "", "style": 7}, "V131": {"content": "", "style": 7}, "A152": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A153": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A154": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A155": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A156": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A157": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B152": {"content": "=sum(B153:B172)", "style": 46, "format": "#,##0.00"}, "B153": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B154": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B155": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B156": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B157": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C152": {"content": "=sum(C153:C172)", "style": 46, "format": "#,##0.00"}, "C153": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C154": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C155": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C156": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C157": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H152": {"content": "=sum(H153:H172)", "style": 46, "format": "#,##0.00"}, "H153": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H154": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H155": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H156": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H157": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I152": {"content": "=sum(I153:I172)", "style": 46, "format": "#,##0.00"}, "I153": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I154": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I155": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I156": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I157": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D152": {"content": "=sum(D153:D172)", "style": 46, "format": "#,##0.00"}, "E152": {"content": "=sum(E153:E172)", "style": 46, "format": "#,##0.00"}, "F152": {"content": "=iferror(B152/D152,0)", "style": 46, "format": "0.00%"}, "G152": {"content": "=iferror(C152/E152,0)", "style": 46, "format": "0.00%"}, "D153": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E153": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F153": {"content": "=iferror(B153/D153,0)", "style": 20, "format": "0.00%"}, "G153": {"content": "=iferror(C153/E153,0)", "style": 20, "format": "0.00%"}, "D154": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E154": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F154": {"content": "=iferror(B154/D154,0)", "style": 20, "format": "0.00%"}, "G154": {"content": "=iferror(C154/E154,0)", "style": 20, "format": "0.00%"}, "D155": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E155": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F155": {"content": "=iferror(B155/D155,0)", "style": 20, "format": "0.00%"}, "G155": {"content": "=iferror(C155/E155,0)", "style": 20, "format": "0.00%"}, "D156": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E156": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F156": {"content": "=iferror(B156/D156,0)", "style": 20, "format": "0.00%"}, "G156": {"content": "=iferror(C156/E156,0)", "style": 20, "format": "0.00%"}, "D157": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E157": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F157": {"content": "=iferror(B157/D157,0)", "style": 20, "format": "0.00%"}, "G157": {"content": "=iferror(C157/E157,0)", "style": 20, "format": "0.00%"}, "J152": {"content": "", "style": 7}, "K152": {"content": "", "style": 7}, "L152": {"content": "", "style": 7}, "M152": {"content": "", "style": 7}, "N152": {"content": "", "style": 7}, "O152": {"content": "", "style": 7}, "P152": {"content": "", "style": 7}, "Q152": {"content": "", "style": 7}, "R152": {"content": "", "style": 7}, "S152": {"content": "", "style": 7}, "T152": {"content": "", "style": 7}, "U152": {"content": "", "style": 7}, "V152": {"content": "", "style": 7}, "A173": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A174": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A175": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A176": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A177": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A178": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B173": {"content": "=sum(B174:B193)", "style": 46, "format": "#,##0.00"}, "B174": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B175": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B176": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B177": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B178": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C173": {"content": "=sum(C174:C193)", "style": 46, "format": "#,##0.00"}, "C174": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C175": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C176": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C177": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C178": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H173": {"content": "=sum(H174:H193)", "style": 46, "format": "#,##0.00"}, "H174": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H175": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H176": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H177": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H178": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I173": {"content": "=sum(I174:I193)", "style": 46, "format": "#,##0.00"}, "I174": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I175": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I176": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I177": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I178": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D173": {"content": "=sum(D174:D193)", "style": 46, "format": "#,##0.00"}, "E173": {"content": "=sum(E174:E193)", "style": 46, "format": "#,##0.00"}, "F173": {"content": "=iferror(B173/D173,0)", "style": 46, "format": "0.00%"}, "G173": {"content": "=iferror(C173/E173,0)", "style": 46, "format": "0.00%"}, "D174": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E174": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F174": {"content": "=iferror(B174/D174,0)", "style": 20, "format": "0.00%"}, "G174": {"content": "=iferror(C174/E174,0)", "style": 20, "format": "0.00%"}, "D175": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E175": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F175": {"content": "=iferror(B175/D175,0)", "style": 20, "format": "0.00%"}, "G175": {"content": "=iferror(C175/E175,0)", "style": 20, "format": "0.00%"}, "D176": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E176": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F176": {"content": "=iferror(B176/D176,0)", "style": 20, "format": "0.00%"}, "G176": {"content": "=iferror(C176/E176,0)", "style": 20, "format": "0.00%"}, "D177": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E177": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F177": {"content": "=iferror(B177/D177,0)", "style": 20, "format": "0.00%"}, "G177": {"content": "=iferror(C177/E177,0)", "style": 20, "format": "0.00%"}, "D178": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E178": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F178": {"content": "=iferror(B178/D178,0)", "style": 20, "format": "0.00%"}, "G178": {"content": "=iferror(C178/E178,0)", "style": 20, "format": "0.00%"}, "J173": {"content": "", "style": 7}, "K173": {"content": "", "style": 7}, "L173": {"content": "", "style": 7}, "M173": {"content": "", "style": 7}, "N173": {"content": "", "style": 7}, "O173": {"content": "", "style": 7}, "P173": {"content": "", "style": 7}, "Q173": {"content": "", "style": 7}, "R173": {"content": "", "style": 7}, "S173": {"content": "", "style": 7}, "T173": {"content": "", "style": 7}, "U173": {"content": "", "style": 7}, "V173": {"content": "", "style": 7}, "A194": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A195": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A196": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A197": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A198": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A199": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B194": {"content": "=sum(B195:B214)", "style": 46, "format": "#,##0.00"}, "B195": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B196": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B197": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B198": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B199": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C194": {"content": "=sum(C195:C214)", "style": 46, "format": "#,##0.00"}, "C195": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C196": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C197": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C198": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C199": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H194": {"content": "=sum(H195:H214)", "style": 46, "format": "#,##0.00"}, "H195": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H196": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H197": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H198": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H199": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I194": {"content": "=sum(I195:I214)", "style": 46, "format": "#,##0.00"}, "I195": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I196": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I197": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I198": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I199": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D194": {"content": "=sum(D195:D214)", "style": 46, "format": "#,##0.00"}, "E194": {"content": "=sum(E195:E214)", "style": 46, "format": "#,##0.00"}, "F194": {"content": "=iferror(B194/D194,0)", "style": 46, "format": "0.00%"}, "G194": {"content": "=iferror(C194/E194,0)", "style": 46, "format": "0.00%"}, "D195": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E195": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F195": {"content": "=iferror(B195/D195,0)", "style": 20, "format": "0.00%"}, "G195": {"content": "=iferror(C195/E195,0)", "style": 20, "format": "0.00%"}, "D196": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E196": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F196": {"content": "=iferror(B196/D196,0)", "style": 20, "format": "0.00%"}, "G196": {"content": "=iferror(C196/E196,0)", "style": 20, "format": "0.00%"}, "D197": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E197": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F197": {"content": "=iferror(B197/D197,0)", "style": 20, "format": "0.00%"}, "G197": {"content": "=iferror(C197/E197,0)", "style": 20, "format": "0.00%"}, "D198": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E198": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F198": {"content": "=iferror(B198/D198,0)", "style": 20, "format": "0.00%"}, "G198": {"content": "=iferror(C198/E198,0)", "style": 20, "format": "0.00%"}, "D199": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E199": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F199": {"content": "=iferror(B199/D199,0)", "style": 20, "format": "0.00%"}, "G199": {"content": "=iferror(C199/E199,0)", "style": 20, "format": "0.00%"}, "J194": {"content": "", "style": 7}, "K194": {"content": "", "style": 7}, "L194": {"content": "", "style": 7}, "M194": {"content": "", "style": 7}, "N194": {"content": "", "style": 7}, "O194": {"content": "", "style": 7}, "P194": {"content": "", "style": 7}, "Q194": {"content": "", "style": 7}, "R194": {"content": "", "style": 7}, "S194": {"content": "", "style": 7}, "T194": {"content": "", "style": 7}, "U194": {"content": "", "style": 7}, "V194": {"content": "", "style": 7}, "A215": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A216": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A217": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A218": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A219": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A220": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B215": {"content": "=sum(B216:B235)", "style": 46, "format": "#,##0.00"}, "B216": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B217": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B218": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B219": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B220": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C215": {"content": "=sum(C216:C235)", "style": 46, "format": "#,##0.00"}, "C216": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C217": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C218": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C219": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C220": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H215": {"content": "=sum(H216:H235)", "style": 46, "format": "#,##0.00"}, "H216": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H217": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H218": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H219": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H220": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I215": {"content": "=sum(I216:I235)", "style": 46, "format": "#,##0.00"}, "I216": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I217": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I218": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I219": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I220": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D215": {"content": "=sum(D216:D235)", "style": 46, "format": "#,##0.00"}, "E215": {"content": "=sum(E216:E235)", "style": 46, "format": "#,##0.00"}, "F215": {"content": "=iferror(B215/D215,0)", "style": 46, "format": "0.00%"}, "G215": {"content": "=iferror(C215/E215,0)", "style": 46, "format": "0.00%"}, "D216": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E216": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F216": {"content": "=iferror(B216/D216,0)", "style": 20, "format": "0.00%"}, "G216": {"content": "=iferror(C216/E216,0)", "style": 20, "format": "0.00%"}, "D217": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E217": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F217": {"content": "=iferror(B217/D217,0)", "style": 20, "format": "0.00%"}, "G217": {"content": "=iferror(C217/E217,0)", "style": 20, "format": "0.00%"}, "D218": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E218": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F218": {"content": "=iferror(B218/D218,0)", "style": 20, "format": "0.00%"}, "G218": {"content": "=iferror(C218/E218,0)", "style": 20, "format": "0.00%"}, "D219": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E219": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F219": {"content": "=iferror(B219/D219,0)", "style": 20, "format": "0.00%"}, "G219": {"content": "=iferror(C219/E219,0)", "style": 20, "format": "0.00%"}, "D220": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E220": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F220": {"content": "=iferror(B220/D220,0)", "style": 20, "format": "0.00%"}, "G220": {"content": "=iferror(C220/E220,0)", "style": 20, "format": "0.00%"}, "J215": {"content": "", "style": 7}, "K215": {"content": "", "style": 7}, "L215": {"content": "", "style": 7}, "M215": {"content": "", "style": 7}, "N215": {"content": "", "style": 7}, "O215": {"content": "", "style": 7}, "P215": {"content": "", "style": 7}, "Q215": {"content": "", "style": 7}, "R215": {"content": "", "style": 7}, "S215": {"content": "", "style": 7}, "T215": {"content": "", "style": 7}, "U215": {"content": "", "style": 7}, "V215": {"content": "", "style": 7}, "A236": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A237": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "style": 2}, "A238": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "style": 2}, "A239": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "style": 2}, "A240": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "style": 2}, "A241": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "style": 2}, "B236": {"content": "=sum(B237:B256)", "style": 46, "format": "#,##0.00"}, "B237": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "B238": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "B239": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "B240": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "B241": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "C236": {"content": "=sum(C237:C256)", "style": 46, "format": "#,##0.00"}, "C237": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1),\"won_status\",\"won\")", "format": "#,##0.00"}, "C238": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2),\"won_status\",\"won\")", "format": "#,##0.00"}, "C239": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3),\"won_status\",\"won\")", "format": "#,##0.00"}, "C240": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4),\"won_status\",\"won\")", "format": "#,##0.00"}, "C241": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5),\"won_status\",\"won\")", "format": "#,##0.00"}, "H236": {"content": "=sum(H237:H256)", "style": 46, "format": "#,##0.00"}, "H237": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "H238": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "H239": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "H240": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "H241": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "I236": {"content": "=sum(I237:I256)", "style": 46, "format": "#,##0.00"}, "I237": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",1))", "format": "#,##0.00"}, "I238": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",2))", "format": "#,##0.00"}, "I239": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",3))", "format": "#,##0.00"}, "I240": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",4))", "format": "#,##0.00"}, "I241": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",5))", "format": "#,##0.00"}, "D236": {"content": "=sum(D237:D256)", "style": 46, "format": "#,##0.00"}, "E236": {"content": "=sum(E237:E256)", "style": 46, "format": "#,##0.00"}, "F236": {"content": "=iferror(B236/D236,0)", "style": 46, "format": "0.00%"}, "G236": {"content": "=iferror(C236/E236,0)", "style": 46, "format": "0.00%"}, "D237": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E237": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F237": {"content": "=iferror(B237/D237,0)", "style": 20, "format": "0.00%"}, "G237": {"content": "=iferror(C237/E237,0)", "style": 20, "format": "0.00%"}, "D238": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E238": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F238": {"content": "=iferror(B238/D238,0)", "style": 20, "format": "0.00%"}, "G238": {"content": "=iferror(C238/E238,0)", "style": 20, "format": "0.00%"}, "D239": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E239": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F239": {"content": "=iferror(B239/D239,0)", "style": 20, "format": "0.00%"}, "G239": {"content": "=iferror(C239/E239,0)", "style": 20, "format": "0.00%"}, "D240": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E240": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F240": {"content": "=iferror(B240/D240,0)", "style": 20, "format": "0.00%"}, "G240": {"content": "=iferror(C240/E240,0)", "style": 20, "format": "0.00%"}, "D241": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E241": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F241": {"content": "=iferror(B241/D241,0)", "style": 20, "format": "0.00%"}, "G241": {"content": "=iferror(C241/E241,0)", "style": 20, "format": "0.00%"}, "J236": {"content": "", "style": 7}, "K236": {"content": "", "style": 7}, "L236": {"content": "", "style": 7}, "M236": {"content": "", "style": 7}, "N236": {"content": "", "style": 7}, "O236": {"content": "", "style": 7}, "P236": {"content": "", "style": 7}, "Q236": {"content": "", "style": 7}, "R236": {"content": "", "style": 7}, "S236": {"content": "", "style": 7}, "T236": {"content": "", "style": 7}, "U236": {"content": "", "style": 7}, "V236": {"content": "", "style": 7}, "A257": {"content": "Total", "style": 3}, "B257": {"content": "=sum(B5,B26,B47,B68,B89,B110,B131,B152,B173,B194,B215,B236)", "style": 46, "format": "#,##0.00"}, "C257": {"content": "=sum(C5,C26,C47,C68,C89,C110,C131,C152,C173,C194,C215,C236)", "style": 46, "format": "#,##0.00"}, "H257": {"content": "=sum(H5,H26,H47,H68,H89,H110,H131,H152,H173,H194,H215,H236)", "style": 46, "format": "#,##0.00"}, "I257": {"content": "=sum(I5,I26,I47,I68,I89,I110,I131,I152,I173,I194,I215,I236)", "style": 46, "format": "#,##0.00"}, "D257": {"content": "=sum(D5,D26,D47,D68,D89,D110,D131,D152,D173,D194,D215,D236)", "style": 46, "format": "#,##0.00"}, "E257": {"content": "=sum(E5,E26,E47,E68,E89,E110,E131,E152,E173,E194,E215,E236)", "style": 46, "format": "#,##0.00"}, "F257": {"content": "=iferror(B257/D257,0)", "style": 46, "format": "0.00%"}, "G257": {"content": "=iferror(C257/E257,0)", "style": 46, "format": "0.00%"}, "K257": {"content": "", "style": 7}, "L257": {"content": "", "style": 7}, "M257": {"content": "", "style": 7}, "N257": {"content": "", "style": 7}, "O257": {"content": "", "style": 7}, "P257": {"content": "", "style": 7}, "Q257": {"content": "", "style": 7}, "R257": {"content": "", "style": 7}, "S257": {"content": "", "style": 7}, "T257": {"content": "", "style": 7}, "U257": {"content": "", "style": 7}, "V257": {"content": "", "style": 7}, "F258": {"content": "", "format": "0.00%"}, "G258": {"content": "", "format": "0.00%"}, "F259": {"content": "", "format": "0.00%"}, "G259": {"content": "", "format": "0.00%"}, "F260": {"content": "", "format": "0.00%"}, "G260": {"content": "", "format": "0.00%"}, "F261": {"content": "", "format": "0.00%"}, "G261": {"content": "", "format": "0.00%"}, "F262": {"content": "", "format": "0.00%"}, "G262": {"content": "", "format": "0.00%"}, "F263": {"content": "", "format": "0.00%"}, "G263": {"content": "", "format": "0.00%"}, "F264": {"content": "", "format": "0.00%"}, "G264": {"content": "", "format": "0.00%"}, "F265": {"content": "", "format": "0.00%"}, "G265": {"content": "", "format": "0.00%"}, "F266": {"content": "", "format": "0.00%"}, "G266": {"content": "", "format": "0.00%"}, "F267": {"content": "", "format": "0.00%"}, "G267": {"content": "", "format": "0.00%"}, "F268": {"content": "", "format": "0.00%"}, "G268": {"content": "", "format": "0.00%"}, "F269": {"content": "", "format": "0.00%"}, "G269": {"content": "", "format": "0.00%"}, "F270": {"content": "", "format": "0.00%"}, "G270": {"content": "", "format": "0.00%"}, "F271": {"content": "", "format": "0.00%"}, "G271": {"content": "", "format": "0.00%"}, "F272": {"content": "", "format": "0.00%"}, "G272": {"content": "", "format": "0.00%"}, "F273": {"content": "", "format": "0.00%"}, "G273": {"content": "", "format": "0.00%"}, "F274": {"content": "", "format": "0.00%"}, "G274": {"content": "", "format": "0.00%"}, "F275": {"content": "", "format": "0.00%"}, "G275": {"content": "", "format": "0.00%"}, "F276": {"content": "", "format": "0.00%"}, "G276": {"content": "", "format": "0.00%"}, "F277": {"content": "", "format": "0.00%"}, "G277": {"content": "", "format": "0.00%"}, "F278": {"content": "", "format": "0.00%"}, "G278": {"content": "", "format": "0.00%"}, "F279": {"content": "", "format": "0.00%"}, "G279": {"content": "", "format": "0.00%"}, "F280": {"content": "", "format": "0.00%"}, "G280": {"content": "", "format": "0.00%"}, "F281": {"content": "", "format": "0.00%"}, "G281": {"content": "", "format": "0.00%"}, "F282": {"content": "", "format": "0.00%"}, "G282": {"content": "", "format": "0.00%"}, "A242": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A243": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A244": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A245": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A246": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A247": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A248": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A249": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A250": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A251": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A252": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A253": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A254": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A255": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A256": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B242": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B243": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B244": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B245": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B246": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B247": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B248": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B249": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B250": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B251": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B252": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B253": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B254": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B255": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B256": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C242": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C243": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C244": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C245": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C246": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C247": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C248": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C249": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C250": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C251": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C252": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C253": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C254": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C255": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C256": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D242": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D243": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D244": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D245": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D246": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D247": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D248": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D249": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D250": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D251": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D252": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D253": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D254": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D255": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D256": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E242": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E243": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E244": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E245": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E246": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E247": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E248": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E249": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E250": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E251": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E252": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E253": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E254": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E255": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E256": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F242": {"content": "=iferror(B242/D242,0)", "style": 20, "format": "0.00%"}, "F243": {"content": "=iferror(B243/D243,0)", "style": 20, "format": "0.00%"}, "F244": {"content": "=iferror(B244/D244,0)", "style": 20, "format": "0.00%"}, "F245": {"content": "=iferror(B245/D245,0)", "style": 20, "format": "0.00%"}, "F246": {"content": "=iferror(B246/D246,0)", "style": 20, "format": "0.00%"}, "F247": {"content": "=iferror(B247/D247,0)", "style": 20, "format": "0.00%"}, "F248": {"content": "=iferror(B248/D248,0)", "style": 20, "format": "0.00%"}, "F249": {"content": "=iferror(B249/D249,0)", "style": 20, "format": "0.00%"}, "F250": {"content": "=iferror(B250/D250,0)", "style": 20, "format": "0.00%"}, "F251": {"content": "=iferror(B251/D251,0)", "style": 20, "format": "0.00%"}, "F252": {"content": "=iferror(B252/D252,0)", "style": 20, "format": "0.00%"}, "F253": {"content": "=iferror(B253/D253,0)", "style": 20, "format": "0.00%"}, "F254": {"content": "=iferror(B254/D254,0)", "style": 20, "format": "0.00%"}, "F255": {"content": "=iferror(B255/D255,0)", "style": 20, "format": "0.00%"}, "F256": {"content": "=iferror(B256/D256,0)", "style": 20, "format": "0.00%"}, "G242": {"content": "=iferror(C242/E242,0)", "style": 20, "format": "0.00%"}, "G243": {"content": "=iferror(C243/E243,0)", "style": 20, "format": "0.00%"}, "G244": {"content": "=iferror(C244/E244,0)", "style": 20, "format": "0.00%"}, "G245": {"content": "=iferror(C245/E245,0)", "style": 20, "format": "0.00%"}, "G246": {"content": "=iferror(C246/E246,0)", "style": 20, "format": "0.00%"}, "G247": {"content": "=iferror(C247/E247,0)", "style": 20, "format": "0.00%"}, "G248": {"content": "=iferror(C248/E248,0)", "style": 20, "format": "0.00%"}, "G249": {"content": "=iferror(C249/E249,0)", "style": 20, "format": "0.00%"}, "G250": {"content": "=iferror(C250/E250,0)", "style": 20, "format": "0.00%"}, "G251": {"content": "=iferror(C251/E251,0)", "style": 20, "format": "0.00%"}, "G252": {"content": "=iferror(C252/E252,0)", "style": 20, "format": "0.00%"}, "G253": {"content": "=iferror(C253/E253,0)", "style": 20, "format": "0.00%"}, "G254": {"content": "=iferror(C254/E254,0)", "style": 20, "format": "0.00%"}, "G255": {"content": "=iferror(C255/E255,0)", "style": 20, "format": "0.00%"}, "G256": {"content": "=iferror(C256/E256,0)", "style": 20, "format": "0.00%"}, "H242": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H243": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H244": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H245": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H246": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H247": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H248": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H249": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H250": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H251": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H252": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H253": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H254": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H255": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H256": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I242": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I243": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I244": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I245": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I246": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I247": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I248": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I249": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I250": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I251": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I252": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I253": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I254": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I255": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I256": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "K242": {"content": "", "style": 7}, "K243": {"content": "", "style": 7}, "K244": {"content": "", "style": 7}, "K245": {"content": "", "style": 7}, "K246": {"content": "", "style": 7}, "K247": {"content": "", "style": 7}, "K248": {"content": "", "style": 7}, "K249": {"content": "", "style": 7}, "K250": {"content": "", "style": 7}, "K251": {"content": "", "style": 7}, "K252": {"content": "", "style": 7}, "K253": {"content": "", "style": 7}, "K254": {"content": "", "style": 7}, "K255": {"content": "", "style": 7}, "K256": {"content": "", "style": 7}, "L242": {"content": "", "style": 7}, "L243": {"content": "", "style": 7}, "L244": {"content": "", "style": 7}, "L245": {"content": "", "style": 7}, "L246": {"content": "", "style": 7}, "L247": {"content": "", "style": 7}, "L248": {"content": "", "style": 7}, "L249": {"content": "", "style": 7}, "L250": {"content": "", "style": 7}, "L251": {"content": "", "style": 7}, "L252": {"content": "", "style": 7}, "L253": {"content": "", "style": 7}, "L254": {"content": "", "style": 7}, "L255": {"content": "", "style": 7}, "L256": {"content": "", "style": 7}, "M242": {"content": "", "style": 7}, "M243": {"content": "", "style": 7}, "M244": {"content": "", "style": 7}, "M245": {"content": "", "style": 7}, "M246": {"content": "", "style": 7}, "M247": {"content": "", "style": 7}, "M248": {"content": "", "style": 7}, "M249": {"content": "", "style": 7}, "M250": {"content": "", "style": 7}, "M251": {"content": "", "style": 7}, "M252": {"content": "", "style": 7}, "M253": {"content": "", "style": 7}, "M254": {"content": "", "style": 7}, "M255": {"content": "", "style": 7}, "M256": {"content": "", "style": 7}, "N242": {"content": "", "style": 7}, "N243": {"content": "", "style": 7}, "N244": {"content": "", "style": 7}, "N245": {"content": "", "style": 7}, "N246": {"content": "", "style": 7}, "N247": {"content": "", "style": 7}, "N248": {"content": "", "style": 7}, "N249": {"content": "", "style": 7}, "N250": {"content": "", "style": 7}, "N251": {"content": "", "style": 7}, "N252": {"content": "", "style": 7}, "N253": {"content": "", "style": 7}, "N254": {"content": "", "style": 7}, "N255": {"content": "", "style": 7}, "N256": {"content": "", "style": 7}, "O242": {"content": "", "style": 7}, "O243": {"content": "", "style": 7}, "O244": {"content": "", "style": 7}, "O245": {"content": "", "style": 7}, "O246": {"content": "", "style": 7}, "O247": {"content": "", "style": 7}, "O248": {"content": "", "style": 7}, "O249": {"content": "", "style": 7}, "O250": {"content": "", "style": 7}, "O251": {"content": "", "style": 7}, "O252": {"content": "", "style": 7}, "O253": {"content": "", "style": 7}, "O254": {"content": "", "style": 7}, "O255": {"content": "", "style": 7}, "O256": {"content": "", "style": 7}, "P242": {"content": "", "style": 7}, "P243": {"content": "", "style": 7}, "P244": {"content": "", "style": 7}, "P245": {"content": "", "style": 7}, "P246": {"content": "", "style": 7}, "P247": {"content": "", "style": 7}, "P248": {"content": "", "style": 7}, "P249": {"content": "", "style": 7}, "P250": {"content": "", "style": 7}, "P251": {"content": "", "style": 7}, "P252": {"content": "", "style": 7}, "P253": {"content": "", "style": 7}, "P254": {"content": "", "style": 7}, "P255": {"content": "", "style": 7}, "P256": {"content": "", "style": 7}, "Q242": {"content": "", "style": 7}, "Q243": {"content": "", "style": 7}, "Q244": {"content": "", "style": 7}, "Q245": {"content": "", "style": 7}, "Q246": {"content": "", "style": 7}, "Q247": {"content": "", "style": 7}, "Q248": {"content": "", "style": 7}, "Q249": {"content": "", "style": 7}, "Q250": {"content": "", "style": 7}, "Q251": {"content": "", "style": 7}, "Q252": {"content": "", "style": 7}, "Q253": {"content": "", "style": 7}, "Q254": {"content": "", "style": 7}, "Q255": {"content": "", "style": 7}, "Q256": {"content": "", "style": 7}, "R242": {"content": "", "style": 7}, "R243": {"content": "", "style": 7}, "R244": {"content": "", "style": 7}, "R245": {"content": "", "style": 7}, "R246": {"content": "", "style": 7}, "R247": {"content": "", "style": 7}, "R248": {"content": "", "style": 7}, "R249": {"content": "", "style": 7}, "R250": {"content": "", "style": 7}, "R251": {"content": "", "style": 7}, "R252": {"content": "", "style": 7}, "R253": {"content": "", "style": 7}, "R254": {"content": "", "style": 7}, "R255": {"content": "", "style": 7}, "R256": {"content": "", "style": 7}, "S242": {"content": "", "style": 7}, "S243": {"content": "", "style": 7}, "S244": {"content": "", "style": 7}, "S245": {"content": "", "style": 7}, "S246": {"content": "", "style": 7}, "S247": {"content": "", "style": 7}, "S248": {"content": "", "style": 7}, "S249": {"content": "", "style": 7}, "S250": {"content": "", "style": 7}, "S251": {"content": "", "style": 7}, "S252": {"content": "", "style": 7}, "S253": {"content": "", "style": 7}, "S254": {"content": "", "style": 7}, "S255": {"content": "", "style": 7}, "S256": {"content": "", "style": 7}, "T242": {"content": "", "style": 7}, "T243": {"content": "", "style": 7}, "T244": {"content": "", "style": 7}, "T245": {"content": "", "style": 7}, "T246": {"content": "", "style": 7}, "T247": {"content": "", "style": 7}, "T248": {"content": "", "style": 7}, "T249": {"content": "", "style": 7}, "T250": {"content": "", "style": 7}, "T251": {"content": "", "style": 7}, "T252": {"content": "", "style": 7}, "T253": {"content": "", "style": 7}, "T254": {"content": "", "style": 7}, "T255": {"content": "", "style": 7}, "T256": {"content": "", "style": 7}, "U242": {"content": "", "style": 7}, "U243": {"content": "", "style": 7}, "U244": {"content": "", "style": 7}, "U245": {"content": "", "style": 7}, "U246": {"content": "", "style": 7}, "U247": {"content": "", "style": 7}, "U248": {"content": "", "style": 7}, "U249": {"content": "", "style": 7}, "U250": {"content": "", "style": 7}, "U251": {"content": "", "style": 7}, "U252": {"content": "", "style": 7}, "U253": {"content": "", "style": 7}, "U254": {"content": "", "style": 7}, "U255": {"content": "", "style": 7}, "U256": {"content": "", "style": 7}, "V242": {"content": "", "style": 7}, "V243": {"content": "", "style": 7}, "V244": {"content": "", "style": 7}, "V245": {"content": "", "style": 7}, "V246": {"content": "", "style": 7}, "V247": {"content": "", "style": 7}, "V248": {"content": "", "style": 7}, "V249": {"content": "", "style": 7}, "V250": {"content": "", "style": 7}, "V251": {"content": "", "style": 7}, "V252": {"content": "", "style": 7}, "V253": {"content": "", "style": 7}, "V254": {"content": "", "style": 7}, "V255": {"content": "", "style": 7}, "V256": {"content": "", "style": 7}, "A221": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A222": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A223": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A224": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A225": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A226": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A227": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A228": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A229": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A230": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A231": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A232": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A233": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A234": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A235": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B221": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B222": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B223": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B224": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B225": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B226": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B227": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B228": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B229": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B230": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B231": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B232": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B233": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B234": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B235": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C221": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C222": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C223": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C224": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C225": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C226": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C227": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C228": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C229": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C230": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C231": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C232": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C233": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C234": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C235": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D221": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D222": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D223": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D224": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D225": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D226": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D227": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D228": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D229": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D230": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D231": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D232": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D233": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D234": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D235": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E221": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E222": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E223": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E224": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E225": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E226": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E227": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E228": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E229": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E230": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E231": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E232": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E233": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E234": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E235": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F221": {"content": "=iferror(B221/D221,0)", "style": 20, "format": "0.00%"}, "F222": {"content": "=iferror(B222/D222,0)", "style": 20, "format": "0.00%"}, "F223": {"content": "=iferror(B223/D223,0)", "style": 20, "format": "0.00%"}, "F224": {"content": "=iferror(B224/D224,0)", "style": 20, "format": "0.00%"}, "F225": {"content": "=iferror(B225/D225,0)", "style": 20, "format": "0.00%"}, "F226": {"content": "=iferror(B226/D226,0)", "style": 20, "format": "0.00%"}, "F227": {"content": "=iferror(B227/D227,0)", "style": 20, "format": "0.00%"}, "F228": {"content": "=iferror(B228/D228,0)", "style": 20, "format": "0.00%"}, "F229": {"content": "=iferror(B229/D229,0)", "style": 20, "format": "0.00%"}, "F230": {"content": "=iferror(B230/D230,0)", "style": 20, "format": "0.00%"}, "F231": {"content": "=iferror(B231/D231,0)", "style": 20, "format": "0.00%"}, "F232": {"content": "=iferror(B232/D232,0)", "style": 20, "format": "0.00%"}, "F233": {"content": "=iferror(B233/D233,0)", "style": 20, "format": "0.00%"}, "F234": {"content": "=iferror(B234/D234,0)", "style": 20, "format": "0.00%"}, "F235": {"content": "=iferror(B235/D235,0)", "style": 20, "format": "0.00%"}, "G221": {"content": "=iferror(C221/E221,0)", "style": 20, "format": "0.00%"}, "G222": {"content": "=iferror(C222/E222,0)", "style": 20, "format": "0.00%"}, "G223": {"content": "=iferror(C223/E223,0)", "style": 20, "format": "0.00%"}, "G224": {"content": "=iferror(C224/E224,0)", "style": 20, "format": "0.00%"}, "G225": {"content": "=iferror(C225/E225,0)", "style": 20, "format": "0.00%"}, "G226": {"content": "=iferror(C226/E226,0)", "style": 20, "format": "0.00%"}, "G227": {"content": "=iferror(C227/E227,0)", "style": 20, "format": "0.00%"}, "G228": {"content": "=iferror(C228/E228,0)", "style": 20, "format": "0.00%"}, "G229": {"content": "=iferror(C229/E229,0)", "style": 20, "format": "0.00%"}, "G230": {"content": "=iferror(C230/E230,0)", "style": 20, "format": "0.00%"}, "G231": {"content": "=iferror(C231/E231,0)", "style": 20, "format": "0.00%"}, "G232": {"content": "=iferror(C232/E232,0)", "style": 20, "format": "0.00%"}, "G233": {"content": "=iferror(C233/E233,0)", "style": 20, "format": "0.00%"}, "G234": {"content": "=iferror(C234/E234,0)", "style": 20, "format": "0.00%"}, "G235": {"content": "=iferror(C235/E235,0)", "style": 20, "format": "0.00%"}, "H221": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H222": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H223": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H224": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H225": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H226": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H227": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H228": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H229": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H230": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H231": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H232": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H233": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H234": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H235": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I221": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I222": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I223": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I224": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I225": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I226": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I227": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I228": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I229": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I230": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I231": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I232": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I233": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I234": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I235": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "K221": {"content": "", "style": 7}, "K222": {"content": "", "style": 7}, "K223": {"content": "", "style": 7}, "K224": {"content": "", "style": 7}, "K225": {"content": "", "style": 7}, "K226": {"content": "", "style": 7}, "K227": {"content": "", "style": 7}, "K228": {"content": "", "style": 7}, "K229": {"content": "", "style": 7}, "K230": {"content": "", "style": 7}, "K231": {"content": "", "style": 7}, "K232": {"content": "", "style": 7}, "K233": {"content": "", "style": 7}, "K234": {"content": "", "style": 7}, "K235": {"content": "", "style": 7}, "L221": {"content": "", "style": 7}, "L222": {"content": "", "style": 7}, "L223": {"content": "", "style": 7}, "L224": {"content": "", "style": 7}, "L225": {"content": "", "style": 7}, "L226": {"content": "", "style": 7}, "L227": {"content": "", "style": 7}, "L228": {"content": "", "style": 7}, "L229": {"content": "", "style": 7}, "L230": {"content": "", "style": 7}, "L231": {"content": "", "style": 7}, "L232": {"content": "", "style": 7}, "L233": {"content": "", "style": 7}, "L234": {"content": "", "style": 7}, "L235": {"content": "", "style": 7}, "M221": {"content": "", "style": 7}, "M222": {"content": "", "style": 7}, "M223": {"content": "", "style": 7}, "M224": {"content": "", "style": 7}, "M225": {"content": "", "style": 7}, "M226": {"content": "", "style": 7}, "M227": {"content": "", "style": 7}, "M228": {"content": "", "style": 7}, "M229": {"content": "", "style": 7}, "M230": {"content": "", "style": 7}, "M231": {"content": "", "style": 7}, "M232": {"content": "", "style": 7}, "M233": {"content": "", "style": 7}, "M234": {"content": "", "style": 7}, "M235": {"content": "", "style": 7}, "N221": {"content": "", "style": 7}, "N222": {"content": "", "style": 7}, "N223": {"content": "", "style": 7}, "N224": {"content": "", "style": 7}, "N225": {"content": "", "style": 7}, "N226": {"content": "", "style": 7}, "N227": {"content": "", "style": 7}, "N228": {"content": "", "style": 7}, "N229": {"content": "", "style": 7}, "N230": {"content": "", "style": 7}, "N231": {"content": "", "style": 7}, "N232": {"content": "", "style": 7}, "N233": {"content": "", "style": 7}, "N234": {"content": "", "style": 7}, "N235": {"content": "", "style": 7}, "O221": {"content": "", "style": 7}, "O222": {"content": "", "style": 7}, "O223": {"content": "", "style": 7}, "O224": {"content": "", "style": 7}, "O225": {"content": "", "style": 7}, "O226": {"content": "", "style": 7}, "O227": {"content": "", "style": 7}, "O228": {"content": "", "style": 7}, "O229": {"content": "", "style": 7}, "O230": {"content": "", "style": 7}, "O231": {"content": "", "style": 7}, "O232": {"content": "", "style": 7}, "O233": {"content": "", "style": 7}, "O234": {"content": "", "style": 7}, "O235": {"content": "", "style": 7}, "P221": {"content": "", "style": 7}, "P222": {"content": "", "style": 7}, "P223": {"content": "", "style": 7}, "P224": {"content": "", "style": 7}, "P225": {"content": "", "style": 7}, "P226": {"content": "", "style": 7}, "P227": {"content": "", "style": 7}, "P228": {"content": "", "style": 7}, "P229": {"content": "", "style": 7}, "P230": {"content": "", "style": 7}, "P231": {"content": "", "style": 7}, "P232": {"content": "", "style": 7}, "P233": {"content": "", "style": 7}, "P234": {"content": "", "style": 7}, "P235": {"content": "", "style": 7}, "Q221": {"content": "", "style": 7}, "Q222": {"content": "", "style": 7}, "Q223": {"content": "", "style": 7}, "Q224": {"content": "", "style": 7}, "Q225": {"content": "", "style": 7}, "Q226": {"content": "", "style": 7}, "Q227": {"content": "", "style": 7}, "Q228": {"content": "", "style": 7}, "Q229": {"content": "", "style": 7}, "Q230": {"content": "", "style": 7}, "Q231": {"content": "", "style": 7}, "Q232": {"content": "", "style": 7}, "Q233": {"content": "", "style": 7}, "Q234": {"content": "", "style": 7}, "Q235": {"content": "", "style": 7}, "R221": {"content": "", "style": 7}, "R222": {"content": "", "style": 7}, "R223": {"content": "", "style": 7}, "R224": {"content": "", "style": 7}, "R225": {"content": "", "style": 7}, "R226": {"content": "", "style": 7}, "R227": {"content": "", "style": 7}, "R228": {"content": "", "style": 7}, "R229": {"content": "", "style": 7}, "R230": {"content": "", "style": 7}, "R231": {"content": "", "style": 7}, "R232": {"content": "", "style": 7}, "R233": {"content": "", "style": 7}, "R234": {"content": "", "style": 7}, "R235": {"content": "", "style": 7}, "S221": {"content": "", "style": 7}, "S222": {"content": "", "style": 7}, "S223": {"content": "", "style": 7}, "S224": {"content": "", "style": 7}, "S225": {"content": "", "style": 7}, "S226": {"content": "", "style": 7}, "S227": {"content": "", "style": 7}, "S228": {"content": "", "style": 7}, "S229": {"content": "", "style": 7}, "S230": {"content": "", "style": 7}, "S231": {"content": "", "style": 7}, "S232": {"content": "", "style": 7}, "S233": {"content": "", "style": 7}, "S234": {"content": "", "style": 7}, "S235": {"content": "", "style": 7}, "T221": {"content": "", "style": 7}, "T222": {"content": "", "style": 7}, "T223": {"content": "", "style": 7}, "T224": {"content": "", "style": 7}, "T225": {"content": "", "style": 7}, "T226": {"content": "", "style": 7}, "T227": {"content": "", "style": 7}, "T228": {"content": "", "style": 7}, "T229": {"content": "", "style": 7}, "T230": {"content": "", "style": 7}, "T231": {"content": "", "style": 7}, "T232": {"content": "", "style": 7}, "T233": {"content": "", "style": 7}, "T234": {"content": "", "style": 7}, "T235": {"content": "", "style": 7}, "U221": {"content": "", "style": 7}, "U222": {"content": "", "style": 7}, "U223": {"content": "", "style": 7}, "U224": {"content": "", "style": 7}, "U225": {"content": "", "style": 7}, "U226": {"content": "", "style": 7}, "U227": {"content": "", "style": 7}, "U228": {"content": "", "style": 7}, "U229": {"content": "", "style": 7}, "U230": {"content": "", "style": 7}, "U231": {"content": "", "style": 7}, "U232": {"content": "", "style": 7}, "U233": {"content": "", "style": 7}, "U234": {"content": "", "style": 7}, "U235": {"content": "", "style": 7}, "V221": {"content": "", "style": 7}, "V222": {"content": "", "style": 7}, "V223": {"content": "", "style": 7}, "V224": {"content": "", "style": 7}, "V225": {"content": "", "style": 7}, "V226": {"content": "", "style": 7}, "V227": {"content": "", "style": 7}, "V228": {"content": "", "style": 7}, "V229": {"content": "", "style": 7}, "V230": {"content": "", "style": 7}, "V231": {"content": "", "style": 7}, "V232": {"content": "", "style": 7}, "V233": {"content": "", "style": 7}, "V234": {"content": "", "style": 7}, "V235": {"content": "", "style": 7}, "A200": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A201": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A202": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A203": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A204": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A205": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A206": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A207": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A208": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A209": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A210": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A211": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A212": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A213": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A214": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B200": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B201": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B202": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B203": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B204": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B205": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B206": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B207": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B208": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B209": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B210": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B211": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B212": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B213": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B214": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C200": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C201": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C202": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C203": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C204": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C205": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C206": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C207": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C208": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C209": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C210": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C211": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C212": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C213": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C214": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D200": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D201": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D202": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D203": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D204": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D205": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D206": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D207": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D208": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D209": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D210": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D211": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D212": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D213": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D214": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E200": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E201": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E202": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E203": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E204": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E205": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E206": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E207": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E208": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E209": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E210": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E211": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E212": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E213": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E214": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F200": {"content": "=iferror(B200/D200,0)", "style": 20, "format": "0.00%"}, "F201": {"content": "=iferror(B201/D201,0)", "style": 20, "format": "0.00%"}, "F202": {"content": "=iferror(B202/D202,0)", "style": 20, "format": "0.00%"}, "F203": {"content": "=iferror(B203/D203,0)", "style": 20, "format": "0.00%"}, "F204": {"content": "=iferror(B204/D204,0)", "style": 20, "format": "0.00%"}, "F205": {"content": "=iferror(B205/D205,0)", "style": 20, "format": "0.00%"}, "F206": {"content": "=iferror(B206/D206,0)", "style": 20, "format": "0.00%"}, "F207": {"content": "=iferror(B207/D207,0)", "style": 20, "format": "0.00%"}, "F208": {"content": "=iferror(B208/D208,0)", "style": 20, "format": "0.00%"}, "F209": {"content": "=iferror(B209/D209,0)", "style": 20, "format": "0.00%"}, "F210": {"content": "=iferror(B210/D210,0)", "style": 20, "format": "0.00%"}, "F211": {"content": "=iferror(B211/D211,0)", "style": 20, "format": "0.00%"}, "F212": {"content": "=iferror(B212/D212,0)", "style": 20, "format": "0.00%"}, "F213": {"content": "=iferror(B213/D213,0)", "style": 20, "format": "0.00%"}, "F214": {"content": "=iferror(B214/D214,0)", "style": 20, "format": "0.00%"}, "G200": {"content": "=iferror(C200/E200,0)", "style": 20, "format": "0.00%"}, "G201": {"content": "=iferror(C201/E201,0)", "style": 20, "format": "0.00%"}, "G202": {"content": "=iferror(C202/E202,0)", "style": 20, "format": "0.00%"}, "G203": {"content": "=iferror(C203/E203,0)", "style": 20, "format": "0.00%"}, "G204": {"content": "=iferror(C204/E204,0)", "style": 20, "format": "0.00%"}, "G205": {"content": "=iferror(C205/E205,0)", "style": 20, "format": "0.00%"}, "G206": {"content": "=iferror(C206/E206,0)", "style": 20, "format": "0.00%"}, "G207": {"content": "=iferror(C207/E207,0)", "style": 20, "format": "0.00%"}, "G208": {"content": "=iferror(C208/E208,0)", "style": 20, "format": "0.00%"}, "G209": {"content": "=iferror(C209/E209,0)", "style": 20, "format": "0.00%"}, "G210": {"content": "=iferror(C210/E210,0)", "style": 20, "format": "0.00%"}, "G211": {"content": "=iferror(C211/E211,0)", "style": 20, "format": "0.00%"}, "G212": {"content": "=iferror(C212/E212,0)", "style": 20, "format": "0.00%"}, "G213": {"content": "=iferror(C213/E213,0)", "style": 20, "format": "0.00%"}, "G214": {"content": "=iferror(C214/E214,0)", "style": 20, "format": "0.00%"}, "H200": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H201": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H202": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H203": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H204": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H205": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H206": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H207": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H208": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H209": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H210": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H211": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H212": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H213": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H214": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I200": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I201": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I202": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I203": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I204": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I205": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I206": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I207": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I208": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I209": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I210": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I211": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I212": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I213": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I214": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J200": {"content": "", "style": 7}, "J201": {"content": "", "style": 7}, "J202": {"content": "", "style": 7}, "J203": {"content": "", "style": 7}, "J204": {"content": "", "style": 7}, "J205": {"content": "", "style": 7}, "J206": {"content": "", "style": 7}, "J207": {"content": "", "style": 7}, "J208": {"content": "", "style": 7}, "J209": {"content": "", "style": 7}, "J210": {"content": "", "style": 7}, "J211": {"content": "", "style": 7}, "J212": {"content": "", "style": 7}, "J213": {"content": "", "style": 7}, "J214": {"content": "", "style": 7}, "K200": {"content": "", "style": 7}, "K201": {"content": "", "style": 7}, "K202": {"content": "", "style": 7}, "K203": {"content": "", "style": 7}, "K204": {"content": "", "style": 7}, "K205": {"content": "", "style": 7}, "K206": {"content": "", "style": 7}, "K207": {"content": "", "style": 7}, "K208": {"content": "", "style": 7}, "K209": {"content": "", "style": 7}, "K210": {"content": "", "style": 7}, "K211": {"content": "", "style": 7}, "K212": {"content": "", "style": 7}, "K213": {"content": "", "style": 7}, "K214": {"content": "", "style": 7}, "L200": {"content": "", "style": 7}, "L201": {"content": "", "style": 7}, "L202": {"content": "", "style": 7}, "L203": {"content": "", "style": 7}, "L204": {"content": "", "style": 7}, "L205": {"content": "", "style": 7}, "L206": {"content": "", "style": 7}, "L207": {"content": "", "style": 7}, "L208": {"content": "", "style": 7}, "L209": {"content": "", "style": 7}, "L210": {"content": "", "style": 7}, "L211": {"content": "", "style": 7}, "L212": {"content": "", "style": 7}, "L213": {"content": "", "style": 7}, "L214": {"content": "", "style": 7}, "M200": {"content": "", "style": 7}, "M201": {"content": "", "style": 7}, "M202": {"content": "", "style": 7}, "M203": {"content": "", "style": 7}, "M204": {"content": "", "style": 7}, "M205": {"content": "", "style": 7}, "M206": {"content": "", "style": 7}, "M207": {"content": "", "style": 7}, "M208": {"content": "", "style": 7}, "M209": {"content": "", "style": 7}, "M210": {"content": "", "style": 7}, "M211": {"content": "", "style": 7}, "M212": {"content": "", "style": 7}, "M213": {"content": "", "style": 7}, "M214": {"content": "", "style": 7}, "N200": {"content": "", "style": 7}, "N201": {"content": "", "style": 7}, "N202": {"content": "", "style": 7}, "N203": {"content": "", "style": 7}, "N204": {"content": "", "style": 7}, "N205": {"content": "", "style": 7}, "N206": {"content": "", "style": 7}, "N207": {"content": "", "style": 7}, "N208": {"content": "", "style": 7}, "N209": {"content": "", "style": 7}, "N210": {"content": "", "style": 7}, "N211": {"content": "", "style": 7}, "N212": {"content": "", "style": 7}, "N213": {"content": "", "style": 7}, "N214": {"content": "", "style": 7}, "O200": {"content": "", "style": 7}, "O201": {"content": "", "style": 7}, "O202": {"content": "", "style": 7}, "O203": {"content": "", "style": 7}, "O204": {"content": "", "style": 7}, "O205": {"content": "", "style": 7}, "O206": {"content": "", "style": 7}, "O207": {"content": "", "style": 7}, "O208": {"content": "", "style": 7}, "O209": {"content": "", "style": 7}, "O210": {"content": "", "style": 7}, "O211": {"content": "", "style": 7}, "O212": {"content": "", "style": 7}, "O213": {"content": "", "style": 7}, "O214": {"content": "", "style": 7}, "P200": {"content": "", "style": 7}, "P201": {"content": "", "style": 7}, "P202": {"content": "", "style": 7}, "P203": {"content": "", "style": 7}, "P204": {"content": "", "style": 7}, "P205": {"content": "", "style": 7}, "P206": {"content": "", "style": 7}, "P207": {"content": "", "style": 7}, "P208": {"content": "", "style": 7}, "P209": {"content": "", "style": 7}, "P210": {"content": "", "style": 7}, "P211": {"content": "", "style": 7}, "P212": {"content": "", "style": 7}, "P213": {"content": "", "style": 7}, "P214": {"content": "", "style": 7}, "Q200": {"content": "", "style": 7}, "Q201": {"content": "", "style": 7}, "Q202": {"content": "", "style": 7}, "Q203": {"content": "", "style": 7}, "Q204": {"content": "", "style": 7}, "Q205": {"content": "", "style": 7}, "Q206": {"content": "", "style": 7}, "Q207": {"content": "", "style": 7}, "Q208": {"content": "", "style": 7}, "Q209": {"content": "", "style": 7}, "Q210": {"content": "", "style": 7}, "Q211": {"content": "", "style": 7}, "Q212": {"content": "", "style": 7}, "Q213": {"content": "", "style": 7}, "Q214": {"content": "", "style": 7}, "R200": {"content": "", "style": 7}, "R201": {"content": "", "style": 7}, "R202": {"content": "", "style": 7}, "R203": {"content": "", "style": 7}, "R204": {"content": "", "style": 7}, "R205": {"content": "", "style": 7}, "R206": {"content": "", "style": 7}, "R207": {"content": "", "style": 7}, "R208": {"content": "", "style": 7}, "R209": {"content": "", "style": 7}, "R210": {"content": "", "style": 7}, "R211": {"content": "", "style": 7}, "R212": {"content": "", "style": 7}, "R213": {"content": "", "style": 7}, "R214": {"content": "", "style": 7}, "S200": {"content": "", "style": 7}, "S201": {"content": "", "style": 7}, "S202": {"content": "", "style": 7}, "S203": {"content": "", "style": 7}, "S204": {"content": "", "style": 7}, "S205": {"content": "", "style": 7}, "S206": {"content": "", "style": 7}, "S207": {"content": "", "style": 7}, "S208": {"content": "", "style": 7}, "S209": {"content": "", "style": 7}, "S210": {"content": "", "style": 7}, "S211": {"content": "", "style": 7}, "S212": {"content": "", "style": 7}, "S213": {"content": "", "style": 7}, "S214": {"content": "", "style": 7}, "T200": {"content": "", "style": 7}, "T201": {"content": "", "style": 7}, "T202": {"content": "", "style": 7}, "T203": {"content": "", "style": 7}, "T204": {"content": "", "style": 7}, "T205": {"content": "", "style": 7}, "T206": {"content": "", "style": 7}, "T207": {"content": "", "style": 7}, "T208": {"content": "", "style": 7}, "T209": {"content": "", "style": 7}, "T210": {"content": "", "style": 7}, "T211": {"content": "", "style": 7}, "T212": {"content": "", "style": 7}, "T213": {"content": "", "style": 7}, "T214": {"content": "", "style": 7}, "U200": {"content": "", "style": 7}, "U201": {"content": "", "style": 7}, "U202": {"content": "", "style": 7}, "U203": {"content": "", "style": 7}, "U204": {"content": "", "style": 7}, "U205": {"content": "", "style": 7}, "U206": {"content": "", "style": 7}, "U207": {"content": "", "style": 7}, "U208": {"content": "", "style": 7}, "U209": {"content": "", "style": 7}, "U210": {"content": "", "style": 7}, "U211": {"content": "", "style": 7}, "U212": {"content": "", "style": 7}, "U213": {"content": "", "style": 7}, "U214": {"content": "", "style": 7}, "V200": {"content": "", "style": 7}, "V201": {"content": "", "style": 7}, "V202": {"content": "", "style": 7}, "V203": {"content": "", "style": 7}, "V204": {"content": "", "style": 7}, "V205": {"content": "", "style": 7}, "V206": {"content": "", "style": 7}, "V207": {"content": "", "style": 7}, "V208": {"content": "", "style": 7}, "V209": {"content": "", "style": 7}, "V210": {"content": "", "style": 7}, "V211": {"content": "", "style": 7}, "V212": {"content": "", "style": 7}, "V213": {"content": "", "style": 7}, "V214": {"content": "", "style": 7}, "A179": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A180": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A181": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A182": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A183": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A184": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A185": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A186": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A187": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A188": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A189": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A190": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A191": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A192": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A193": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B179": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B180": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B181": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B182": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B183": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B184": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B185": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B186": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B187": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B188": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B189": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B190": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B191": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B192": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B193": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C179": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C180": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C181": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C182": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C183": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C184": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C185": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C186": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C187": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C188": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C189": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C190": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C191": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C192": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C193": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D179": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D180": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D181": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D182": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D183": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D184": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D185": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D186": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D187": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D188": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D189": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D190": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D191": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D192": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D193": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E179": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E180": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E181": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E182": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E183": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E184": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E185": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E186": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E187": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E188": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E189": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E190": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E191": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E192": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E193": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F179": {"content": "=iferror(B179/D179,0)", "style": 20, "format": "0.00%"}, "F180": {"content": "=iferror(B180/D180,0)", "style": 20, "format": "0.00%"}, "F181": {"content": "=iferror(B181/D181,0)", "style": 20, "format": "0.00%"}, "F182": {"content": "=iferror(B182/D182,0)", "style": 20, "format": "0.00%"}, "F183": {"content": "=iferror(B183/D183,0)", "style": 20, "format": "0.00%"}, "F184": {"content": "=iferror(B184/D184,0)", "style": 20, "format": "0.00%"}, "F185": {"content": "=iferror(B185/D185,0)", "style": 20, "format": "0.00%"}, "F186": {"content": "=iferror(B186/D186,0)", "style": 20, "format": "0.00%"}, "F187": {"content": "=iferror(B187/D187,0)", "style": 20, "format": "0.00%"}, "F188": {"content": "=iferror(B188/D188,0)", "style": 20, "format": "0.00%"}, "F189": {"content": "=iferror(B189/D189,0)", "style": 20, "format": "0.00%"}, "F190": {"content": "=iferror(B190/D190,0)", "style": 20, "format": "0.00%"}, "F191": {"content": "=iferror(B191/D191,0)", "style": 20, "format": "0.00%"}, "F192": {"content": "=iferror(B192/D192,0)", "style": 20, "format": "0.00%"}, "F193": {"content": "=iferror(B193/D193,0)", "style": 20, "format": "0.00%"}, "G179": {"content": "=iferror(C179/E179,0)", "style": 20, "format": "0.00%"}, "G180": {"content": "=iferror(C180/E180,0)", "style": 20, "format": "0.00%"}, "G181": {"content": "=iferror(C181/E181,0)", "style": 20, "format": "0.00%"}, "G182": {"content": "=iferror(C182/E182,0)", "style": 20, "format": "0.00%"}, "G183": {"content": "=iferror(C183/E183,0)", "style": 20, "format": "0.00%"}, "G184": {"content": "=iferror(C184/E184,0)", "style": 20, "format": "0.00%"}, "G185": {"content": "=iferror(C185/E185,0)", "style": 20, "format": "0.00%"}, "G186": {"content": "=iferror(C186/E186,0)", "style": 20, "format": "0.00%"}, "G187": {"content": "=iferror(C187/E187,0)", "style": 20, "format": "0.00%"}, "G188": {"content": "=iferror(C188/E188,0)", "style": 20, "format": "0.00%"}, "G189": {"content": "=iferror(C189/E189,0)", "style": 20, "format": "0.00%"}, "G190": {"content": "=iferror(C190/E190,0)", "style": 20, "format": "0.00%"}, "G191": {"content": "=iferror(C191/E191,0)", "style": 20, "format": "0.00%"}, "G192": {"content": "=iferror(C192/E192,0)", "style": 20, "format": "0.00%"}, "G193": {"content": "=iferror(C193/E193,0)", "style": 20, "format": "0.00%"}, "H179": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H180": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H181": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H182": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H183": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H184": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H185": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H186": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H187": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H188": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H189": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H190": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H191": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H192": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H193": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I179": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I180": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I181": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I182": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I183": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I184": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I185": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I186": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I187": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I188": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I189": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I190": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I191": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I192": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I193": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J179": {"content": "", "style": 7}, "J180": {"content": "", "style": 7}, "J181": {"content": "", "style": 7}, "J182": {"content": "", "style": 7}, "J183": {"content": "", "style": 7}, "J184": {"content": "", "style": 7}, "J185": {"content": "", "style": 7}, "J186": {"content": "", "style": 7}, "J187": {"content": "", "style": 7}, "J188": {"content": "", "style": 7}, "J189": {"content": "", "style": 7}, "J190": {"content": "", "style": 7}, "J191": {"content": "", "style": 7}, "J192": {"content": "", "style": 7}, "J193": {"content": "", "style": 7}, "K179": {"content": "", "style": 7}, "K180": {"content": "", "style": 7}, "K181": {"content": "", "style": 7}, "K182": {"content": "", "style": 7}, "K183": {"content": "", "style": 7}, "K184": {"content": "", "style": 7}, "K185": {"content": "", "style": 7}, "K186": {"content": "", "style": 7}, "K187": {"content": "", "style": 7}, "K188": {"content": "", "style": 7}, "K189": {"content": "", "style": 7}, "K190": {"content": "", "style": 7}, "K191": {"content": "", "style": 7}, "K192": {"content": "", "style": 7}, "K193": {"content": "", "style": 7}, "L179": {"content": "", "style": 7}, "L180": {"content": "", "style": 7}, "L181": {"content": "", "style": 7}, "L182": {"content": "", "style": 7}, "L183": {"content": "", "style": 7}, "L184": {"content": "", "style": 7}, "L185": {"content": "", "style": 7}, "L186": {"content": "", "style": 7}, "L187": {"content": "", "style": 7}, "L188": {"content": "", "style": 7}, "L189": {"content": "", "style": 7}, "L190": {"content": "", "style": 7}, "L191": {"content": "", "style": 7}, "L192": {"content": "", "style": 7}, "L193": {"content": "", "style": 7}, "M179": {"content": "", "style": 7}, "M180": {"content": "", "style": 7}, "M181": {"content": "", "style": 7}, "M182": {"content": "", "style": 7}, "M183": {"content": "", "style": 7}, "M184": {"content": "", "style": 7}, "M185": {"content": "", "style": 7}, "M186": {"content": "", "style": 7}, "M187": {"content": "", "style": 7}, "M188": {"content": "", "style": 7}, "M189": {"content": "", "style": 7}, "M190": {"content": "", "style": 7}, "M191": {"content": "", "style": 7}, "M192": {"content": "", "style": 7}, "M193": {"content": "", "style": 7}, "N179": {"content": "", "style": 7}, "N180": {"content": "", "style": 7}, "N181": {"content": "", "style": 7}, "N182": {"content": "", "style": 7}, "N183": {"content": "", "style": 7}, "N184": {"content": "", "style": 7}, "N185": {"content": "", "style": 7}, "N186": {"content": "", "style": 7}, "N187": {"content": "", "style": 7}, "N188": {"content": "", "style": 7}, "N189": {"content": "", "style": 7}, "N190": {"content": "", "style": 7}, "N191": {"content": "", "style": 7}, "N192": {"content": "", "style": 7}, "N193": {"content": "", "style": 7}, "O179": {"content": "", "style": 7}, "O180": {"content": "", "style": 7}, "O181": {"content": "", "style": 7}, "O182": {"content": "", "style": 7}, "O183": {"content": "", "style": 7}, "O184": {"content": "", "style": 7}, "O185": {"content": "", "style": 7}, "O186": {"content": "", "style": 7}, "O187": {"content": "", "style": 7}, "O188": {"content": "", "style": 7}, "O189": {"content": "", "style": 7}, "O190": {"content": "", "style": 7}, "O191": {"content": "", "style": 7}, "O192": {"content": "", "style": 7}, "O193": {"content": "", "style": 7}, "P179": {"content": "", "style": 7}, "P180": {"content": "", "style": 7}, "P181": {"content": "", "style": 7}, "P182": {"content": "", "style": 7}, "P183": {"content": "", "style": 7}, "P184": {"content": "", "style": 7}, "P185": {"content": "", "style": 7}, "P186": {"content": "", "style": 7}, "P187": {"content": "", "style": 7}, "P188": {"content": "", "style": 7}, "P189": {"content": "", "style": 7}, "P190": {"content": "", "style": 7}, "P191": {"content": "", "style": 7}, "P192": {"content": "", "style": 7}, "P193": {"content": "", "style": 7}, "Q179": {"content": "", "style": 7}, "Q180": {"content": "", "style": 7}, "Q181": {"content": "", "style": 7}, "Q182": {"content": "", "style": 7}, "Q183": {"content": "", "style": 7}, "Q184": {"content": "", "style": 7}, "Q185": {"content": "", "style": 7}, "Q186": {"content": "", "style": 7}, "Q187": {"content": "", "style": 7}, "Q188": {"content": "", "style": 7}, "Q189": {"content": "", "style": 7}, "Q190": {"content": "", "style": 7}, "Q191": {"content": "", "style": 7}, "Q192": {"content": "", "style": 7}, "Q193": {"content": "", "style": 7}, "R179": {"content": "", "style": 7}, "R180": {"content": "", "style": 7}, "R181": {"content": "", "style": 7}, "R182": {"content": "", "style": 7}, "R183": {"content": "", "style": 7}, "R184": {"content": "", "style": 7}, "R185": {"content": "", "style": 7}, "R186": {"content": "", "style": 7}, "R187": {"content": "", "style": 7}, "R188": {"content": "", "style": 7}, "R189": {"content": "", "style": 7}, "R190": {"content": "", "style": 7}, "R191": {"content": "", "style": 7}, "R192": {"content": "", "style": 7}, "R193": {"content": "", "style": 7}, "S179": {"content": "", "style": 7}, "S180": {"content": "", "style": 7}, "S181": {"content": "", "style": 7}, "S182": {"content": "", "style": 7}, "S183": {"content": "", "style": 7}, "S184": {"content": "", "style": 7}, "S185": {"content": "", "style": 7}, "S186": {"content": "", "style": 7}, "S187": {"content": "", "style": 7}, "S188": {"content": "", "style": 7}, "S189": {"content": "", "style": 7}, "S190": {"content": "", "style": 7}, "S191": {"content": "", "style": 7}, "S192": {"content": "", "style": 7}, "S193": {"content": "", "style": 7}, "T179": {"content": "", "style": 7}, "T180": {"content": "", "style": 7}, "T181": {"content": "", "style": 7}, "T182": {"content": "", "style": 7}, "T183": {"content": "", "style": 7}, "T184": {"content": "", "style": 7}, "T185": {"content": "", "style": 7}, "T186": {"content": "", "style": 7}, "T187": {"content": "", "style": 7}, "T188": {"content": "", "style": 7}, "T189": {"content": "", "style": 7}, "T190": {"content": "", "style": 7}, "T191": {"content": "", "style": 7}, "T192": {"content": "", "style": 7}, "T193": {"content": "", "style": 7}, "U179": {"content": "", "style": 7}, "U180": {"content": "", "style": 7}, "U181": {"content": "", "style": 7}, "U182": {"content": "", "style": 7}, "U183": {"content": "", "style": 7}, "U184": {"content": "", "style": 7}, "U185": {"content": "", "style": 7}, "U186": {"content": "", "style": 7}, "U187": {"content": "", "style": 7}, "U188": {"content": "", "style": 7}, "U189": {"content": "", "style": 7}, "U190": {"content": "", "style": 7}, "U191": {"content": "", "style": 7}, "U192": {"content": "", "style": 7}, "U193": {"content": "", "style": 7}, "V179": {"content": "", "style": 7}, "V180": {"content": "", "style": 7}, "V181": {"content": "", "style": 7}, "V182": {"content": "", "style": 7}, "V183": {"content": "", "style": 7}, "V184": {"content": "", "style": 7}, "V185": {"content": "", "style": 7}, "V186": {"content": "", "style": 7}, "V187": {"content": "", "style": 7}, "V188": {"content": "", "style": 7}, "V189": {"content": "", "style": 7}, "V190": {"content": "", "style": 7}, "V191": {"content": "", "style": 7}, "V192": {"content": "", "style": 7}, "V193": {"content": "", "style": 7}, "A158": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A159": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A160": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A161": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A162": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A163": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A164": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A165": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A166": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A167": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A168": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A169": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A170": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A171": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A172": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B158": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B159": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B160": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B161": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B162": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B163": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B164": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B165": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B166": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B167": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B168": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B169": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B170": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B171": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B172": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C158": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C159": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C160": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C161": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C162": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C163": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C164": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C165": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C166": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C167": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C168": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C169": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C170": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C171": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C172": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D158": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D159": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D160": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D161": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D162": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D163": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D164": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D165": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D166": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D167": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D168": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D169": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D170": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D171": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D172": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E158": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E159": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E160": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E161": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E162": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E163": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E164": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E165": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E166": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E167": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E168": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E169": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E170": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E171": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E172": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F158": {"content": "=iferror(B158/D158,0)", "style": 20, "format": "0.00%"}, "F159": {"content": "=iferror(B159/D159,0)", "style": 20, "format": "0.00%"}, "F160": {"content": "=iferror(B160/D160,0)", "style": 20, "format": "0.00%"}, "F161": {"content": "=iferror(B161/D161,0)", "style": 20, "format": "0.00%"}, "F162": {"content": "=iferror(B162/D162,0)", "style": 20, "format": "0.00%"}, "F163": {"content": "=iferror(B163/D163,0)", "style": 20, "format": "0.00%"}, "F164": {"content": "=iferror(B164/D164,0)", "style": 20, "format": "0.00%"}, "F165": {"content": "=iferror(B165/D165,0)", "style": 20, "format": "0.00%"}, "F166": {"content": "=iferror(B166/D166,0)", "style": 20, "format": "0.00%"}, "F167": {"content": "=iferror(B167/D167,0)", "style": 20, "format": "0.00%"}, "F168": {"content": "=iferror(B168/D168,0)", "style": 20, "format": "0.00%"}, "F169": {"content": "=iferror(B169/D169,0)", "style": 20, "format": "0.00%"}, "F170": {"content": "=iferror(B170/D170,0)", "style": 20, "format": "0.00%"}, "F171": {"content": "=iferror(B171/D171,0)", "style": 20, "format": "0.00%"}, "F172": {"content": "=iferror(B172/D172,0)", "style": 20, "format": "0.00%"}, "G158": {"content": "=iferror(C158/E158,0)", "style": 20, "format": "0.00%"}, "G159": {"content": "=iferror(C159/E159,0)", "style": 20, "format": "0.00%"}, "G160": {"content": "=iferror(C160/E160,0)", "style": 20, "format": "0.00%"}, "G161": {"content": "=iferror(C161/E161,0)", "style": 20, "format": "0.00%"}, "G162": {"content": "=iferror(C162/E162,0)", "style": 20, "format": "0.00%"}, "G163": {"content": "=iferror(C163/E163,0)", "style": 20, "format": "0.00%"}, "G164": {"content": "=iferror(C164/E164,0)", "style": 20, "format": "0.00%"}, "G165": {"content": "=iferror(C165/E165,0)", "style": 20, "format": "0.00%"}, "G166": {"content": "=iferror(C166/E166,0)", "style": 20, "format": "0.00%"}, "G167": {"content": "=iferror(C167/E167,0)", "style": 20, "format": "0.00%"}, "G168": {"content": "=iferror(C168/E168,0)", "style": 20, "format": "0.00%"}, "G169": {"content": "=iferror(C169/E169,0)", "style": 20, "format": "0.00%"}, "G170": {"content": "=iferror(C170/E170,0)", "style": 20, "format": "0.00%"}, "G171": {"content": "=iferror(C171/E171,0)", "style": 20, "format": "0.00%"}, "G172": {"content": "=iferror(C172/E172,0)", "style": 20, "format": "0.00%"}, "H158": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H159": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H160": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H161": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H162": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H163": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H164": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H165": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H166": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H167": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H168": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H169": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H170": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H171": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H172": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I158": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I159": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I160": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I161": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I162": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I163": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I164": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I165": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I166": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I167": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I168": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I169": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I170": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I171": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I172": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J158": {"content": "", "style": 7}, "J159": {"content": "", "style": 7}, "J160": {"content": "", "style": 7}, "J161": {"content": "", "style": 7}, "J162": {"content": "", "style": 7}, "J163": {"content": "", "style": 7}, "J164": {"content": "", "style": 7}, "J165": {"content": "", "style": 7}, "J166": {"content": "", "style": 7}, "J167": {"content": "", "style": 7}, "J168": {"content": "", "style": 7}, "J169": {"content": "", "style": 7}, "J170": {"content": "", "style": 7}, "J171": {"content": "", "style": 7}, "J172": {"content": "", "style": 7}, "K158": {"content": "", "style": 7}, "K159": {"content": "", "style": 7}, "K160": {"content": "", "style": 7}, "K161": {"content": "", "style": 7}, "K162": {"content": "", "style": 7}, "K163": {"content": "", "style": 7}, "K164": {"content": "", "style": 7}, "K165": {"content": "", "style": 7}, "K166": {"content": "", "style": 7}, "K167": {"content": "", "style": 7}, "K168": {"content": "", "style": 7}, "K169": {"content": "", "style": 7}, "K170": {"content": "", "style": 7}, "K171": {"content": "", "style": 7}, "K172": {"content": "", "style": 7}, "L158": {"content": "", "style": 7}, "L159": {"content": "", "style": 7}, "L160": {"content": "", "style": 7}, "L161": {"content": "", "style": 7}, "L162": {"content": "", "style": 7}, "L163": {"content": "", "style": 7}, "L164": {"content": "", "style": 7}, "L165": {"content": "", "style": 7}, "L166": {"content": "", "style": 7}, "L167": {"content": "", "style": 7}, "L168": {"content": "", "style": 7}, "L169": {"content": "", "style": 7}, "L170": {"content": "", "style": 7}, "L171": {"content": "", "style": 7}, "L172": {"content": "", "style": 7}, "M158": {"content": "", "style": 7}, "M159": {"content": "", "style": 7}, "M160": {"content": "", "style": 7}, "M161": {"content": "", "style": 7}, "M162": {"content": "", "style": 7}, "M163": {"content": "", "style": 7}, "M164": {"content": "", "style": 7}, "M165": {"content": "", "style": 7}, "M166": {"content": "", "style": 7}, "M167": {"content": "", "style": 7}, "M168": {"content": "", "style": 7}, "M169": {"content": "", "style": 7}, "M170": {"content": "", "style": 7}, "M171": {"content": "", "style": 7}, "M172": {"content": "", "style": 7}, "N158": {"content": "", "style": 7}, "N159": {"content": "", "style": 7}, "N160": {"content": "", "style": 7}, "N161": {"content": "", "style": 7}, "N162": {"content": "", "style": 7}, "N163": {"content": "", "style": 7}, "N164": {"content": "", "style": 7}, "N165": {"content": "", "style": 7}, "N166": {"content": "", "style": 7}, "N167": {"content": "", "style": 7}, "N168": {"content": "", "style": 7}, "N169": {"content": "", "style": 7}, "N170": {"content": "", "style": 7}, "N171": {"content": "", "style": 7}, "N172": {"content": "", "style": 7}, "O158": {"content": "", "style": 7}, "O159": {"content": "", "style": 7}, "O160": {"content": "", "style": 7}, "O161": {"content": "", "style": 7}, "O162": {"content": "", "style": 7}, "O163": {"content": "", "style": 7}, "O164": {"content": "", "style": 7}, "O165": {"content": "", "style": 7}, "O166": {"content": "", "style": 7}, "O167": {"content": "", "style": 7}, "O168": {"content": "", "style": 7}, "O169": {"content": "", "style": 7}, "O170": {"content": "", "style": 7}, "O171": {"content": "", "style": 7}, "O172": {"content": "", "style": 7}, "P158": {"content": "", "style": 7}, "P159": {"content": "", "style": 7}, "P160": {"content": "", "style": 7}, "P161": {"content": "", "style": 7}, "P162": {"content": "", "style": 7}, "P163": {"content": "", "style": 7}, "P164": {"content": "", "style": 7}, "P165": {"content": "", "style": 7}, "P166": {"content": "", "style": 7}, "P167": {"content": "", "style": 7}, "P168": {"content": "", "style": 7}, "P169": {"content": "", "style": 7}, "P170": {"content": "", "style": 7}, "P171": {"content": "", "style": 7}, "P172": {"content": "", "style": 7}, "Q158": {"content": "", "style": 7}, "Q159": {"content": "", "style": 7}, "Q160": {"content": "", "style": 7}, "Q161": {"content": "", "style": 7}, "Q162": {"content": "", "style": 7}, "Q163": {"content": "", "style": 7}, "Q164": {"content": "", "style": 7}, "Q165": {"content": "", "style": 7}, "Q166": {"content": "", "style": 7}, "Q167": {"content": "", "style": 7}, "Q168": {"content": "", "style": 7}, "Q169": {"content": "", "style": 7}, "Q170": {"content": "", "style": 7}, "Q171": {"content": "", "style": 7}, "Q172": {"content": "", "style": 7}, "R158": {"content": "", "style": 7}, "R159": {"content": "", "style": 7}, "R160": {"content": "", "style": 7}, "R161": {"content": "", "style": 7}, "R162": {"content": "", "style": 7}, "R163": {"content": "", "style": 7}, "R164": {"content": "", "style": 7}, "R165": {"content": "", "style": 7}, "R166": {"content": "", "style": 7}, "R167": {"content": "", "style": 7}, "R168": {"content": "", "style": 7}, "R169": {"content": "", "style": 7}, "R170": {"content": "", "style": 7}, "R171": {"content": "", "style": 7}, "R172": {"content": "", "style": 7}, "S158": {"content": "", "style": 7}, "S159": {"content": "", "style": 7}, "S160": {"content": "", "style": 7}, "S161": {"content": "", "style": 7}, "S162": {"content": "", "style": 7}, "S163": {"content": "", "style": 7}, "S164": {"content": "", "style": 7}, "S165": {"content": "", "style": 7}, "S166": {"content": "", "style": 7}, "S167": {"content": "", "style": 7}, "S168": {"content": "", "style": 7}, "S169": {"content": "", "style": 7}, "S170": {"content": "", "style": 7}, "S171": {"content": "", "style": 7}, "S172": {"content": "", "style": 7}, "T158": {"content": "", "style": 7}, "T159": {"content": "", "style": 7}, "T160": {"content": "", "style": 7}, "T161": {"content": "", "style": 7}, "T162": {"content": "", "style": 7}, "T163": {"content": "", "style": 7}, "T164": {"content": "", "style": 7}, "T165": {"content": "", "style": 7}, "T166": {"content": "", "style": 7}, "T167": {"content": "", "style": 7}, "T168": {"content": "", "style": 7}, "T169": {"content": "", "style": 7}, "T170": {"content": "", "style": 7}, "T171": {"content": "", "style": 7}, "T172": {"content": "", "style": 7}, "U158": {"content": "", "style": 7}, "U159": {"content": "", "style": 7}, "U160": {"content": "", "style": 7}, "U161": {"content": "", "style": 7}, "U162": {"content": "", "style": 7}, "U163": {"content": "", "style": 7}, "U164": {"content": "", "style": 7}, "U165": {"content": "", "style": 7}, "U166": {"content": "", "style": 7}, "U167": {"content": "", "style": 7}, "U168": {"content": "", "style": 7}, "U169": {"content": "", "style": 7}, "U170": {"content": "", "style": 7}, "U171": {"content": "", "style": 7}, "U172": {"content": "", "style": 7}, "V158": {"content": "", "style": 7}, "V159": {"content": "", "style": 7}, "V160": {"content": "", "style": 7}, "V161": {"content": "", "style": 7}, "V162": {"content": "", "style": 7}, "V163": {"content": "", "style": 7}, "V164": {"content": "", "style": 7}, "V165": {"content": "", "style": 7}, "V166": {"content": "", "style": 7}, "V167": {"content": "", "style": 7}, "V168": {"content": "", "style": 7}, "V169": {"content": "", "style": 7}, "V170": {"content": "", "style": 7}, "V171": {"content": "", "style": 7}, "V172": {"content": "", "style": 7}, "A137": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A138": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A139": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A140": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A141": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A142": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A143": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A144": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A145": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A146": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A147": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A148": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A149": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A150": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A151": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B137": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B138": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B139": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B140": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B141": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B142": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B143": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B144": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B145": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B146": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B147": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B148": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B149": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B150": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B151": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C137": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C138": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C139": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C140": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C141": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C142": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C143": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C144": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C145": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C146": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C147": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C148": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C149": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C150": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C151": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D137": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D138": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D139": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D140": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D141": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D142": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D143": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D144": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D145": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D146": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D147": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D148": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D149": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D150": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D151": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E137": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E138": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E139": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E140": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E141": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E142": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E143": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E144": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E145": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E146": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E147": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E148": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E149": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E150": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E151": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F137": {"content": "=iferror(B137/D137,0)", "style": 20, "format": "0.00%"}, "F138": {"content": "=iferror(B138/D138,0)", "style": 20, "format": "0.00%"}, "F139": {"content": "=iferror(B139/D139,0)", "style": 20, "format": "0.00%"}, "F140": {"content": "=iferror(B140/D140,0)", "style": 20, "format": "0.00%"}, "F141": {"content": "=iferror(B141/D141,0)", "style": 20, "format": "0.00%"}, "F142": {"content": "=iferror(B142/D142,0)", "style": 20, "format": "0.00%"}, "F143": {"content": "=iferror(B143/D143,0)", "style": 20, "format": "0.00%"}, "F144": {"content": "=iferror(B144/D144,0)", "style": 20, "format": "0.00%"}, "F145": {"content": "=iferror(B145/D145,0)", "style": 20, "format": "0.00%"}, "F146": {"content": "=iferror(B146/D146,0)", "style": 20, "format": "0.00%"}, "F147": {"content": "=iferror(B147/D147,0)", "style": 20, "format": "0.00%"}, "F148": {"content": "=iferror(B148/D148,0)", "style": 20, "format": "0.00%"}, "F149": {"content": "=iferror(B149/D149,0)", "style": 20, "format": "0.00%"}, "F150": {"content": "=iferror(B150/D150,0)", "style": 20, "format": "0.00%"}, "F151": {"content": "=iferror(B151/D151,0)", "style": 20, "format": "0.00%"}, "G137": {"content": "=iferror(C137/E137,0)", "style": 20, "format": "0.00%"}, "G138": {"content": "=iferror(C138/E138,0)", "style": 20, "format": "0.00%"}, "G139": {"content": "=iferror(C139/E139,0)", "style": 20, "format": "0.00%"}, "G140": {"content": "=iferror(C140/E140,0)", "style": 20, "format": "0.00%"}, "G141": {"content": "=iferror(C141/E141,0)", "style": 20, "format": "0.00%"}, "G142": {"content": "=iferror(C142/E142,0)", "style": 20, "format": "0.00%"}, "G143": {"content": "=iferror(C143/E143,0)", "style": 20, "format": "0.00%"}, "G144": {"content": "=iferror(C144/E144,0)", "style": 20, "format": "0.00%"}, "G145": {"content": "=iferror(C145/E145,0)", "style": 20, "format": "0.00%"}, "G146": {"content": "=iferror(C146/E146,0)", "style": 20, "format": "0.00%"}, "G147": {"content": "=iferror(C147/E147,0)", "style": 20, "format": "0.00%"}, "G148": {"content": "=iferror(C148/E148,0)", "style": 20, "format": "0.00%"}, "G149": {"content": "=iferror(C149/E149,0)", "style": 20, "format": "0.00%"}, "G150": {"content": "=iferror(C150/E150,0)", "style": 20, "format": "0.00%"}, "G151": {"content": "=iferror(C151/E151,0)", "style": 20, "format": "0.00%"}, "H137": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H138": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H139": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H140": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H141": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H142": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H143": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H144": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H145": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H146": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H147": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H148": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H149": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H150": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H151": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I137": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I138": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I139": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I140": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I141": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I142": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I143": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I144": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I145": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I146": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I147": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I148": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I149": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I150": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I151": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J137": {"content": "", "style": 7}, "J138": {"content": "", "style": 7}, "J139": {"content": "", "style": 7}, "J140": {"content": "", "style": 7}, "J141": {"content": "", "style": 7}, "J142": {"content": "", "style": 7}, "J143": {"content": "", "style": 7}, "J144": {"content": "", "style": 7}, "J145": {"content": "", "style": 7}, "J146": {"content": "", "style": 7}, "J147": {"content": "", "style": 7}, "J148": {"content": "", "style": 7}, "J149": {"content": "", "style": 7}, "J150": {"content": "", "style": 7}, "J151": {"content": "", "style": 7}, "K137": {"content": "", "style": 7}, "K138": {"content": "", "style": 7}, "K139": {"content": "", "style": 7}, "K140": {"content": "", "style": 7}, "K141": {"content": "", "style": 7}, "K142": {"content": "", "style": 7}, "K143": {"content": "", "style": 7}, "K144": {"content": "", "style": 7}, "K145": {"content": "", "style": 7}, "K146": {"content": "", "style": 7}, "K147": {"content": "", "style": 7}, "K148": {"content": "", "style": 7}, "K149": {"content": "", "style": 7}, "K150": {"content": "", "style": 7}, "K151": {"content": "", "style": 7}, "L137": {"content": "", "style": 7}, "L138": {"content": "", "style": 7}, "L139": {"content": "", "style": 7}, "L140": {"content": "", "style": 7}, "L141": {"content": "", "style": 7}, "L142": {"content": "", "style": 7}, "L143": {"content": "", "style": 7}, "L144": {"content": "", "style": 7}, "L145": {"content": "", "style": 7}, "L146": {"content": "", "style": 7}, "L147": {"content": "", "style": 7}, "L148": {"content": "", "style": 7}, "L149": {"content": "", "style": 7}, "L150": {"content": "", "style": 7}, "L151": {"content": "", "style": 7}, "M137": {"content": "", "style": 7}, "M138": {"content": "", "style": 7}, "M139": {"content": "", "style": 7}, "M140": {"content": "", "style": 7}, "M141": {"content": "", "style": 7}, "M142": {"content": "", "style": 7}, "M143": {"content": "", "style": 7}, "M144": {"content": "", "style": 7}, "M145": {"content": "", "style": 7}, "M146": {"content": "", "style": 7}, "M147": {"content": "", "style": 7}, "M148": {"content": "", "style": 7}, "M149": {"content": "", "style": 7}, "M150": {"content": "", "style": 7}, "M151": {"content": "", "style": 7}, "N137": {"content": "", "style": 7}, "N138": {"content": "", "style": 7}, "N139": {"content": "", "style": 7}, "N140": {"content": "", "style": 7}, "N141": {"content": "", "style": 7}, "N142": {"content": "", "style": 7}, "N143": {"content": "", "style": 7}, "N144": {"content": "", "style": 7}, "N145": {"content": "", "style": 7}, "N146": {"content": "", "style": 7}, "N147": {"content": "", "style": 7}, "N148": {"content": "", "style": 7}, "N149": {"content": "", "style": 7}, "N150": {"content": "", "style": 7}, "N151": {"content": "", "style": 7}, "O137": {"content": "", "style": 7}, "O138": {"content": "", "style": 7}, "O139": {"content": "", "style": 7}, "O140": {"content": "", "style": 7}, "O141": {"content": "", "style": 7}, "O142": {"content": "", "style": 7}, "O143": {"content": "", "style": 7}, "O144": {"content": "", "style": 7}, "O145": {"content": "", "style": 7}, "O146": {"content": "", "style": 7}, "O147": {"content": "", "style": 7}, "O148": {"content": "", "style": 7}, "O149": {"content": "", "style": 7}, "O150": {"content": "", "style": 7}, "O151": {"content": "", "style": 7}, "P137": {"content": "", "style": 7}, "P138": {"content": "", "style": 7}, "P139": {"content": "", "style": 7}, "P140": {"content": "", "style": 7}, "P141": {"content": "", "style": 7}, "P142": {"content": "", "style": 7}, "P143": {"content": "", "style": 7}, "P144": {"content": "", "style": 7}, "P145": {"content": "", "style": 7}, "P146": {"content": "", "style": 7}, "P147": {"content": "", "style": 7}, "P148": {"content": "", "style": 7}, "P149": {"content": "", "style": 7}, "P150": {"content": "", "style": 7}, "P151": {"content": "", "style": 7}, "Q137": {"content": "", "style": 7}, "Q138": {"content": "", "style": 7}, "Q139": {"content": "", "style": 7}, "Q140": {"content": "", "style": 7}, "Q141": {"content": "", "style": 7}, "Q142": {"content": "", "style": 7}, "Q143": {"content": "", "style": 7}, "Q144": {"content": "", "style": 7}, "Q145": {"content": "", "style": 7}, "Q146": {"content": "", "style": 7}, "Q147": {"content": "", "style": 7}, "Q148": {"content": "", "style": 7}, "Q149": {"content": "", "style": 7}, "Q150": {"content": "", "style": 7}, "Q151": {"content": "", "style": 7}, "R137": {"content": "", "style": 7}, "R138": {"content": "", "style": 7}, "R139": {"content": "", "style": 7}, "R140": {"content": "", "style": 7}, "R141": {"content": "", "style": 7}, "R142": {"content": "", "style": 7}, "R143": {"content": "", "style": 7}, "R144": {"content": "", "style": 7}, "R145": {"content": "", "style": 7}, "R146": {"content": "", "style": 7}, "R147": {"content": "", "style": 7}, "R148": {"content": "", "style": 7}, "R149": {"content": "", "style": 7}, "R150": {"content": "", "style": 7}, "R151": {"content": "", "style": 7}, "S137": {"content": "", "style": 7}, "S138": {"content": "", "style": 7}, "S139": {"content": "", "style": 7}, "S140": {"content": "", "style": 7}, "S141": {"content": "", "style": 7}, "S142": {"content": "", "style": 7}, "S143": {"content": "", "style": 7}, "S144": {"content": "", "style": 7}, "S145": {"content": "", "style": 7}, "S146": {"content": "", "style": 7}, "S147": {"content": "", "style": 7}, "S148": {"content": "", "style": 7}, "S149": {"content": "", "style": 7}, "S150": {"content": "", "style": 7}, "S151": {"content": "", "style": 7}, "T137": {"content": "", "style": 7}, "T138": {"content": "", "style": 7}, "T139": {"content": "", "style": 7}, "T140": {"content": "", "style": 7}, "T141": {"content": "", "style": 7}, "T142": {"content": "", "style": 7}, "T143": {"content": "", "style": 7}, "T144": {"content": "", "style": 7}, "T145": {"content": "", "style": 7}, "T146": {"content": "", "style": 7}, "T147": {"content": "", "style": 7}, "T148": {"content": "", "style": 7}, "T149": {"content": "", "style": 7}, "T150": {"content": "", "style": 7}, "T151": {"content": "", "style": 7}, "U137": {"content": "", "style": 7}, "U138": {"content": "", "style": 7}, "U139": {"content": "", "style": 7}, "U140": {"content": "", "style": 7}, "U141": {"content": "", "style": 7}, "U142": {"content": "", "style": 7}, "U143": {"content": "", "style": 7}, "U144": {"content": "", "style": 7}, "U145": {"content": "", "style": 7}, "U146": {"content": "", "style": 7}, "U147": {"content": "", "style": 7}, "U148": {"content": "", "style": 7}, "U149": {"content": "", "style": 7}, "U150": {"content": "", "style": 7}, "U151": {"content": "", "style": 7}, "V137": {"content": "", "style": 7}, "V138": {"content": "", "style": 7}, "V139": {"content": "", "style": 7}, "V140": {"content": "", "style": 7}, "V141": {"content": "", "style": 7}, "V142": {"content": "", "style": 7}, "V143": {"content": "", "style": 7}, "V144": {"content": "", "style": 7}, "V145": {"content": "", "style": 7}, "V146": {"content": "", "style": 7}, "V147": {"content": "", "style": 7}, "V148": {"content": "", "style": 7}, "V149": {"content": "", "style": 7}, "V150": {"content": "", "style": 7}, "V151": {"content": "", "style": 7}, "A116": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A117": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A118": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A119": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A120": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A121": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A122": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A123": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A124": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A125": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A126": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A127": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A128": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A129": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A130": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B116": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B117": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B118": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B119": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B120": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B121": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B122": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B123": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B124": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B125": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B126": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B127": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B128": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B129": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B130": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C116": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C117": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C118": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C119": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C120": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C121": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C122": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C123": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C124": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C125": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C126": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C127": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C128": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C129": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C130": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D116": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D117": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D118": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D119": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D120": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D121": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D122": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D123": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D124": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D125": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D126": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D127": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D128": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D129": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D130": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E116": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E117": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E118": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E119": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E120": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E121": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E122": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E123": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E124": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E125": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E126": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E127": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E128": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E129": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E130": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F116": {"content": "=iferror(B116/D116,0)", "style": 20, "format": "0.00%"}, "F117": {"content": "=iferror(B117/D117,0)", "style": 20, "format": "0.00%"}, "F118": {"content": "=iferror(B118/D118,0)", "style": 20, "format": "0.00%"}, "F119": {"content": "=iferror(B119/D119,0)", "style": 20, "format": "0.00%"}, "F120": {"content": "=iferror(B120/D120,0)", "style": 20, "format": "0.00%"}, "F121": {"content": "=iferror(B121/D121,0)", "style": 20, "format": "0.00%"}, "F122": {"content": "=iferror(B122/D122,0)", "style": 20, "format": "0.00%"}, "F123": {"content": "=iferror(B123/D123,0)", "style": 20, "format": "0.00%"}, "F124": {"content": "=iferror(B124/D124,0)", "style": 20, "format": "0.00%"}, "F125": {"content": "=iferror(B125/D125,0)", "style": 20, "format": "0.00%"}, "F126": {"content": "=iferror(B126/D126,0)", "style": 20, "format": "0.00%"}, "F127": {"content": "=iferror(B127/D127,0)", "style": 20, "format": "0.00%"}, "F128": {"content": "=iferror(B128/D128,0)", "style": 20, "format": "0.00%"}, "F129": {"content": "=iferror(B129/D129,0)", "style": 20, "format": "0.00%"}, "F130": {"content": "=iferror(B130/D130,0)", "style": 20, "format": "0.00%"}, "G116": {"content": "=iferror(C116/E116,0)", "style": 20, "format": "0.00%"}, "G117": {"content": "=iferror(C117/E117,0)", "style": 20, "format": "0.00%"}, "G118": {"content": "=iferror(C118/E118,0)", "style": 20, "format": "0.00%"}, "G119": {"content": "=iferror(C119/E119,0)", "style": 20, "format": "0.00%"}, "G120": {"content": "=iferror(C120/E120,0)", "style": 20, "format": "0.00%"}, "G121": {"content": "=iferror(C121/E121,0)", "style": 20, "format": "0.00%"}, "G122": {"content": "=iferror(C122/E122,0)", "style": 20, "format": "0.00%"}, "G123": {"content": "=iferror(C123/E123,0)", "style": 20, "format": "0.00%"}, "G124": {"content": "=iferror(C124/E124,0)", "style": 20, "format": "0.00%"}, "G125": {"content": "=iferror(C125/E125,0)", "style": 20, "format": "0.00%"}, "G126": {"content": "=iferror(C126/E126,0)", "style": 20, "format": "0.00%"}, "G127": {"content": "=iferror(C127/E127,0)", "style": 20, "format": "0.00%"}, "G128": {"content": "=iferror(C128/E128,0)", "style": 20, "format": "0.00%"}, "G129": {"content": "=iferror(C129/E129,0)", "style": 20, "format": "0.00%"}, "G130": {"content": "=iferror(C130/E130,0)", "style": 20, "format": "0.00%"}, "H116": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H117": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H118": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H119": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H120": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H121": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H122": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H123": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H124": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H125": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H126": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H127": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H128": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H129": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H130": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I116": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I117": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I118": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I119": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I120": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I121": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I122": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I123": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I124": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I125": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I126": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I127": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I128": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I129": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I130": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J116": {"content": "", "style": 7}, "J117": {"content": "", "style": 7}, "J118": {"content": "", "style": 7}, "J119": {"content": "", "style": 7}, "J120": {"content": "", "style": 7}, "J121": {"content": "", "style": 7}, "J122": {"content": "", "style": 7}, "J123": {"content": "", "style": 7}, "J124": {"content": "", "style": 7}, "J125": {"content": "", "style": 7}, "J126": {"content": "", "style": 7}, "J127": {"content": "", "style": 7}, "J128": {"content": "", "style": 7}, "J129": {"content": "", "style": 7}, "J130": {"content": "", "style": 7}, "K116": {"content": "", "style": 7}, "K117": {"content": "", "style": 7}, "K118": {"content": "", "style": 7}, "K119": {"content": "", "style": 7}, "K120": {"content": "", "style": 7}, "K121": {"content": "", "style": 7}, "K122": {"content": "", "style": 7}, "K123": {"content": "", "style": 7}, "K124": {"content": "", "style": 7}, "K125": {"content": "", "style": 7}, "K126": {"content": "", "style": 7}, "K127": {"content": "", "style": 7}, "K128": {"content": "", "style": 7}, "K129": {"content": "", "style": 7}, "K130": {"content": "", "style": 7}, "L116": {"content": "", "style": 7}, "L117": {"content": "", "style": 7}, "L118": {"content": "", "style": 7}, "L119": {"content": "", "style": 7}, "L120": {"content": "", "style": 7}, "L121": {"content": "", "style": 7}, "L122": {"content": "", "style": 7}, "L123": {"content": "", "style": 7}, "L124": {"content": "", "style": 7}, "L125": {"content": "", "style": 7}, "L126": {"content": "", "style": 7}, "L127": {"content": "", "style": 7}, "L128": {"content": "", "style": 7}, "L129": {"content": "", "style": 7}, "L130": {"content": "", "style": 7}, "M116": {"content": "", "style": 7}, "M117": {"content": "", "style": 7}, "M118": {"content": "", "style": 7}, "M119": {"content": "", "style": 7}, "M120": {"content": "", "style": 7}, "M121": {"content": "", "style": 7}, "M122": {"content": "", "style": 7}, "M123": {"content": "", "style": 7}, "M124": {"content": "", "style": 7}, "M125": {"content": "", "style": 7}, "M126": {"content": "", "style": 7}, "M127": {"content": "", "style": 7}, "M128": {"content": "", "style": 7}, "M129": {"content": "", "style": 7}, "M130": {"content": "", "style": 7}, "N116": {"content": "", "style": 7}, "N117": {"content": "", "style": 7}, "N118": {"content": "", "style": 7}, "N119": {"content": "", "style": 7}, "N120": {"content": "", "style": 7}, "N121": {"content": "", "style": 7}, "N122": {"content": "", "style": 7}, "N123": {"content": "", "style": 7}, "N124": {"content": "", "style": 7}, "N125": {"content": "", "style": 7}, "N126": {"content": "", "style": 7}, "N127": {"content": "", "style": 7}, "N128": {"content": "", "style": 7}, "N129": {"content": "", "style": 7}, "N130": {"content": "", "style": 7}, "O116": {"content": "", "style": 7}, "O117": {"content": "", "style": 7}, "O118": {"content": "", "style": 7}, "O119": {"content": "", "style": 7}, "O120": {"content": "", "style": 7}, "O121": {"content": "", "style": 7}, "O122": {"content": "", "style": 7}, "O123": {"content": "", "style": 7}, "O124": {"content": "", "style": 7}, "O125": {"content": "", "style": 7}, "O126": {"content": "", "style": 7}, "O127": {"content": "", "style": 7}, "O128": {"content": "", "style": 7}, "O129": {"content": "", "style": 7}, "O130": {"content": "", "style": 7}, "P116": {"content": "", "style": 7}, "P117": {"content": "", "style": 7}, "P118": {"content": "", "style": 7}, "P119": {"content": "", "style": 7}, "P120": {"content": "", "style": 7}, "P121": {"content": "", "style": 7}, "P122": {"content": "", "style": 7}, "P123": {"content": "", "style": 7}, "P124": {"content": "", "style": 7}, "P125": {"content": "", "style": 7}, "P126": {"content": "", "style": 7}, "P127": {"content": "", "style": 7}, "P128": {"content": "", "style": 7}, "P129": {"content": "", "style": 7}, "P130": {"content": "", "style": 7}, "Q116": {"content": "", "style": 7}, "Q117": {"content": "", "style": 7}, "Q118": {"content": "", "style": 7}, "Q119": {"content": "", "style": 7}, "Q120": {"content": "", "style": 7}, "Q121": {"content": "", "style": 7}, "Q122": {"content": "", "style": 7}, "Q123": {"content": "", "style": 7}, "Q124": {"content": "", "style": 7}, "Q125": {"content": "", "style": 7}, "Q126": {"content": "", "style": 7}, "Q127": {"content": "", "style": 7}, "Q128": {"content": "", "style": 7}, "Q129": {"content": "", "style": 7}, "Q130": {"content": "", "style": 7}, "R116": {"content": "", "style": 7}, "R117": {"content": "", "style": 7}, "R118": {"content": "", "style": 7}, "R119": {"content": "", "style": 7}, "R120": {"content": "", "style": 7}, "R121": {"content": "", "style": 7}, "R122": {"content": "", "style": 7}, "R123": {"content": "", "style": 7}, "R124": {"content": "", "style": 7}, "R125": {"content": "", "style": 7}, "R126": {"content": "", "style": 7}, "R127": {"content": "", "style": 7}, "R128": {"content": "", "style": 7}, "R129": {"content": "", "style": 7}, "R130": {"content": "", "style": 7}, "S116": {"content": "", "style": 7}, "S117": {"content": "", "style": 7}, "S118": {"content": "", "style": 7}, "S119": {"content": "", "style": 7}, "S120": {"content": "", "style": 7}, "S121": {"content": "", "style": 7}, "S122": {"content": "", "style": 7}, "S123": {"content": "", "style": 7}, "S124": {"content": "", "style": 7}, "S125": {"content": "", "style": 7}, "S126": {"content": "", "style": 7}, "S127": {"content": "", "style": 7}, "S128": {"content": "", "style": 7}, "S129": {"content": "", "style": 7}, "S130": {"content": "", "style": 7}, "T116": {"content": "", "style": 7}, "T117": {"content": "", "style": 7}, "T118": {"content": "", "style": 7}, "T119": {"content": "", "style": 7}, "T120": {"content": "", "style": 7}, "T121": {"content": "", "style": 7}, "T122": {"content": "", "style": 7}, "T123": {"content": "", "style": 7}, "T124": {"content": "", "style": 7}, "T125": {"content": "", "style": 7}, "T126": {"content": "", "style": 7}, "T127": {"content": "", "style": 7}, "T128": {"content": "", "style": 7}, "T129": {"content": "", "style": 7}, "T130": {"content": "", "style": 7}, "U116": {"content": "", "style": 7}, "U117": {"content": "", "style": 7}, "U118": {"content": "", "style": 7}, "U119": {"content": "", "style": 7}, "U120": {"content": "", "style": 7}, "U121": {"content": "", "style": 7}, "U122": {"content": "", "style": 7}, "U123": {"content": "", "style": 7}, "U124": {"content": "", "style": 7}, "U125": {"content": "", "style": 7}, "U126": {"content": "", "style": 7}, "U127": {"content": "", "style": 7}, "U128": {"content": "", "style": 7}, "U129": {"content": "", "style": 7}, "U130": {"content": "", "style": 7}, "V116": {"content": "", "style": 7}, "V117": {"content": "", "style": 7}, "V118": {"content": "", "style": 7}, "V119": {"content": "", "style": 7}, "V120": {"content": "", "style": 7}, "V121": {"content": "", "style": 7}, "V122": {"content": "", "style": 7}, "V123": {"content": "", "style": 7}, "V124": {"content": "", "style": 7}, "V125": {"content": "", "style": 7}, "V126": {"content": "", "style": 7}, "V127": {"content": "", "style": 7}, "V128": {"content": "", "style": 7}, "V129": {"content": "", "style": 7}, "V130": {"content": "", "style": 7}, "A95": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A96": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A97": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A98": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A99": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A100": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A101": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A102": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A103": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A104": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A105": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A106": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A107": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A108": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A109": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B95": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B96": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B97": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B98": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B99": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B100": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B101": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B102": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B103": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B104": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B105": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B106": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B107": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B108": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B109": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C95": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C96": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C97": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C98": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C99": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C100": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C101": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C102": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C103": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C104": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C105": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C106": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C107": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C108": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C109": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D95": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D96": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D97": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D98": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D99": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D100": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D101": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D102": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D103": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D104": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D105": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D106": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D107": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D108": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D109": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E95": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E96": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E97": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E98": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E99": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E100": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E101": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E102": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E103": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E104": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E105": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E106": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E107": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E108": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E109": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F95": {"content": "=iferror(B95/D95,0)", "style": 20, "format": "0.00%"}, "F96": {"content": "=iferror(B96/D96,0)", "style": 20, "format": "0.00%"}, "F97": {"content": "=iferror(B97/D97,0)", "style": 20, "format": "0.00%"}, "F98": {"content": "=iferror(B98/D98,0)", "style": 20, "format": "0.00%"}, "F99": {"content": "=iferror(B99/D99,0)", "style": 20, "format": "0.00%"}, "F100": {"content": "=iferror(B100/D100,0)", "style": 20, "format": "0.00%"}, "F101": {"content": "=iferror(B101/D101,0)", "style": 20, "format": "0.00%"}, "F102": {"content": "=iferror(B102/D102,0)", "style": 20, "format": "0.00%"}, "F103": {"content": "=iferror(B103/D103,0)", "style": 20, "format": "0.00%"}, "F104": {"content": "=iferror(B104/D104,0)", "style": 20, "format": "0.00%"}, "F105": {"content": "=iferror(B105/D105,0)", "style": 20, "format": "0.00%"}, "F106": {"content": "=iferror(B106/D106,0)", "style": 20, "format": "0.00%"}, "F107": {"content": "=iferror(B107/D107,0)", "style": 20, "format": "0.00%"}, "F108": {"content": "=iferror(B108/D108,0)", "style": 20, "format": "0.00%"}, "F109": {"content": "=iferror(B109/D109,0)", "style": 20, "format": "0.00%"}, "G95": {"content": "=iferror(C95/E95,0)", "style": 20, "format": "0.00%"}, "G96": {"content": "=iferror(C96/E96,0)", "style": 20, "format": "0.00%"}, "G97": {"content": "=iferror(C97/E97,0)", "style": 20, "format": "0.00%"}, "G98": {"content": "=iferror(C98/E98,0)", "style": 20, "format": "0.00%"}, "G99": {"content": "=iferror(C99/E99,0)", "style": 20, "format": "0.00%"}, "G100": {"content": "=iferror(C100/E100,0)", "style": 20, "format": "0.00%"}, "G101": {"content": "=iferror(C101/E101,0)", "style": 20, "format": "0.00%"}, "G102": {"content": "=iferror(C102/E102,0)", "style": 20, "format": "0.00%"}, "G103": {"content": "=iferror(C103/E103,0)", "style": 20, "format": "0.00%"}, "G104": {"content": "=iferror(C104/E104,0)", "style": 20, "format": "0.00%"}, "G105": {"content": "=iferror(C105/E105,0)", "style": 20, "format": "0.00%"}, "G106": {"content": "=iferror(C106/E106,0)", "style": 20, "format": "0.00%"}, "G107": {"content": "=iferror(C107/E107,0)", "style": 20, "format": "0.00%"}, "G108": {"content": "=iferror(C108/E108,0)", "style": 20, "format": "0.00%"}, "G109": {"content": "=iferror(C109/E109,0)", "style": 20, "format": "0.00%"}, "H95": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H96": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H97": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H98": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H99": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H100": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H101": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H102": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H103": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H104": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H105": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H106": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H107": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H108": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H109": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I95": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I96": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I97": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I98": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I99": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I100": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I101": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I102": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I103": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I104": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I105": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I106": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I107": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I108": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I109": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J95": {"content": "", "style": 7}, "J96": {"content": "", "style": 7}, "J97": {"content": "", "style": 7}, "J98": {"content": "", "style": 7}, "J99": {"content": "", "style": 7}, "J100": {"content": "", "style": 7}, "J101": {"content": "", "style": 7}, "J102": {"content": "", "style": 7}, "J103": {"content": "", "style": 7}, "J104": {"content": "", "style": 7}, "J105": {"content": "", "style": 7}, "J106": {"content": "", "style": 7}, "J107": {"content": "", "style": 7}, "J108": {"content": "", "style": 7}, "J109": {"content": "", "style": 7}, "K95": {"content": "", "style": 7}, "K96": {"content": "", "style": 7}, "K97": {"content": "", "style": 7}, "K98": {"content": "", "style": 7}, "K99": {"content": "", "style": 7}, "K100": {"content": "", "style": 7}, "K101": {"content": "", "style": 7}, "K102": {"content": "", "style": 7}, "K103": {"content": "", "style": 7}, "K104": {"content": "", "style": 7}, "K105": {"content": "", "style": 7}, "K106": {"content": "", "style": 7}, "K107": {"content": "", "style": 7}, "K108": {"content": "", "style": 7}, "K109": {"content": "", "style": 7}, "L95": {"content": "", "style": 7}, "L96": {"content": "", "style": 7}, "L97": {"content": "", "style": 7}, "L98": {"content": "", "style": 7}, "L99": {"content": "", "style": 7}, "L100": {"content": "", "style": 7}, "L101": {"content": "", "style": 7}, "L102": {"content": "", "style": 7}, "L103": {"content": "", "style": 7}, "L104": {"content": "", "style": 7}, "L105": {"content": "", "style": 7}, "L106": {"content": "", "style": 7}, "L107": {"content": "", "style": 7}, "L108": {"content": "", "style": 7}, "L109": {"content": "", "style": 7}, "M95": {"content": "", "style": 7}, "M96": {"content": "", "style": 7}, "M97": {"content": "", "style": 7}, "M98": {"content": "", "style": 7}, "M99": {"content": "", "style": 7}, "M100": {"content": "", "style": 7}, "M101": {"content": "", "style": 7}, "M102": {"content": "", "style": 7}, "M103": {"content": "", "style": 7}, "M104": {"content": "", "style": 7}, "M105": {"content": "", "style": 7}, "M106": {"content": "", "style": 7}, "M107": {"content": "", "style": 7}, "M108": {"content": "", "style": 7}, "M109": {"content": "", "style": 7}, "N95": {"content": "", "style": 7}, "N96": {"content": "", "style": 7}, "N97": {"content": "", "style": 7}, "N98": {"content": "", "style": 7}, "N99": {"content": "", "style": 7}, "N100": {"content": "", "style": 7}, "N101": {"content": "", "style": 7}, "N102": {"content": "", "style": 7}, "N103": {"content": "", "style": 7}, "N104": {"content": "", "style": 7}, "N105": {"content": "", "style": 7}, "N106": {"content": "", "style": 7}, "N107": {"content": "", "style": 7}, "N108": {"content": "", "style": 7}, "N109": {"content": "", "style": 7}, "O95": {"content": "", "style": 7}, "O96": {"content": "", "style": 7}, "O97": {"content": "", "style": 7}, "O98": {"content": "", "style": 7}, "O99": {"content": "", "style": 7}, "O100": {"content": "", "style": 7}, "O101": {"content": "", "style": 7}, "O102": {"content": "", "style": 7}, "O103": {"content": "", "style": 7}, "O104": {"content": "", "style": 7}, "O105": {"content": "", "style": 7}, "O106": {"content": "", "style": 7}, "O107": {"content": "", "style": 7}, "O108": {"content": "", "style": 7}, "O109": {"content": "", "style": 7}, "P95": {"content": "", "style": 7}, "P96": {"content": "", "style": 7}, "P97": {"content": "", "style": 7}, "P98": {"content": "", "style": 7}, "P99": {"content": "", "style": 7}, "P100": {"content": "", "style": 7}, "P101": {"content": "", "style": 7}, "P102": {"content": "", "style": 7}, "P103": {"content": "", "style": 7}, "P104": {"content": "", "style": 7}, "P105": {"content": "", "style": 7}, "P106": {"content": "", "style": 7}, "P107": {"content": "", "style": 7}, "P108": {"content": "", "style": 7}, "P109": {"content": "", "style": 7}, "Q95": {"content": "", "style": 7}, "Q96": {"content": "", "style": 7}, "Q97": {"content": "", "style": 7}, "Q98": {"content": "", "style": 7}, "Q99": {"content": "", "style": 7}, "Q100": {"content": "", "style": 7}, "Q101": {"content": "", "style": 7}, "Q102": {"content": "", "style": 7}, "Q103": {"content": "", "style": 7}, "Q104": {"content": "", "style": 7}, "Q105": {"content": "", "style": 7}, "Q106": {"content": "", "style": 7}, "Q107": {"content": "", "style": 7}, "Q108": {"content": "", "style": 7}, "Q109": {"content": "", "style": 7}, "R95": {"content": "", "style": 7}, "R96": {"content": "", "style": 7}, "R97": {"content": "", "style": 7}, "R98": {"content": "", "style": 7}, "R99": {"content": "", "style": 7}, "R100": {"content": "", "style": 7}, "R101": {"content": "", "style": 7}, "R102": {"content": "", "style": 7}, "R103": {"content": "", "style": 7}, "R104": {"content": "", "style": 7}, "R105": {"content": "", "style": 7}, "R106": {"content": "", "style": 7}, "R107": {"content": "", "style": 7}, "R108": {"content": "", "style": 7}, "R109": {"content": "", "style": 7}, "S95": {"content": "", "style": 7}, "S96": {"content": "", "style": 7}, "S97": {"content": "", "style": 7}, "S98": {"content": "", "style": 7}, "S99": {"content": "", "style": 7}, "S100": {"content": "", "style": 7}, "S101": {"content": "", "style": 7}, "S102": {"content": "", "style": 7}, "S103": {"content": "", "style": 7}, "S104": {"content": "", "style": 7}, "S105": {"content": "", "style": 7}, "S106": {"content": "", "style": 7}, "S107": {"content": "", "style": 7}, "S108": {"content": "", "style": 7}, "S109": {"content": "", "style": 7}, "T95": {"content": "", "style": 7}, "T96": {"content": "", "style": 7}, "T97": {"content": "", "style": 7}, "T98": {"content": "", "style": 7}, "T99": {"content": "", "style": 7}, "T100": {"content": "", "style": 7}, "T101": {"content": "", "style": 7}, "T102": {"content": "", "style": 7}, "T103": {"content": "", "style": 7}, "T104": {"content": "", "style": 7}, "T105": {"content": "", "style": 7}, "T106": {"content": "", "style": 7}, "T107": {"content": "", "style": 7}, "T108": {"content": "", "style": 7}, "T109": {"content": "", "style": 7}, "U95": {"content": "", "style": 7}, "U96": {"content": "", "style": 7}, "U97": {"content": "", "style": 7}, "U98": {"content": "", "style": 7}, "U99": {"content": "", "style": 7}, "U100": {"content": "", "style": 7}, "U101": {"content": "", "style": 7}, "U102": {"content": "", "style": 7}, "U103": {"content": "", "style": 7}, "U104": {"content": "", "style": 7}, "U105": {"content": "", "style": 7}, "U106": {"content": "", "style": 7}, "U107": {"content": "", "style": 7}, "U108": {"content": "", "style": 7}, "U109": {"content": "", "style": 7}, "V95": {"content": "", "style": 7}, "V96": {"content": "", "style": 7}, "V97": {"content": "", "style": 7}, "V98": {"content": "", "style": 7}, "V99": {"content": "", "style": 7}, "V100": {"content": "", "style": 7}, "V101": {"content": "", "style": 7}, "V102": {"content": "", "style": 7}, "V103": {"content": "", "style": 7}, "V104": {"content": "", "style": 7}, "V105": {"content": "", "style": 7}, "V106": {"content": "", "style": 7}, "V107": {"content": "", "style": 7}, "V108": {"content": "", "style": 7}, "V109": {"content": "", "style": 7}, "A74": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A75": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A76": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A77": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A78": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A79": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A80": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A81": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A82": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A83": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A84": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A85": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A86": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A87": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A88": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B74": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B75": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B76": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B77": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B78": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B79": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B80": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B81": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B82": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B83": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B84": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B85": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B86": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B87": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B88": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C74": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C75": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C76": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C77": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C78": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C79": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C80": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C81": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C82": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C83": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C84": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C85": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C86": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C87": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C88": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D74": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D75": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D76": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D77": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D78": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D79": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D80": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D81": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D82": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D83": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D84": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D85": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D86": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D87": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D88": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E74": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E75": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E76": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E77": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E78": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E79": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E80": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E81": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E82": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E83": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E84": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E85": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E86": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E87": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E88": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F74": {"content": "=iferror(B74/D74,0)", "style": 20, "format": "0.00%"}, "F75": {"content": "=iferror(B75/D75,0)", "style": 20, "format": "0.00%"}, "F76": {"content": "=iferror(B76/D76,0)", "style": 20, "format": "0.00%"}, "F77": {"content": "=iferror(B77/D77,0)", "style": 20, "format": "0.00%"}, "F78": {"content": "=iferror(B78/D78,0)", "style": 20, "format": "0.00%"}, "F79": {"content": "=iferror(B79/D79,0)", "style": 20, "format": "0.00%"}, "F80": {"content": "=iferror(B80/D80,0)", "style": 20, "format": "0.00%"}, "F81": {"content": "=iferror(B81/D81,0)", "style": 20, "format": "0.00%"}, "F82": {"content": "=iferror(B82/D82,0)", "style": 20, "format": "0.00%"}, "F83": {"content": "=iferror(B83/D83,0)", "style": 20, "format": "0.00%"}, "F84": {"content": "=iferror(B84/D84,0)", "style": 20, "format": "0.00%"}, "F85": {"content": "=iferror(B85/D85,0)", "style": 20, "format": "0.00%"}, "F86": {"content": "=iferror(B86/D86,0)", "style": 20, "format": "0.00%"}, "F87": {"content": "=iferror(B87/D87,0)", "style": 20, "format": "0.00%"}, "F88": {"content": "=iferror(B88/D88,0)", "style": 20, "format": "0.00%"}, "G74": {"content": "=iferror(C74/E74,0)", "style": 20, "format": "0.00%"}, "G75": {"content": "=iferror(C75/E75,0)", "style": 20, "format": "0.00%"}, "G76": {"content": "=iferror(C76/E76,0)", "style": 20, "format": "0.00%"}, "G77": {"content": "=iferror(C77/E77,0)", "style": 20, "format": "0.00%"}, "G78": {"content": "=iferror(C78/E78,0)", "style": 20, "format": "0.00%"}, "G79": {"content": "=iferror(C79/E79,0)", "style": 20, "format": "0.00%"}, "G80": {"content": "=iferror(C80/E80,0)", "style": 20, "format": "0.00%"}, "G81": {"content": "=iferror(C81/E81,0)", "style": 20, "format": "0.00%"}, "G82": {"content": "=iferror(C82/E82,0)", "style": 20, "format": "0.00%"}, "G83": {"content": "=iferror(C83/E83,0)", "style": 20, "format": "0.00%"}, "G84": {"content": "=iferror(C84/E84,0)", "style": 20, "format": "0.00%"}, "G85": {"content": "=iferror(C85/E85,0)", "style": 20, "format": "0.00%"}, "G86": {"content": "=iferror(C86/E86,0)", "style": 20, "format": "0.00%"}, "G87": {"content": "=iferror(C87/E87,0)", "style": 20, "format": "0.00%"}, "G88": {"content": "=iferror(C88/E88,0)", "style": 20, "format": "0.00%"}, "H74": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H75": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H76": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H77": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H78": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H79": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H80": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H81": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H82": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H83": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H84": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H85": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H86": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H87": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H88": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I74": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I75": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I76": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I77": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I78": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I79": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I80": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I81": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I82": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I83": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I84": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I85": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I86": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I87": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I88": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J74": {"content": "", "style": 7}, "J75": {"content": "", "style": 7}, "J76": {"content": "", "style": 7}, "J77": {"content": "", "style": 7}, "J78": {"content": "", "style": 7}, "J79": {"content": "", "style": 7}, "J80": {"content": "", "style": 7}, "J81": {"content": "", "style": 7}, "J82": {"content": "", "style": 7}, "J83": {"content": "", "style": 7}, "J84": {"content": "", "style": 7}, "J85": {"content": "", "style": 7}, "J86": {"content": "", "style": 7}, "J87": {"content": "", "style": 7}, "J88": {"content": "", "style": 7}, "K74": {"content": "", "style": 7}, "K75": {"content": "", "style": 7}, "K76": {"content": "", "style": 7}, "K77": {"content": "", "style": 7}, "K78": {"content": "", "style": 7}, "K79": {"content": "", "style": 7}, "K80": {"content": "", "style": 7}, "K81": {"content": "", "style": 7}, "K82": {"content": "", "style": 7}, "K83": {"content": "", "style": 7}, "K84": {"content": "", "style": 7}, "K85": {"content": "", "style": 7}, "K86": {"content": "", "style": 7}, "K87": {"content": "", "style": 7}, "K88": {"content": "", "style": 7}, "L74": {"content": "", "style": 7}, "L75": {"content": "", "style": 7}, "L76": {"content": "", "style": 7}, "L77": {"content": "", "style": 7}, "L78": {"content": "", "style": 7}, "L79": {"content": "", "style": 7}, "L80": {"content": "", "style": 7}, "L81": {"content": "", "style": 7}, "L82": {"content": "", "style": 7}, "L83": {"content": "", "style": 7}, "L84": {"content": "", "style": 7}, "L85": {"content": "", "style": 7}, "L86": {"content": "", "style": 7}, "L87": {"content": "", "style": 7}, "L88": {"content": "", "style": 7}, "M74": {"content": "", "style": 7}, "M75": {"content": "", "style": 7}, "M76": {"content": "", "style": 7}, "M77": {"content": "", "style": 7}, "M78": {"content": "", "style": 7}, "M79": {"content": "", "style": 7}, "M80": {"content": "", "style": 7}, "M81": {"content": "", "style": 7}, "M82": {"content": "", "style": 7}, "M83": {"content": "", "style": 7}, "M84": {"content": "", "style": 7}, "M85": {"content": "", "style": 7}, "M86": {"content": "", "style": 7}, "M87": {"content": "", "style": 7}, "M88": {"content": "", "style": 7}, "N74": {"content": "", "style": 7}, "N75": {"content": "", "style": 7}, "N76": {"content": "", "style": 7}, "N77": {"content": "", "style": 7}, "N78": {"content": "", "style": 7}, "N79": {"content": "", "style": 7}, "N80": {"content": "", "style": 7}, "N81": {"content": "", "style": 7}, "N82": {"content": "", "style": 7}, "N83": {"content": "", "style": 7}, "N84": {"content": "", "style": 7}, "N85": {"content": "", "style": 7}, "N86": {"content": "", "style": 7}, "N87": {"content": "", "style": 7}, "N88": {"content": "", "style": 7}, "O74": {"content": "", "style": 7}, "O75": {"content": "", "style": 7}, "O76": {"content": "", "style": 7}, "O77": {"content": "", "style": 7}, "O78": {"content": "", "style": 7}, "O79": {"content": "", "style": 7}, "O80": {"content": "", "style": 7}, "O81": {"content": "", "style": 7}, "O82": {"content": "", "style": 7}, "O83": {"content": "", "style": 7}, "O84": {"content": "", "style": 7}, "O85": {"content": "", "style": 7}, "O86": {"content": "", "style": 7}, "O87": {"content": "", "style": 7}, "O88": {"content": "", "style": 7}, "P74": {"content": "", "style": 7}, "P75": {"content": "", "style": 7}, "P76": {"content": "", "style": 7}, "P77": {"content": "", "style": 7}, "P78": {"content": "", "style": 7}, "P79": {"content": "", "style": 7}, "P80": {"content": "", "style": 7}, "P81": {"content": "", "style": 7}, "P82": {"content": "", "style": 7}, "P83": {"content": "", "style": 7}, "P84": {"content": "", "style": 7}, "P85": {"content": "", "style": 7}, "P86": {"content": "", "style": 7}, "P87": {"content": "", "style": 7}, "P88": {"content": "", "style": 7}, "Q74": {"content": "", "style": 7}, "Q75": {"content": "", "style": 7}, "Q76": {"content": "", "style": 7}, "Q77": {"content": "", "style": 7}, "Q78": {"content": "", "style": 7}, "Q79": {"content": "", "style": 7}, "Q80": {"content": "", "style": 7}, "Q81": {"content": "", "style": 7}, "Q82": {"content": "", "style": 7}, "Q83": {"content": "", "style": 7}, "Q84": {"content": "", "style": 7}, "Q85": {"content": "", "style": 7}, "Q86": {"content": "", "style": 7}, "Q87": {"content": "", "style": 7}, "Q88": {"content": "", "style": 7}, "R74": {"content": "", "style": 7}, "R75": {"content": "", "style": 7}, "R76": {"content": "", "style": 7}, "R77": {"content": "", "style": 7}, "R78": {"content": "", "style": 7}, "R79": {"content": "", "style": 7}, "R80": {"content": "", "style": 7}, "R81": {"content": "", "style": 7}, "R82": {"content": "", "style": 7}, "R83": {"content": "", "style": 7}, "R84": {"content": "", "style": 7}, "R85": {"content": "", "style": 7}, "R86": {"content": "", "style": 7}, "R87": {"content": "", "style": 7}, "R88": {"content": "", "style": 7}, "S74": {"content": "", "style": 7}, "S75": {"content": "", "style": 7}, "S76": {"content": "", "style": 7}, "S77": {"content": "", "style": 7}, "S78": {"content": "", "style": 7}, "S79": {"content": "", "style": 7}, "S80": {"content": "", "style": 7}, "S81": {"content": "", "style": 7}, "S82": {"content": "", "style": 7}, "S83": {"content": "", "style": 7}, "S84": {"content": "", "style": 7}, "S85": {"content": "", "style": 7}, "S86": {"content": "", "style": 7}, "S87": {"content": "", "style": 7}, "S88": {"content": "", "style": 7}, "T74": {"content": "", "style": 7}, "T75": {"content": "", "style": 7}, "T76": {"content": "", "style": 7}, "T77": {"content": "", "style": 7}, "T78": {"content": "", "style": 7}, "T79": {"content": "", "style": 7}, "T80": {"content": "", "style": 7}, "T81": {"content": "", "style": 7}, "T82": {"content": "", "style": 7}, "T83": {"content": "", "style": 7}, "T84": {"content": "", "style": 7}, "T85": {"content": "", "style": 7}, "T86": {"content": "", "style": 7}, "T87": {"content": "", "style": 7}, "T88": {"content": "", "style": 7}, "U74": {"content": "", "style": 7}, "U75": {"content": "", "style": 7}, "U76": {"content": "", "style": 7}, "U77": {"content": "", "style": 7}, "U78": {"content": "", "style": 7}, "U79": {"content": "", "style": 7}, "U80": {"content": "", "style": 7}, "U81": {"content": "", "style": 7}, "U82": {"content": "", "style": 7}, "U83": {"content": "", "style": 7}, "U84": {"content": "", "style": 7}, "U85": {"content": "", "style": 7}, "U86": {"content": "", "style": 7}, "U87": {"content": "", "style": 7}, "U88": {"content": "", "style": 7}, "V74": {"content": "", "style": 7}, "V75": {"content": "", "style": 7}, "V76": {"content": "", "style": 7}, "V77": {"content": "", "style": 7}, "V78": {"content": "", "style": 7}, "V79": {"content": "", "style": 7}, "V80": {"content": "", "style": 7}, "V81": {"content": "", "style": 7}, "V82": {"content": "", "style": 7}, "V83": {"content": "", "style": 7}, "V84": {"content": "", "style": 7}, "V85": {"content": "", "style": 7}, "V86": {"content": "", "style": 7}, "V87": {"content": "", "style": 7}, "V88": {"content": "", "style": 7}, "A53": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A54": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A55": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A56": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A57": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A58": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A59": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A60": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A61": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A62": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A63": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A64": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A65": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A66": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A67": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B53": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B54": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B55": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B56": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B57": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B58": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B59": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B60": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B61": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B62": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B63": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B64": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B65": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B66": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B67": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C53": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C54": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C55": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C56": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C57": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C58": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C59": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C60": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C61": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C62": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C63": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C64": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C65": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C66": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C67": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D53": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D54": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D55": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D56": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D57": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D58": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D59": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D60": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D61": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D62": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D63": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D64": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D65": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D66": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D67": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E53": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E54": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E55": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E56": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E57": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E58": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E59": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E60": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E61": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E62": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E63": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E64": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E65": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E66": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E67": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F53": {"content": "=iferror(B53/D53,0)", "style": 20, "format": "0.00%"}, "F54": {"content": "=iferror(B54/D54,0)", "style": 20, "format": "0.00%"}, "F55": {"content": "=iferror(B55/D55,0)", "style": 20, "format": "0.00%"}, "F56": {"content": "=iferror(B56/D56,0)", "style": 20, "format": "0.00%"}, "F57": {"content": "=iferror(B57/D57,0)", "style": 20, "format": "0.00%"}, "F58": {"content": "=iferror(B58/D58,0)", "style": 20, "format": "0.00%"}, "F59": {"content": "=iferror(B59/D59,0)", "style": 20, "format": "0.00%"}, "F60": {"content": "=iferror(B60/D60,0)", "style": 20, "format": "0.00%"}, "F61": {"content": "=iferror(B61/D61,0)", "style": 20, "format": "0.00%"}, "F62": {"content": "=iferror(B62/D62,0)", "style": 20, "format": "0.00%"}, "F63": {"content": "=iferror(B63/D63,0)", "style": 20, "format": "0.00%"}, "F64": {"content": "=iferror(B64/D64,0)", "style": 20, "format": "0.00%"}, "F65": {"content": "=iferror(B65/D65,0)", "style": 20, "format": "0.00%"}, "F66": {"content": "=iferror(B66/D66,0)", "style": 20, "format": "0.00%"}, "F67": {"content": "=iferror(B67/D67,0)", "style": 20, "format": "0.00%"}, "G53": {"content": "=iferror(C53/E53,0)", "style": 20, "format": "0.00%"}, "G54": {"content": "=iferror(C54/E54,0)", "style": 20, "format": "0.00%"}, "G55": {"content": "=iferror(C55/E55,0)", "style": 20, "format": "0.00%"}, "G56": {"content": "=iferror(C56/E56,0)", "style": 20, "format": "0.00%"}, "G57": {"content": "=iferror(C57/E57,0)", "style": 20, "format": "0.00%"}, "G58": {"content": "=iferror(C58/E58,0)", "style": 20, "format": "0.00%"}, "G59": {"content": "=iferror(C59/E59,0)", "style": 20, "format": "0.00%"}, "G60": {"content": "=iferror(C60/E60,0)", "style": 20, "format": "0.00%"}, "G61": {"content": "=iferror(C61/E61,0)", "style": 20, "format": "0.00%"}, "G62": {"content": "=iferror(C62/E62,0)", "style": 20, "format": "0.00%"}, "G63": {"content": "=iferror(C63/E63,0)", "style": 20, "format": "0.00%"}, "G64": {"content": "=iferror(C64/E64,0)", "style": 20, "format": "0.00%"}, "G65": {"content": "=iferror(C65/E65,0)", "style": 20, "format": "0.00%"}, "G66": {"content": "=iferror(C66/E66,0)", "style": 20, "format": "0.00%"}, "G67": {"content": "=iferror(C67/E67,0)", "style": 20, "format": "0.00%"}, "H53": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H54": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H55": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H56": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H57": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H58": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H59": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H60": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H61": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H62": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H63": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H64": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H65": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H66": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H67": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I53": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I54": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I55": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I56": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I57": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I58": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I59": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I60": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I61": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I62": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I63": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I64": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I65": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I66": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I67": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J53": {"content": "", "style": 7}, "J54": {"content": "", "style": 7}, "J55": {"content": "", "style": 7}, "J56": {"content": "", "style": 7}, "J57": {"content": "", "style": 7}, "J58": {"content": "", "style": 7}, "J59": {"content": "", "style": 7}, "J60": {"content": "", "style": 7}, "J61": {"content": "", "style": 7}, "J62": {"content": "", "style": 7}, "J63": {"content": "", "style": 7}, "J64": {"content": "", "style": 7}, "J65": {"content": "", "style": 7}, "J66": {"content": "", "style": 7}, "J67": {"content": "", "style": 7}, "K53": {"content": "", "style": 7}, "K54": {"content": "", "style": 7}, "K55": {"content": "", "style": 7}, "K56": {"content": "", "style": 7}, "K57": {"content": "", "style": 7}, "K58": {"content": "", "style": 7}, "K59": {"content": "", "style": 7}, "K60": {"content": "", "style": 7}, "K61": {"content": "", "style": 7}, "K62": {"content": "", "style": 7}, "K63": {"content": "", "style": 7}, "K64": {"content": "", "style": 7}, "K65": {"content": "", "style": 7}, "K66": {"content": "", "style": 7}, "K67": {"content": "", "style": 7}, "L53": {"content": "", "style": 7}, "L54": {"content": "", "style": 7}, "L55": {"content": "", "style": 7}, "L56": {"content": "", "style": 7}, "L57": {"content": "", "style": 7}, "L58": {"content": "", "style": 7}, "L59": {"content": "", "style": 7}, "L60": {"content": "", "style": 7}, "L61": {"content": "", "style": 7}, "L62": {"content": "", "style": 7}, "L63": {"content": "", "style": 7}, "L64": {"content": "", "style": 7}, "L65": {"content": "", "style": 7}, "L66": {"content": "", "style": 7}, "L67": {"content": "", "style": 7}, "M53": {"content": "", "style": 7}, "M54": {"content": "", "style": 7}, "M55": {"content": "", "style": 7}, "M56": {"content": "", "style": 7}, "M57": {"content": "", "style": 7}, "M58": {"content": "", "style": 7}, "M59": {"content": "", "style": 7}, "M60": {"content": "", "style": 7}, "M61": {"content": "", "style": 7}, "M62": {"content": "", "style": 7}, "M63": {"content": "", "style": 7}, "M64": {"content": "", "style": 7}, "M65": {"content": "", "style": 7}, "M66": {"content": "", "style": 7}, "M67": {"content": "", "style": 7}, "N53": {"content": "", "style": 7}, "N54": {"content": "", "style": 7}, "N55": {"content": "", "style": 7}, "N56": {"content": "", "style": 7}, "N57": {"content": "", "style": 7}, "N58": {"content": "", "style": 7}, "N59": {"content": "", "style": 7}, "N60": {"content": "", "style": 7}, "N61": {"content": "", "style": 7}, "N62": {"content": "", "style": 7}, "N63": {"content": "", "style": 7}, "N64": {"content": "", "style": 7}, "N65": {"content": "", "style": 7}, "N66": {"content": "", "style": 7}, "N67": {"content": "", "style": 7}, "O53": {"content": "", "style": 7}, "O54": {"content": "", "style": 7}, "O55": {"content": "", "style": 7}, "O56": {"content": "", "style": 7}, "O57": {"content": "", "style": 7}, "O58": {"content": "", "style": 7}, "O59": {"content": "", "style": 7}, "O60": {"content": "", "style": 7}, "O61": {"content": "", "style": 7}, "O62": {"content": "", "style": 7}, "O63": {"content": "", "style": 7}, "O64": {"content": "", "style": 7}, "O65": {"content": "", "style": 7}, "O66": {"content": "", "style": 7}, "O67": {"content": "", "style": 7}, "P53": {"content": "", "style": 7}, "P54": {"content": "", "style": 7}, "P55": {"content": "", "style": 7}, "P56": {"content": "", "style": 7}, "P57": {"content": "", "style": 7}, "P58": {"content": "", "style": 7}, "P59": {"content": "", "style": 7}, "P60": {"content": "", "style": 7}, "P61": {"content": "", "style": 7}, "P62": {"content": "", "style": 7}, "P63": {"content": "", "style": 7}, "P64": {"content": "", "style": 7}, "P65": {"content": "", "style": 7}, "P66": {"content": "", "style": 7}, "P67": {"content": "", "style": 7}, "Q53": {"content": "", "style": 7}, "Q54": {"content": "", "style": 7}, "Q55": {"content": "", "style": 7}, "Q56": {"content": "", "style": 7}, "Q57": {"content": "", "style": 7}, "Q58": {"content": "", "style": 7}, "Q59": {"content": "", "style": 7}, "Q60": {"content": "", "style": 7}, "Q61": {"content": "", "style": 7}, "Q62": {"content": "", "style": 7}, "Q63": {"content": "", "style": 7}, "Q64": {"content": "", "style": 7}, "Q65": {"content": "", "style": 7}, "Q66": {"content": "", "style": 7}, "Q67": {"content": "", "style": 7}, "R53": {"content": "", "style": 7}, "R54": {"content": "", "style": 7}, "R55": {"content": "", "style": 7}, "R56": {"content": "", "style": 7}, "R57": {"content": "", "style": 7}, "R58": {"content": "", "style": 7}, "R59": {"content": "", "style": 7}, "R60": {"content": "", "style": 7}, "R61": {"content": "", "style": 7}, "R62": {"content": "", "style": 7}, "R63": {"content": "", "style": 7}, "R64": {"content": "", "style": 7}, "R65": {"content": "", "style": 7}, "R66": {"content": "", "style": 7}, "R67": {"content": "", "style": 7}, "S53": {"content": "", "style": 7}, "S54": {"content": "", "style": 7}, "S55": {"content": "", "style": 7}, "S56": {"content": "", "style": 7}, "S57": {"content": "", "style": 7}, "S58": {"content": "", "style": 7}, "S59": {"content": "", "style": 7}, "S60": {"content": "", "style": 7}, "S61": {"content": "", "style": 7}, "S62": {"content": "", "style": 7}, "S63": {"content": "", "style": 7}, "S64": {"content": "", "style": 7}, "S65": {"content": "", "style": 7}, "S66": {"content": "", "style": 7}, "S67": {"content": "", "style": 7}, "T53": {"content": "", "style": 7}, "T54": {"content": "", "style": 7}, "T55": {"content": "", "style": 7}, "T56": {"content": "", "style": 7}, "T57": {"content": "", "style": 7}, "T58": {"content": "", "style": 7}, "T59": {"content": "", "style": 7}, "T60": {"content": "", "style": 7}, "T61": {"content": "", "style": 7}, "T62": {"content": "", "style": 7}, "T63": {"content": "", "style": 7}, "T64": {"content": "", "style": 7}, "T65": {"content": "", "style": 7}, "T66": {"content": "", "style": 7}, "T67": {"content": "", "style": 7}, "U53": {"content": "", "style": 7}, "U54": {"content": "", "style": 7}, "U55": {"content": "", "style": 7}, "U56": {"content": "", "style": 7}, "U57": {"content": "", "style": 7}, "U58": {"content": "", "style": 7}, "U59": {"content": "", "style": 7}, "U60": {"content": "", "style": 7}, "U61": {"content": "", "style": 7}, "U62": {"content": "", "style": 7}, "U63": {"content": "", "style": 7}, "U64": {"content": "", "style": 7}, "U65": {"content": "", "style": 7}, "U66": {"content": "", "style": 7}, "U67": {"content": "", "style": 7}, "V53": {"content": "", "style": 7}, "V54": {"content": "", "style": 7}, "V55": {"content": "", "style": 7}, "V56": {"content": "", "style": 7}, "V57": {"content": "", "style": 7}, "V58": {"content": "", "style": 7}, "V59": {"content": "", "style": 7}, "V60": {"content": "", "style": 7}, "V61": {"content": "", "style": 7}, "V62": {"content": "", "style": 7}, "V63": {"content": "", "style": 7}, "V64": {"content": "", "style": 7}, "V65": {"content": "", "style": 7}, "V66": {"content": "", "style": 7}, "V67": {"content": "", "style": 7}, "A32": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A33": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A34": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A35": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A36": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A37": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A38": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A39": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A40": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A41": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A42": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A43": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A44": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A45": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A46": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B32": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B33": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B34": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B35": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B36": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B37": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B38": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B39": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B40": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B41": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B42": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B43": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B44": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B45": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B46": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C32": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C33": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C34": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C35": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C36": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C37": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C38": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C39": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C40": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C41": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C42": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C43": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C44": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C45": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C46": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D32": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D33": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D34": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D35": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D36": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D37": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D38": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D39": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D40": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D41": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D42": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D43": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D44": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D45": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D46": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E32": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E33": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E34": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E35": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E36": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E37": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E38": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E39": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E40": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E41": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E42": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E43": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E44": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E45": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E46": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F32": {"content": "=iferror(B32/D32,0)", "style": 20, "format": "0.00%"}, "F33": {"content": "=iferror(B33/D33,0)", "style": 20, "format": "0.00%"}, "F34": {"content": "=iferror(B34/D34,0)", "style": 20, "format": "0.00%"}, "F35": {"content": "=iferror(B35/D35,0)", "style": 20, "format": "0.00%"}, "F36": {"content": "=iferror(B36/D36,0)", "style": 20, "format": "0.00%"}, "F37": {"content": "=iferror(B37/D37,0)", "style": 20, "format": "0.00%"}, "F38": {"content": "=iferror(B38/D38,0)", "style": 20, "format": "0.00%"}, "F39": {"content": "=iferror(B39/D39,0)", "style": 20, "format": "0.00%"}, "F40": {"content": "=iferror(B40/D40,0)", "style": 20, "format": "0.00%"}, "F41": {"content": "=iferror(B41/D41,0)", "style": 20, "format": "0.00%"}, "F42": {"content": "=iferror(B42/D42,0)", "style": 20, "format": "0.00%"}, "F43": {"content": "=iferror(B43/D43,0)", "style": 20, "format": "0.00%"}, "F44": {"content": "=iferror(B44/D44,0)", "style": 20, "format": "0.00%"}, "F45": {"content": "=iferror(B45/D45,0)", "style": 20, "format": "0.00%"}, "F46": {"content": "=iferror(B46/D46,0)", "style": 20, "format": "0.00%"}, "G32": {"content": "=iferror(C32/E32,0)", "style": 20, "format": "0.00%"}, "G33": {"content": "=iferror(C33/E33,0)", "style": 20, "format": "0.00%"}, "G34": {"content": "=iferror(C34/E34,0)", "style": 20, "format": "0.00%"}, "G35": {"content": "=iferror(C35/E35,0)", "style": 20, "format": "0.00%"}, "G36": {"content": "=iferror(C36/E36,0)", "style": 20, "format": "0.00%"}, "G37": {"content": "=iferror(C37/E37,0)", "style": 20, "format": "0.00%"}, "G38": {"content": "=iferror(C38/E38,0)", "style": 20, "format": "0.00%"}, "G39": {"content": "=iferror(C39/E39,0)", "style": 20, "format": "0.00%"}, "G40": {"content": "=iferror(C40/E40,0)", "style": 20, "format": "0.00%"}, "G41": {"content": "=iferror(C41/E41,0)", "style": 20, "format": "0.00%"}, "G42": {"content": "=iferror(C42/E42,0)", "style": 20, "format": "0.00%"}, "G43": {"content": "=iferror(C43/E43,0)", "style": 20, "format": "0.00%"}, "G44": {"content": "=iferror(C44/E44,0)", "style": 20, "format": "0.00%"}, "G45": {"content": "=iferror(C45/E45,0)", "style": 20, "format": "0.00%"}, "G46": {"content": "=iferror(C46/E46,0)", "style": 20, "format": "0.00%"}, "H32": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H33": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H34": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H35": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H36": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H37": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H38": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H39": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H40": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H41": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H42": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H43": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H44": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H45": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H46": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I32": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I33": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I34": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I35": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I36": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I37": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I38": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I39": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I40": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I41": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I42": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I43": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I44": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I45": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I46": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J32": {"content": "", "style": 7}, "J33": {"content": "", "style": 7}, "J34": {"content": "", "style": 7}, "J35": {"content": "", "style": 7}, "J36": {"content": "", "style": 7}, "J37": {"content": "", "style": 7}, "J38": {"content": "", "style": 7}, "J39": {"content": "", "style": 7}, "J40": {"content": "", "style": 7}, "J41": {"content": "", "style": 7}, "J42": {"content": "", "style": 7}, "J43": {"content": "", "style": 7}, "J44": {"content": "", "style": 7}, "J45": {"content": "", "style": 7}, "J46": {"content": "", "style": 7}, "K32": {"content": "", "style": 7}, "K33": {"content": "", "style": 7}, "K34": {"content": "", "style": 7}, "K35": {"content": "", "style": 7}, "K36": {"content": "", "style": 7}, "K37": {"content": "", "style": 7}, "K38": {"content": "", "style": 7}, "K39": {"content": "", "style": 7}, "K40": {"content": "", "style": 7}, "K41": {"content": "", "style": 7}, "K42": {"content": "", "style": 7}, "K43": {"content": "", "style": 7}, "K44": {"content": "", "style": 7}, "K45": {"content": "", "style": 7}, "K46": {"content": "", "style": 7}, "L32": {"content": "", "style": 7}, "L33": {"content": "", "style": 7}, "L34": {"content": "", "style": 7}, "L35": {"content": "", "style": 7}, "L36": {"content": "", "style": 7}, "L37": {"content": "", "style": 7}, "L38": {"content": "", "style": 7}, "L39": {"content": "", "style": 7}, "L40": {"content": "", "style": 7}, "L41": {"content": "", "style": 7}, "L42": {"content": "", "style": 7}, "L43": {"content": "", "style": 7}, "L44": {"content": "", "style": 7}, "L45": {"content": "", "style": 7}, "L46": {"content": "", "style": 7}, "M32": {"content": "", "style": 7}, "M33": {"content": "", "style": 7}, "M34": {"content": "", "style": 7}, "M35": {"content": "", "style": 7}, "M36": {"content": "", "style": 7}, "M37": {"content": "", "style": 7}, "M38": {"content": "", "style": 7}, "M39": {"content": "", "style": 7}, "M40": {"content": "", "style": 7}, "M41": {"content": "", "style": 7}, "M42": {"content": "", "style": 7}, "M43": {"content": "", "style": 7}, "M44": {"content": "", "style": 7}, "M45": {"content": "", "style": 7}, "M46": {"content": "", "style": 7}, "N32": {"content": "", "style": 7}, "N33": {"content": "", "style": 7}, "N34": {"content": "", "style": 7}, "N35": {"content": "", "style": 7}, "N36": {"content": "", "style": 7}, "N37": {"content": "", "style": 7}, "N38": {"content": "", "style": 7}, "N39": {"content": "", "style": 7}, "N40": {"content": "", "style": 7}, "N41": {"content": "", "style": 7}, "N42": {"content": "", "style": 7}, "N43": {"content": "", "style": 7}, "N44": {"content": "", "style": 7}, "N45": {"content": "", "style": 7}, "N46": {"content": "", "style": 7}, "O32": {"content": "", "style": 7}, "O33": {"content": "", "style": 7}, "O34": {"content": "", "style": 7}, "O35": {"content": "", "style": 7}, "O36": {"content": "", "style": 7}, "O37": {"content": "", "style": 7}, "O38": {"content": "", "style": 7}, "O39": {"content": "", "style": 7}, "O40": {"content": "", "style": 7}, "O41": {"content": "", "style": 7}, "O42": {"content": "", "style": 7}, "O43": {"content": "", "style": 7}, "O44": {"content": "", "style": 7}, "O45": {"content": "", "style": 7}, "O46": {"content": "", "style": 7}, "P32": {"content": "", "style": 7}, "P33": {"content": "", "style": 7}, "P34": {"content": "", "style": 7}, "P35": {"content": "", "style": 7}, "P36": {"content": "", "style": 7}, "P37": {"content": "", "style": 7}, "P38": {"content": "", "style": 7}, "P39": {"content": "", "style": 7}, "P40": {"content": "", "style": 7}, "P41": {"content": "", "style": 7}, "P42": {"content": "", "style": 7}, "P43": {"content": "", "style": 7}, "P44": {"content": "", "style": 7}, "P45": {"content": "", "style": 7}, "P46": {"content": "", "style": 7}, "Q32": {"content": "", "style": 7}, "Q33": {"content": "", "style": 7}, "Q34": {"content": "", "style": 7}, "Q35": {"content": "", "style": 7}, "Q36": {"content": "", "style": 7}, "Q37": {"content": "", "style": 7}, "Q38": {"content": "", "style": 7}, "Q39": {"content": "", "style": 7}, "Q40": {"content": "", "style": 7}, "Q41": {"content": "", "style": 7}, "Q42": {"content": "", "style": 7}, "Q43": {"content": "", "style": 7}, "Q44": {"content": "", "style": 7}, "Q45": {"content": "", "style": 7}, "Q46": {"content": "", "style": 7}, "R32": {"content": "", "style": 7}, "R33": {"content": "", "style": 7}, "R34": {"content": "", "style": 7}, "R35": {"content": "", "style": 7}, "R36": {"content": "", "style": 7}, "R37": {"content": "", "style": 7}, "R38": {"content": "", "style": 7}, "R39": {"content": "", "style": 7}, "R40": {"content": "", "style": 7}, "R41": {"content": "", "style": 7}, "R42": {"content": "", "style": 7}, "R43": {"content": "", "style": 7}, "R44": {"content": "", "style": 7}, "R45": {"content": "", "style": 7}, "R46": {"content": "", "style": 7}, "S32": {"content": "", "style": 7}, "S33": {"content": "", "style": 7}, "S34": {"content": "", "style": 7}, "S35": {"content": "", "style": 7}, "S36": {"content": "", "style": 7}, "S37": {"content": "", "style": 7}, "S38": {"content": "", "style": 7}, "S39": {"content": "", "style": 7}, "S40": {"content": "", "style": 7}, "S41": {"content": "", "style": 7}, "S42": {"content": "", "style": 7}, "S43": {"content": "", "style": 7}, "S44": {"content": "", "style": 7}, "S45": {"content": "", "style": 7}, "S46": {"content": "", "style": 7}, "T32": {"content": "", "style": 7}, "T33": {"content": "", "style": 7}, "T34": {"content": "", "style": 7}, "T35": {"content": "", "style": 7}, "T36": {"content": "", "style": 7}, "T37": {"content": "", "style": 7}, "T38": {"content": "", "style": 7}, "T39": {"content": "", "style": 7}, "T40": {"content": "", "style": 7}, "T41": {"content": "", "style": 7}, "T42": {"content": "", "style": 7}, "T43": {"content": "", "style": 7}, "T44": {"content": "", "style": 7}, "T45": {"content": "", "style": 7}, "T46": {"content": "", "style": 7}, "U32": {"content": "", "style": 7}, "U33": {"content": "", "style": 7}, "U34": {"content": "", "style": 7}, "U35": {"content": "", "style": 7}, "U36": {"content": "", "style": 7}, "U37": {"content": "", "style": 7}, "U38": {"content": "", "style": 7}, "U39": {"content": "", "style": 7}, "U40": {"content": "", "style": 7}, "U41": {"content": "", "style": 7}, "U42": {"content": "", "style": 7}, "U43": {"content": "", "style": 7}, "U44": {"content": "", "style": 7}, "U45": {"content": "", "style": 7}, "U46": {"content": "", "style": 7}, "V32": {"content": "", "style": 7}, "V33": {"content": "", "style": 7}, "V34": {"content": "", "style": 7}, "V35": {"content": "", "style": 7}, "V36": {"content": "", "style": 7}, "V37": {"content": "", "style": 7}, "V38": {"content": "", "style": 7}, "V39": {"content": "", "style": 7}, "V40": {"content": "", "style": 7}, "V41": {"content": "", "style": 7}, "V42": {"content": "", "style": 7}, "V43": {"content": "", "style": 7}, "V44": {"content": "", "style": 7}, "V45": {"content": "", "style": 7}, "V46": {"content": "", "style": 7}, "A11": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "style": 2}, "A12": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "style": 2}, "A13": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "style": 2}, "A14": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "style": 2}, "A15": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "style": 2}, "A16": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "style": 2}, "A17": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "style": 2}, "A18": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "style": 2}, "A19": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "style": 2}, "A20": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "style": 2}, "A21": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "style": 2}, "A22": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "style": 2}, "A23": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "style": 2}, "A24": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "style": 2}, "A25": {"content": "=PIVOT.HEADER(\"1\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "style": 2}, "B11": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "B12": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "B13": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "B14": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "B15": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "B16": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "B17": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "B18": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "B19": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "B20": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "B21": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "B22": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "B23": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "B24": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "B25": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "C11": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6),\"won_status\",\"won\")", "format": "#,##0.00"}, "C12": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7),\"won_status\",\"won\")", "format": "#,##0.00"}, "C13": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8),\"won_status\",\"won\")", "format": "#,##0.00"}, "C14": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9),\"won_status\",\"won\")", "format": "#,##0.00"}, "C15": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10),\"won_status\",\"won\")", "format": "#,##0.00"}, "C16": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11),\"won_status\",\"won\")", "format": "#,##0.00"}, "C17": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12),\"won_status\",\"won\")", "format": "#,##0.00"}, "C18": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13),\"won_status\",\"won\")", "format": "#,##0.00"}, "C19": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14),\"won_status\",\"won\")", "format": "#,##0.00"}, "C20": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15),\"won_status\",\"won\")", "format": "#,##0.00"}, "C21": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16),\"won_status\",\"won\")", "format": "#,##0.00"}, "C22": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17),\"won_status\",\"won\")", "format": "#,##0.00"}, "C23": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18),\"won_status\",\"won\")", "format": "#,##0.00"}, "C24": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19),\"won_status\",\"won\")", "format": "#,##0.00"}, "C25": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20),\"won_status\",\"won\")", "format": "#,##0.00"}, "D11": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D12": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D13": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D14": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D15": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D16": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D17": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D18": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D19": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D20": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D21": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D22": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D23": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D24": {"content": "100000", "style": 33, "format": "#,##0.00"}, "D25": {"content": "100000", "style": 33, "format": "#,##0.00"}, "E11": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E12": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E13": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E14": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E15": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E16": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E17": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E18": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E19": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E20": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E21": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E22": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E23": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E24": {"content": "50000", "style": 33, "format": "#,##0.00"}, "E25": {"content": "50000", "style": 33, "format": "#,##0.00"}, "F11": {"content": "=iferror(B11/D11,0)", "style": 20, "format": "0.00%"}, "F12": {"content": "=iferror(B12/D12,0)", "style": 20, "format": "0.00%"}, "F13": {"content": "=iferror(B13/D13,0)", "style": 20, "format": "0.00%"}, "F14": {"content": "=iferror(B14/D14,0)", "style": 20, "format": "0.00%"}, "F15": {"content": "=iferror(B15/D15,0)", "style": 20, "format": "0.00%"}, "F16": {"content": "=iferror(B16/D16,0)", "style": 20, "format": "0.00%"}, "F17": {"content": "=iferror(B17/D17,0)", "style": 20, "format": "0.00%"}, "F18": {"content": "=iferror(B18/D18,0)", "style": 20, "format": "0.00%"}, "F19": {"content": "=iferror(B19/D19,0)", "style": 20, "format": "0.00%"}, "F20": {"content": "=iferror(B20/D20,0)", "style": 20, "format": "0.00%"}, "F21": {"content": "=iferror(B21/D21,0)", "style": 20, "format": "0.00%"}, "F22": {"content": "=iferror(B22/D22,0)", "style": 20, "format": "0.00%"}, "F23": {"content": "=iferror(B23/D23,0)", "style": 20, "format": "0.00%"}, "F24": {"content": "=iferror(B24/D24,0)", "style": 20, "format": "0.00%"}, "F25": {"content": "=iferror(B25/D25,0)", "style": 20, "format": "0.00%"}, "G11": {"content": "=iferror(C11/E11,0)", "style": 20, "format": "0.00%"}, "G12": {"content": "=iferror(C12/E12,0)", "style": 20, "format": "0.00%"}, "G13": {"content": "=iferror(C13/E13,0)", "style": 20, "format": "0.00%"}, "G14": {"content": "=iferror(C14/E14,0)", "style": 20, "format": "0.00%"}, "G15": {"content": "=iferror(C15/E15,0)", "style": 20, "format": "0.00%"}, "G16": {"content": "=iferror(C16/E16,0)", "style": 20, "format": "0.00%"}, "G17": {"content": "=iferror(C17/E17,0)", "style": 20, "format": "0.00%"}, "G18": {"content": "=iferror(C18/E18,0)", "style": 20, "format": "0.00%"}, "G19": {"content": "=iferror(C19/E19,0)", "style": 20, "format": "0.00%"}, "G20": {"content": "=iferror(C20/E20,0)", "style": 20, "format": "0.00%"}, "G21": {"content": "=iferror(C21/E21,0)", "style": 20, "format": "0.00%"}, "G22": {"content": "=iferror(C22/E22,0)", "style": 20, "format": "0.00%"}, "G23": {"content": "=iferror(C23/E23,0)", "style": 20, "format": "0.00%"}, "G24": {"content": "=iferror(C24/E24,0)", "style": 20, "format": "0.00%"}, "G25": {"content": "=iferror(C25/E25,0)", "style": 20, "format": "0.00%"}, "H11": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "H12": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "H13": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "H14": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "H15": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "H16": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "H17": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "H18": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "H19": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "H20": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "H21": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "H22": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "H23": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "H24": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "H25": {"content": "=PIVOT(\"1\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "I11": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",6))", "format": "#,##0.00"}, "I12": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",7))", "format": "#,##0.00"}, "I13": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",8))", "format": "#,##0.00"}, "I14": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",9))", "format": "#,##0.00"}, "I15": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",10))", "format": "#,##0.00"}, "I16": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",11))", "format": "#,##0.00"}, "I17": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",12))", "format": "#,##0.00"}, "I18": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",13))", "format": "#,##0.00"}, "I19": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",14))", "format": "#,##0.00"}, "I20": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",15))", "format": "#,##0.00"}, "I21": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",16))", "format": "#,##0.00"}, "I22": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",17))", "format": "#,##0.00"}, "I23": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",18))", "format": "#,##0.00"}, "I24": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",19))", "format": "#,##0.00"}, "I25": {"content": "=PIVOT(\"1\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"team_id\",PIVOT.POSITION(\"1\",\"team_id\",20))", "format": "#,##0.00"}, "J11": {"content": "", "style": 7}, "J12": {"content": "", "style": 7}, "J13": {"content": "", "style": 7}, "J14": {"content": "", "style": 7}, "J15": {"content": "", "style": 7}, "J16": {"content": "", "style": 7}, "J17": {"content": "", "style": 7}, "J18": {"content": "", "style": 7}, "J19": {"content": "", "style": 7}, "J20": {"content": "", "style": 7}, "J21": {"content": "", "style": 7}, "J22": {"content": "", "style": 7}, "J23": {"content": "", "style": 7}, "J24": {"content": "", "style": 7}, "J25": {"content": "", "style": 7}, "K11": {"content": "", "style": 7}, "K12": {"content": "", "style": 7}, "K13": {"content": "", "style": 7}, "K14": {"content": "", "style": 7}, "K15": {"content": "", "style": 7}, "K16": {"content": "", "style": 7}, "K17": {"content": "", "style": 7}, "K18": {"content": "", "style": 7}, "K19": {"content": "", "style": 7}, "K20": {"content": "", "style": 7}, "K21": {"content": "", "style": 7}, "K22": {"content": "", "style": 7}, "K23": {"content": "", "style": 7}, "K24": {"content": "", "style": 7}, "K25": {"content": "", "style": 7}, "L11": {"content": "", "style": 7}, "L12": {"content": "", "style": 7}, "L13": {"content": "", "style": 7}, "L14": {"content": "", "style": 7}, "L15": {"content": "", "style": 7}, "L16": {"content": "", "style": 7}, "L17": {"content": "", "style": 7}, "L18": {"content": "", "style": 7}, "L19": {"content": "", "style": 7}, "L20": {"content": "", "style": 7}, "L21": {"content": "", "style": 7}, "L22": {"content": "", "style": 7}, "L23": {"content": "", "style": 7}, "L24": {"content": "", "style": 7}, "L25": {"content": "", "style": 7}, "M11": {"content": "", "style": 7}, "M12": {"content": "", "style": 7}, "M13": {"content": "", "style": 7}, "M14": {"content": "", "style": 7}, "M15": {"content": "", "style": 7}, "M16": {"content": "", "style": 7}, "M17": {"content": "", "style": 7}, "M18": {"content": "", "style": 7}, "M19": {"content": "", "style": 7}, "M20": {"content": "", "style": 7}, "M21": {"content": "", "style": 7}, "M22": {"content": "", "style": 7}, "M23": {"content": "", "style": 7}, "M24": {"content": "", "style": 7}, "M25": {"content": "", "style": 7}, "N11": {"content": "", "style": 7}, "N12": {"content": "", "style": 7}, "N13": {"content": "", "style": 7}, "N14": {"content": "", "style": 7}, "N15": {"content": "", "style": 7}, "N16": {"content": "", "style": 7}, "N17": {"content": "", "style": 7}, "N18": {"content": "", "style": 7}, "N19": {"content": "", "style": 7}, "N20": {"content": "", "style": 7}, "N21": {"content": "", "style": 7}, "N22": {"content": "", "style": 7}, "N23": {"content": "", "style": 7}, "N24": {"content": "", "style": 7}, "N25": {"content": "", "style": 7}, "O11": {"content": "", "style": 7}, "O12": {"content": "", "style": 7}, "O13": {"content": "", "style": 7}, "O14": {"content": "", "style": 7}, "O15": {"content": "", "style": 7}, "O16": {"content": "", "style": 7}, "O17": {"content": "", "style": 7}, "O18": {"content": "", "style": 7}, "O19": {"content": "", "style": 7}, "O20": {"content": "", "style": 7}, "O21": {"content": "", "style": 7}, "O22": {"content": "", "style": 7}, "O23": {"content": "", "style": 7}, "O24": {"content": "", "style": 7}, "O25": {"content": "", "style": 7}, "P11": {"content": "", "style": 7}, "P12": {"content": "", "style": 7}, "P13": {"content": "", "style": 7}, "P14": {"content": "", "style": 7}, "P15": {"content": "", "style": 7}, "P16": {"content": "", "style": 7}, "P17": {"content": "", "style": 7}, "P18": {"content": "", "style": 7}, "P19": {"content": "", "style": 7}, "P20": {"content": "", "style": 7}, "P21": {"content": "", "style": 7}, "P22": {"content": "", "style": 7}, "P23": {"content": "", "style": 7}, "P24": {"content": "", "style": 7}, "P25": {"content": "", "style": 7}, "Q11": {"content": "", "style": 7}, "Q12": {"content": "", "style": 7}, "Q13": {"content": "", "style": 7}, "Q14": {"content": "", "style": 7}, "Q15": {"content": "", "style": 7}, "Q16": {"content": "", "style": 7}, "Q17": {"content": "", "style": 7}, "Q18": {"content": "", "style": 7}, "Q19": {"content": "", "style": 7}, "Q20": {"content": "", "style": 7}, "Q21": {"content": "", "style": 7}, "Q22": {"content": "", "style": 7}, "Q23": {"content": "", "style": 7}, "Q24": {"content": "", "style": 7}, "Q25": {"content": "", "style": 7}, "R11": {"content": "", "style": 7}, "R12": {"content": "", "style": 7}, "R13": {"content": "", "style": 7}, "R14": {"content": "", "style": 7}, "R15": {"content": "", "style": 7}, "R16": {"content": "", "style": 7}, "R17": {"content": "", "style": 7}, "R18": {"content": "", "style": 7}, "R19": {"content": "", "style": 7}, "R20": {"content": "", "style": 7}, "R21": {"content": "", "style": 7}, "R22": {"content": "", "style": 7}, "R23": {"content": "", "style": 7}, "R24": {"content": "", "style": 7}, "R25": {"content": "", "style": 7}, "S11": {"content": "", "style": 7}, "S12": {"content": "", "style": 7}, "S13": {"content": "", "style": 7}, "S14": {"content": "", "style": 7}, "S15": {"content": "", "style": 7}, "S16": {"content": "", "style": 7}, "S17": {"content": "", "style": 7}, "S18": {"content": "", "style": 7}, "S19": {"content": "", "style": 7}, "S20": {"content": "", "style": 7}, "S21": {"content": "", "style": 7}, "S22": {"content": "", "style": 7}, "S23": {"content": "", "style": 7}, "S24": {"content": "", "style": 7}, "S25": {"content": "", "style": 7}, "T11": {"content": "", "style": 7}, "T12": {"content": "", "style": 7}, "T13": {"content": "", "style": 7}, "T14": {"content": "", "style": 7}, "T15": {"content": "", "style": 7}, "T16": {"content": "", "style": 7}, "T17": {"content": "", "style": 7}, "T18": {"content": "", "style": 7}, "T19": {"content": "", "style": 7}, "T20": {"content": "", "style": 7}, "T21": {"content": "", "style": 7}, "T22": {"content": "", "style": 7}, "T23": {"content": "", "style": 7}, "T24": {"content": "", "style": 7}, "T25": {"content": "", "style": 7}, "U11": {"content": "", "style": 7}, "U12": {"content": "", "style": 7}, "U13": {"content": "", "style": 7}, "U14": {"content": "", "style": 7}, "U15": {"content": "", "style": 7}, "U16": {"content": "", "style": 7}, "U17": {"content": "", "style": 7}, "U18": {"content": "", "style": 7}, "U19": {"content": "", "style": 7}, "U20": {"content": "", "style": 7}, "U21": {"content": "", "style": 7}, "U22": {"content": "", "style": 7}, "U23": {"content": "", "style": 7}, "U24": {"content": "", "style": 7}, "U25": {"content": "", "style": 7}, "V11": {"content": "", "style": 7}, "V12": {"content": "", "style": 7}, "V13": {"content": "", "style": 7}, "V14": {"content": "", "style": 7}, "V15": {"content": "", "style": 7}, "V16": {"content": "", "style": 7}, "V17": {"content": "", "style": 7}, "V18": {"content": "", "style": 7}, "V19": {"content": "", "style": 7}, "V20": {"content": "", "style": 7}, "V21": {"content": "", "style": 7}, "V22": {"content": "", "style": 7}, "V23": {"content": "", "style": 7}, "V24": {"content": "", "style": 7}, "V25": {"content": "", "style": 7}, "J257": {"content": "", "style": 7}, "J256": {"content": "", "style": 7}, "J255": {"content": "", "style": 7}, "J254": {"content": "", "style": 7}, "J253": {"content": "", "style": 7}, "J252": {"content": "", "style": 7}, "J251": {"content": "", "style": 7}, "J250": {"content": "", "style": 7}, "J249": {"content": "", "style": 7}, "J248": {"content": "", "style": 7}, "J247": {"content": "", "style": 7}, "J246": {"content": "", "style": 7}, "J245": {"content": "", "style": 7}, "J244": {"content": "", "style": 7}, "J243": {"content": "", "style": 7}, "J242": {"content": "", "style": 7}, "J2": {"content": "NB: Make sure the 'Recurring Revenues' feature is enabled in the CRM settings before using this template", "style": 45}}, "conditionalFormats": [{"id": "90c15d8f-ad16-44df-8e0e-1c2ded5f08d0", "rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 10997900}}, "ranges": ["F5:F257", "G6:G25", "G27:G46", "G48:G67", "G69:G88", "G90:G109", "G111:G130", "G132:G151", "G153:G172", "G174:G193", "G195:G214", "G216:G235", "G237:G256"]}, {"id": "34f710a3-1185-48c5-b68f-5b1c06d100da", "rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 10997900}}, "ranges": ["G5:G257"]}], "figures": []}, {"id": "cdb44c0b-fcf2-44d0-bd30-307da7e4306c", "name": "Revenue by Salesperson", "colNumber": 22, "rowNumber": 103, "rows": {}, "cols": {"0": {"size": 118.5205078125}, "1": {"size": 94.18310546875}, "2": {"size": 94.18310546875}, "3": {"size": 94.18310546875}, "4": {"size": 94.18310546875}, "5": {"size": 94.18310546875}, "6": {"size": 94.18310546875}, "7": {"size": 94.18310546875}, "8": {"size": 94.18310546875}}, "merges": ["A1:I2", "B4:C4", "H4:I4", "D4:E4", "F4:G4", "A3:I3"], "cells": {"A1": {"content": "=\"Monthly Revenue by Salesperson - \"&FILTER.VALUE(\"Year\")", "style": 17}, "A2": {"content": "", "style": 17}, "B1": {"content": "", "style": 17}, "B2": {"content": "", "style": 17}, "C1": {"content": "", "style": 17}, "C2": {"content": "", "style": 17}, "H1": {"content": "", "style": 17}, "H2": {"content": "", "style": 17}, "I1": {"content": "", "style": 17}, "I2": {"content": "", "style": 17}, "D1": {"content": "", "style": 18}, "D2": {"content": "", "style": 18}, "E1": {"content": "", "style": 18}, "E2": {"content": "", "style": 18}, "F1": {"content": "", "style": 18, "format": "0.00%"}, "F2": {"content": "", "style": 18, "format": "0.00%"}, "G1": {"content": "", "style": 18, "format": "0.00%"}, "G2": {"content": "", "style": 18, "format": "0.00%"}, "A6": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A7": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A8": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A9": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A10": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A11": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A12": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A13": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A14": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A15": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A16": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A17": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "A18": {"content": "Total", "style": 3}, "B5": {"content": "MRR", "style": 35}, "C5": {"content": "NRR", "style": 36}, "B6": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B7": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B8": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B9": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B10": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B11": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B12": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B13": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B14": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B15": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B16": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B17": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "B18": {"content": "=sum(B6:B17)", "style": 46, "format": "#,##0.00"}, "C6": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C7": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C8": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C9": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C10": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C11": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C12": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C13": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C14": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C15": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C16": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C17": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"),\"won_status\",\"won\")", "format": "#,##0.00"}, "C18": {"content": "=sum(C6:C17)", "style": 46, "format": "#,##0.00"}, "A4": {"content": "=FILTER.VALUE(\"Salesperson\")", "style": 38}, "B4": {"content": "Actuals", "style": 9}, "C4": {"content": "", "style": 9}, "H5": {"content": "MRR", "style": 35}, "I5": {"content": "NRR", "style": 36}, "H6": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H7": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H8": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H9": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H10": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H11": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H12": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H13": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H14": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H15": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H16": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H17": {"content": "=PIVOT(\"2\",\"recurring_revenue_monthly_prorated\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "H18": {"content": "=sum(H6:H17)", "style": 46, "format": "#,##0.00"}, "I6": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I7": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I8": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I9": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I10": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I11": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I12": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I13": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I14": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I15": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I16": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I17": {"content": "=PIVOT(\"2\",\"prorated_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "I18": {"content": "=sum(I6:I17)", "style": 46, "format": "#,##0.00"}, "H4": {"content": "Forecasted", "style": 9}, "I4": {"content": "", "style": 9}, "D4": {"content": "Target", "style": 9}, "F4": {"content": "Performance", "style": 9, "format": "0.00%"}, "D5": {"content": "MRR", "style": 35}, "E5": {"content": "NRR", "style": 36}, "F5": {"content": "MRR", "style": 35, "format": "0.00%"}, "G5": {"content": "NRR", "style": 36, "format": "0.00%"}, "D6": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B58", "format": "#,##0.00"}, "E6": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B58", "format": "#,##0.00"}, "F6": {"content": "=iferror(B6/D6,0)", "format": "0.00%"}, "G6": {"content": "=iferror(C6/E6,0)", "format": "0.00%"}, "D7": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B59", "format": "#,##0.00"}, "E7": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B59", "format": "#,##0.00"}, "F7": {"content": "=iferror(B7/D7,0)", "format": "0.00%"}, "G7": {"content": "=iferror(C7/E7,0)", "format": "0.00%"}, "D8": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B60", "format": "#,##0.00"}, "E8": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B60", "format": "#,##0.00"}, "F8": {"content": "=iferror(B8/D8,0)", "format": "0.00%"}, "G8": {"content": "=iferror(C8/E8,0)", "format": "0.00%"}, "D9": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B61", "format": "#,##0.00"}, "E9": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B61", "format": "#,##0.00"}, "F9": {"content": "=iferror(B9/D9,0)", "format": "0.00%"}, "G9": {"content": "=iferror(C9/E9,0)", "format": "0.00%"}, "D10": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B62", "format": "#,##0.00"}, "E10": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B62", "format": "#,##0.00"}, "F10": {"content": "=iferror(B10/D10,0)", "format": "0.00%"}, "G10": {"content": "=iferror(C10/E10,0)", "format": "0.00%"}, "D11": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B63", "format": "#,##0.00"}, "E11": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B63", "format": "#,##0.00"}, "F11": {"content": "=iferror(B11/D11,0)", "format": "0.00%"}, "G11": {"content": "=iferror(C11/E11,0)", "format": "0.00%"}, "D12": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B64", "format": "#,##0.00"}, "E12": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B64", "format": "#,##0.00"}, "F12": {"content": "=iferror(B12/D12,0)", "format": "0.00%"}, "G12": {"content": "=iferror(C12/E12,0)", "format": "0.00%"}, "D13": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B65", "format": "#,##0.00"}, "E13": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B65", "format": "#,##0.00"}, "F13": {"content": "=iferror(B13/D13,0)", "format": "0.00%"}, "G13": {"content": "=iferror(C13/E13,0)", "format": "0.00%"}, "D14": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B66", "format": "#,##0.00"}, "E14": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B66", "format": "#,##0.00"}, "F14": {"content": "=iferror(B14/D14,0)", "format": "0.00%"}, "G14": {"content": "=iferror(C14/E14,0)", "format": "0.00%"}, "D15": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B67", "format": "#,##0.00"}, "E15": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B67", "format": "#,##0.00"}, "F15": {"content": "=iferror(B15/D15,0)", "format": "0.00%"}, "G15": {"content": "=iferror(C15/E15,0)", "format": "0.00%"}, "D16": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B68", "format": "#,##0.00"}, "E16": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B68", "format": "#,##0.00"}, "F16": {"content": "=iferror(B16/D16,0)", "format": "0.00%"}, "G16": {"content": "=iferror(C16/E16,0)", "format": "0.00%"}, "D17": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,2,false)*Targets!B69", "format": "#,##0.00"}, "E17": {"content": "=vlookup($A$4,Targets!$A$5:$C$55,3,false)*Targets!B69", "format": "#,##0.00"}, "F17": {"content": "=iferror(B17/D17,0)", "format": "0.00%"}, "G17": {"content": "=iferror(C17/E17,0)", "format": "0.00%"}, "D18": {"content": "=sum(D6:D17)", "style": 46, "format": "#,##0.00"}, "E18": {"content": "=sum(E6:E17)", "style": 46, "format": "#,##0.00"}, "F18": {"content": "=iferror(B18/D18,0)", "style": 46, "format": "0.00%"}, "G18": {"content": "=iferror(C18/E18,0)", "style": 46, "format": "0.00%"}, "E4": {"content": "", "style": 10}, "G4": {"content": "", "style": 10, "format": "0.00%"}, "J4": {"content": "", "style": 10}, "K4": {"content": "", "style": 10}, "L4": {"content": "", "style": 10}, "M4": {"content": "", "style": 10}, "N4": {"content": "", "style": 10}, "O4": {"content": "", "style": 10}, "P4": {"content": "", "style": 10}, "Q4": {"content": "", "style": 10}, "R4": {"content": "", "style": 10}, "S4": {"content": "", "style": 10}, "T4": {"content": "", "style": 10}, "U4": {"content": "", "style": 10}, "V4": {"content": "", "style": 10}, "J5": {"content": "", "style": 13}, "K5": {"content": "", "style": 13}, "L5": {"content": "", "style": 13}, "M5": {"content": "", "style": 13}, "N5": {"content": "", "style": 13}, "O5": {"content": "", "style": 13}, "P5": {"content": "", "style": 13}, "Q5": {"content": "", "style": 13}, "R5": {"content": "", "style": 13}, "S5": {"content": "", "style": 13}, "T5": {"content": "", "style": 13}, "U5": {"content": "", "style": 13}, "V5": {"content": "", "style": 13}, "J18": {"content": "", "style": 7}, "K18": {"content": "", "style": 7}, "L18": {"content": "", "style": 7}, "M18": {"content": "", "style": 7}, "N18": {"content": "", "style": 7}, "O18": {"content": "", "style": 7}, "P18": {"content": "", "style": 7}, "Q18": {"content": "", "style": 7}, "R18": {"content": "", "style": 7}, "S18": {"content": "", "style": 7}, "T18": {"content": "", "style": 7}, "U18": {"content": "", "style": 7}, "V18": {"content": "", "style": 7}, "A5": {"content": "", "style": 38}, "A3": {"content": "Use the \"Salesperson\" filter from the top right icon to get individual actuals/targets", "style": 43}, "J3": {"content": "", "style": 10}, "K3": {"content": "", "style": 10}, "L3": {"content": "", "style": 10}, "M3": {"content": "", "style": 10}, "N3": {"content": "", "style": 10}, "O3": {"content": "", "style": 10}, "P3": {"content": "", "style": 10}, "Q3": {"content": "", "style": 10}, "R3": {"content": "", "style": 10}, "S3": {"content": "", "style": 10}, "T3": {"content": "", "style": 10}, "U3": {"content": "", "style": 10}, "V3": {"content": "", "style": 10}, "B3": {"content": "", "style": 44}, "C3": {"content": "", "style": 44}, "D3": {"content": "", "style": 44}, "E3": {"content": "", "style": 44}, "F3": {"content": "", "style": 44, "format": "0.00%"}, "G3": {"content": "", "style": 44, "format": "0.00%"}, "H3": {"content": "", "style": 44}, "I3": {"content": "", "style": 44}, "F19": {"content": "", "format": "0.00%"}, "G19": {"content": "", "format": "0.00%"}, "F20": {"content": "", "format": "0.00%"}, "G20": {"content": "", "format": "0.00%"}, "F21": {"content": "", "format": "0.00%"}, "G21": {"content": "", "format": "0.00%"}, "F22": {"content": "", "format": "0.00%"}, "G22": {"content": "", "format": "0.00%"}, "F23": {"content": "", "format": "0.00%"}, "G23": {"content": "", "format": "0.00%"}, "F24": {"content": "", "format": "0.00%"}, "G24": {"content": "", "format": "0.00%"}, "F25": {"content": "", "format": "0.00%"}, "G25": {"content": "", "format": "0.00%"}, "F26": {"content": "", "format": "0.00%"}, "G26": {"content": "", "format": "0.00%"}, "F27": {"content": "", "format": "0.00%"}, "G27": {"content": "", "format": "0.00%"}, "F28": {"content": "", "format": "0.00%"}, "G28": {"content": "", "format": "0.00%"}, "F29": {"content": "", "format": "0.00%"}, "G29": {"content": "", "format": "0.00%"}, "F30": {"content": "", "format": "0.00%"}, "G30": {"content": "", "format": "0.00%"}, "F31": {"content": "", "format": "0.00%"}, "G31": {"content": "", "format": "0.00%"}, "F32": {"content": "", "format": "0.00%"}, "G32": {"content": "", "format": "0.00%"}, "F33": {"content": "", "format": "0.00%"}, "G33": {"content": "", "format": "0.00%"}, "F34": {"content": "", "format": "0.00%"}, "G34": {"content": "", "format": "0.00%"}, "F35": {"content": "", "format": "0.00%"}, "G35": {"content": "", "format": "0.00%"}, "F36": {"content": "", "format": "0.00%"}, "G36": {"content": "", "format": "0.00%"}, "F37": {"content": "", "format": "0.00%"}, "G37": {"content": "", "format": "0.00%"}, "F38": {"content": "", "format": "0.00%"}, "G38": {"content": "", "format": "0.00%"}, "F39": {"content": "", "format": "0.00%"}, "G39": {"content": "", "format": "0.00%"}, "F40": {"content": "", "format": "0.00%"}, "G40": {"content": "", "format": "0.00%"}, "F41": {"content": "", "format": "0.00%"}, "G41": {"content": "", "format": "0.00%"}, "F42": {"content": "", "format": "0.00%"}, "G42": {"content": "", "format": "0.00%"}, "F43": {"content": "", "format": "0.00%"}, "G43": {"content": "", "format": "0.00%"}, "F44": {"content": "", "format": "0.00%"}, "G44": {"content": "", "format": "0.00%"}, "F45": {"content": "", "format": "0.00%"}, "G45": {"content": "", "format": "0.00%"}, "F46": {"content": "", "format": "0.00%"}, "G46": {"content": "", "format": "0.00%"}, "F47": {"content": "", "format": "0.00%"}, "G47": {"content": "", "format": "0.00%"}, "F48": {"content": "", "format": "0.00%"}, "G48": {"content": "", "format": "0.00%"}, "F49": {"content": "", "format": "0.00%"}, "G49": {"content": "", "format": "0.00%"}, "F50": {"content": "", "format": "0.00%"}, "G50": {"content": "", "format": "0.00%"}, "F51": {"content": "", "format": "0.00%"}, "G51": {"content": "", "format": "0.00%"}, "F52": {"content": "", "format": "0.00%"}, "G52": {"content": "", "format": "0.00%"}, "F53": {"content": "", "format": "0.00%"}, "G53": {"content": "", "format": "0.00%"}, "F54": {"content": "", "format": "0.00%"}, "G54": {"content": "", "format": "0.00%"}, "F55": {"content": "", "format": "0.00%"}, "G55": {"content": "", "format": "0.00%"}, "F56": {"content": "", "format": "0.00%"}, "G56": {"content": "", "format": "0.00%"}, "F57": {"content": "", "format": "0.00%"}, "G57": {"content": "", "format": "0.00%"}, "F58": {"content": "", "format": "0.00%"}, "G58": {"content": "", "format": "0.00%"}, "F59": {"content": "", "format": "0.00%"}, "G59": {"content": "", "format": "0.00%"}, "F60": {"content": "", "format": "0.00%"}, "G60": {"content": "", "format": "0.00%"}, "F61": {"content": "", "format": "0.00%"}, "G61": {"content": "", "format": "0.00%"}, "F62": {"content": "", "format": "0.00%"}, "G62": {"content": "", "format": "0.00%"}, "F63": {"content": "", "format": "0.00%"}, "G63": {"content": "", "format": "0.00%"}, "F64": {"content": "", "format": "0.00%"}, "G64": {"content": "", "format": "0.00%"}, "F65": {"content": "", "format": "0.00%"}, "G65": {"content": "", "format": "0.00%"}, "F66": {"content": "", "format": "0.00%"}, "G66": {"content": "", "format": "0.00%"}, "F67": {"content": "", "format": "0.00%"}, "G67": {"content": "", "format": "0.00%"}, "F68": {"content": "", "format": "0.00%"}, "G68": {"content": "", "format": "0.00%"}, "F69": {"content": "", "format": "0.00%"}, "G69": {"content": "", "format": "0.00%"}, "F70": {"content": "", "format": "0.00%"}, "G70": {"content": "", "format": "0.00%"}, "F71": {"content": "", "format": "0.00%"}, "G71": {"content": "", "format": "0.00%"}, "F72": {"content": "", "format": "0.00%"}, "G72": {"content": "", "format": "0.00%"}, "F73": {"content": "", "format": "0.00%"}, "G73": {"content": "", "format": "0.00%"}, "F74": {"content": "", "format": "0.00%"}, "G74": {"content": "", "format": "0.00%"}, "F75": {"content": "", "format": "0.00%"}, "G75": {"content": "", "format": "0.00%"}, "F76": {"content": "", "format": "0.00%"}, "G76": {"content": "", "format": "0.00%"}, "F77": {"content": "", "format": "0.00%"}, "G77": {"content": "", "format": "0.00%"}, "F78": {"content": "", "format": "0.00%"}, "G78": {"content": "", "format": "0.00%"}, "F79": {"content": "", "format": "0.00%"}, "G79": {"content": "", "format": "0.00%"}, "F80": {"content": "", "format": "0.00%"}, "G80": {"content": "", "format": "0.00%"}, "F81": {"content": "", "format": "0.00%"}, "G81": {"content": "", "format": "0.00%"}, "F82": {"content": "", "format": "0.00%"}, "G82": {"content": "", "format": "0.00%"}, "F83": {"content": "", "format": "0.00%"}, "G83": {"content": "", "format": "0.00%"}, "F84": {"content": "", "format": "0.00%"}, "G84": {"content": "", "format": "0.00%"}, "F85": {"content": "", "format": "0.00%"}, "G85": {"content": "", "format": "0.00%"}, "F86": {"content": "", "format": "0.00%"}, "G86": {"content": "", "format": "0.00%"}, "F87": {"content": "", "format": "0.00%"}, "G87": {"content": "", "format": "0.00%"}, "F88": {"content": "", "format": "0.00%"}, "G88": {"content": "", "format": "0.00%"}, "F89": {"content": "", "format": "0.00%"}, "G89": {"content": "", "format": "0.00%"}, "F90": {"content": "", "format": "0.00%"}, "G90": {"content": "", "format": "0.00%"}, "F91": {"content": "", "format": "0.00%"}, "G91": {"content": "", "format": "0.00%"}, "F92": {"content": "", "format": "0.00%"}, "G92": {"content": "", "format": "0.00%"}, "F93": {"content": "", "format": "0.00%"}, "G93": {"content": "", "format": "0.00%"}, "F94": {"content": "", "format": "0.00%"}, "G94": {"content": "", "format": "0.00%"}, "F95": {"content": "", "format": "0.00%"}, "G95": {"content": "", "format": "0.00%"}, "F96": {"content": "", "format": "0.00%"}, "G96": {"content": "", "format": "0.00%"}, "F97": {"content": "", "format": "0.00%"}, "G97": {"content": "", "format": "0.00%"}, "F98": {"content": "", "format": "0.00%"}, "G98": {"content": "", "format": "0.00%"}, "F99": {"content": "", "format": "0.00%"}, "G99": {"content": "", "format": "0.00%"}, "F100": {"content": "", "format": "0.00%"}, "G100": {"content": "", "format": "0.00%"}, "F101": {"content": "", "format": "0.00%"}, "G101": {"content": "", "format": "0.00%"}, "F102": {"content": "", "format": "0.00%"}, "G102": {"content": "", "format": "0.00%"}, "F103": {"content": "", "format": "0.00%"}, "G103": {"content": "", "format": "0.00%"}}, "conditionalFormats": [{"id": "feda389b-2e3e-49ce-a586-4c295b3a2b76", "rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 10997900}}, "ranges": ["F6:F18"]}, {"id": "07454173-dbb1-4fac-8d00-359e0bc8244d", "rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 10997900}}, "ranges": ["G6:G18"]}], "figures": []}, {"id": "9fd67908-cf49-4c7a-aa30-1d9bff8f243f", "name": "Targets", "colNumber": 26, "rowNumber": 143, "rows": {}, "cols": {"0": {"size": 115.45166015625}, "1": {"size": 108.3125}, "2": {"size": 108.3125}}, "merges": ["A1:C2", "A3:C3", "A56:B57"], "cells": {"A1": {"content": "=\"Monthly Target - \"&FILTER.VALUE(\"Year\")", "style": 17}, "A2": {"content": "", "style": 17}, "B1": {"content": "", "style": 17, "format": "#,##0.00"}, "B2": {"content": "", "style": 17, "format": "#,##0.00"}, "C1": {"content": "", "style": 17, "format": "#,##0.00"}, "C2": {"content": "", "style": 17, "format": "#,##0.00"}, "A5": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",1))", "style": 28}, "A6": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",2))", "style": 28}, "A7": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",3))", "style": 28}, "A8": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",4))", "style": 28}, "A9": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",5))", "style": 28}, "A10": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",6))", "style": 28}, "A11": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",7))", "style": 28}, "B5": {"content": "10000", "format": "#,##0.00"}, "B6": {"content": "10000", "format": "#,##0.00"}, "B7": {"content": "10000", "format": "#,##0.00"}, "B8": {"content": "10000", "format": "#,##0.00"}, "B9": {"content": "10000", "format": "#,##0.00"}, "B10": {"content": "10000", "format": "#,##0.00"}, "B11": {"content": "10000", "format": "#,##0.00"}, "A4": {"content": "", "style": 3}, "B4": {"content": "MRR", "style": 35, "format": "#,##0.00"}, "C4": {"content": "NRR", "style": 36, "format": "#,##0.00"}, "A3": {"content": "Define the monthly target of each salesperson", "style": 43}, "B3": {"content": "", "style": 43, "format": "#,##0.00"}, "C3": {"content": "", "style": 43, "format": "#,##0.00"}, "C5": {"content": "5000", "format": "#,##0.00"}, "C6": {"content": "5000", "format": "#,##0.00"}, "C7": {"content": "5000", "format": "#,##0.00"}, "C8": {"content": "5000", "format": "#,##0.00"}, "C9": {"content": "5000", "format": "#,##0.00"}, "C10": {"content": "5000", "format": "#,##0.00"}, "C11": {"content": "5000", "format": "#,##0.00"}, "A56": {"content": "=\"Monthly Factors - \"&FILTER.VALUE(\"Year\")", "style": 17}, "C56": {"content": "", "style": 29, "format": "#,##0.00"}, "C57": {"content": "", "style": 29, "format": "#,##0.00"}, "A57": {"content": "", "style": 17}, "B56": {"content": "", "style": 18, "format": "#,##0.00"}, "B57": {"content": "", "style": 18, "format": "#,##0.00"}, "A58": {"content": "January", "style": 7}, "B58": {"content": "1", "style": 33, "format": "#,##0.00"}, "A59": {"content": "February", "style": 7}, "B59": {"content": "1", "style": 33, "format": "#,##0.00"}, "A60": {"content": "March", "style": 7}, "B60": {"content": "1", "style": 33, "format": "#,##0.00"}, "A61": {"content": "April", "style": 7}, "B61": {"content": "1", "style": 33, "format": "#,##0.00"}, "A62": {"content": "May", "style": 7}, "B62": {"content": "1", "style": 33, "format": "#,##0.00"}, "A63": {"content": "June", "style": 7}, "B63": {"content": "1.1", "style": 33, "format": "#,##0.00"}, "A64": {"content": "July", "style": 7}, "B64": {"content": "0.9", "style": 33, "format": "#,##0.00"}, "A65": {"content": "August", "style": 7}, "B65": {"content": "0.9", "style": 33, "format": "#,##0.00"}, "A66": {"content": "September", "style": 7}, "B66": {"content": "1", "style": 33, "format": "#,##0.00"}, "A67": {"content": "October", "style": 7}, "B67": {"content": "1", "style": 33, "format": "#,##0.00"}, "A68": {"content": "November", "style": 7}, "B68": {"content": "1.1", "style": 33, "format": "#,##0.00"}, "A69": {"content": "December", "style": 7}, "B69": {"content": "1", "style": 33, "format": "#,##0.00"}, "B55": {"content": "", "format": "#,##0.00"}, "C55": {"content": "", "format": "#,##0.00"}, "C58": {"content": "", "format": "#,##0.00"}, "C59": {"content": "", "format": "#,##0.00"}, "C60": {"content": "", "format": "#,##0.00"}, "C61": {"content": "", "format": "#,##0.00"}, "C62": {"content": "", "format": "#,##0.00"}, "C63": {"content": "", "format": "#,##0.00"}, "C64": {"content": "", "format": "#,##0.00"}, "C65": {"content": "", "format": "#,##0.00"}, "C66": {"content": "", "format": "#,##0.00"}, "C67": {"content": "", "format": "#,##0.00"}, "C68": {"content": "", "format": "#,##0.00"}, "C69": {"content": "", "format": "#,##0.00"}, "B70": {"content": "", "format": "#,##0.00"}, "C70": {"content": "", "format": "#,##0.00"}, "B71": {"content": "", "format": "#,##0.00"}, "C71": {"content": "", "format": "#,##0.00"}, "B72": {"content": "", "format": "#,##0.00"}, "C72": {"content": "", "format": "#,##0.00"}, "B73": {"content": "", "format": "#,##0.00"}, "C73": {"content": "", "format": "#,##0.00"}, "B74": {"content": "", "format": "#,##0.00"}, "C74": {"content": "", "format": "#,##0.00"}, "B75": {"content": "", "format": "#,##0.00"}, "C75": {"content": "", "format": "#,##0.00"}, "B76": {"content": "", "format": "#,##0.00"}, "C76": {"content": "", "format": "#,##0.00"}, "B77": {"content": "", "format": "#,##0.00"}, "C77": {"content": "", "format": "#,##0.00"}, "B78": {"content": "", "format": "#,##0.00"}, "C78": {"content": "", "format": "#,##0.00"}, "B79": {"content": "", "format": "#,##0.00"}, "C79": {"content": "", "format": "#,##0.00"}, "B80": {"content": "", "format": "#,##0.00"}, "C80": {"content": "", "format": "#,##0.00"}, "B81": {"content": "", "format": "#,##0.00"}, "C81": {"content": "", "format": "#,##0.00"}, "B82": {"content": "", "format": "#,##0.00"}, "C82": {"content": "", "format": "#,##0.00"}, "B83": {"content": "", "format": "#,##0.00"}, "C83": {"content": "", "format": "#,##0.00"}, "B84": {"content": "", "format": "#,##0.00"}, "C84": {"content": "", "format": "#,##0.00"}, "B85": {"content": "", "format": "#,##0.00"}, "C85": {"content": "", "format": "#,##0.00"}, "B86": {"content": "", "format": "#,##0.00"}, "C86": {"content": "", "format": "#,##0.00"}, "B87": {"content": "", "format": "#,##0.00"}, "C87": {"content": "", "format": "#,##0.00"}, "B88": {"content": "", "format": "#,##0.00"}, "C88": {"content": "", "format": "#,##0.00"}, "B89": {"content": "", "format": "#,##0.00"}, "C89": {"content": "", "format": "#,##0.00"}, "B90": {"content": "", "format": "#,##0.00"}, "C90": {"content": "", "format": "#,##0.00"}, "B91": {"content": "", "format": "#,##0.00"}, "C91": {"content": "", "format": "#,##0.00"}, "B92": {"content": "", "format": "#,##0.00"}, "C92": {"content": "", "format": "#,##0.00"}, "B93": {"content": "", "format": "#,##0.00"}, "C93": {"content": "", "format": "#,##0.00"}, "B94": {"content": "", "format": "#,##0.00"}, "C94": {"content": "", "format": "#,##0.00"}, "B95": {"content": "", "format": "#,##0.00"}, "C95": {"content": "", "format": "#,##0.00"}, "B96": {"content": "", "format": "#,##0.00"}, "C96": {"content": "", "format": "#,##0.00"}, "B97": {"content": "", "format": "#,##0.00"}, "C97": {"content": "", "format": "#,##0.00"}, "B98": {"content": "", "format": "#,##0.00"}, "C98": {"content": "", "format": "#,##0.00"}, "B99": {"content": "", "format": "#,##0.00"}, "C99": {"content": "", "format": "#,##0.00"}, "B100": {"content": "", "format": "#,##0.00"}, "C100": {"content": "", "format": "#,##0.00"}, "B101": {"content": "", "format": "#,##0.00"}, "C101": {"content": "", "format": "#,##0.00"}, "B102": {"content": "", "format": "#,##0.00"}, "C102": {"content": "", "format": "#,##0.00"}, "B103": {"content": "", "format": "#,##0.00"}, "C103": {"content": "", "format": "#,##0.00"}, "B104": {"content": "", "format": "#,##0.00"}, "C104": {"content": "", "format": "#,##0.00"}, "B105": {"content": "", "format": "#,##0.00"}, "C105": {"content": "", "format": "#,##0.00"}, "B106": {"content": "", "format": "#,##0.00"}, "C106": {"content": "", "format": "#,##0.00"}, "B107": {"content": "", "format": "#,##0.00"}, "C107": {"content": "", "format": "#,##0.00"}, "B108": {"content": "", "format": "#,##0.00"}, "C108": {"content": "", "format": "#,##0.00"}, "B109": {"content": "", "format": "#,##0.00"}, "C109": {"content": "", "format": "#,##0.00"}, "B110": {"content": "", "format": "#,##0.00"}, "C110": {"content": "", "format": "#,##0.00"}, "B111": {"content": "", "format": "#,##0.00"}, "C111": {"content": "", "format": "#,##0.00"}, "B112": {"content": "", "format": "#,##0.00"}, "C112": {"content": "", "format": "#,##0.00"}, "B113": {"content": "", "format": "#,##0.00"}, "C113": {"content": "", "format": "#,##0.00"}, "B114": {"content": "", "format": "#,##0.00"}, "C114": {"content": "", "format": "#,##0.00"}, "B115": {"content": "", "format": "#,##0.00"}, "C115": {"content": "", "format": "#,##0.00"}, "B116": {"content": "", "format": "#,##0.00"}, "C116": {"content": "", "format": "#,##0.00"}, "B117": {"content": "", "format": "#,##0.00"}, "C117": {"content": "", "format": "#,##0.00"}, "B118": {"content": "", "format": "#,##0.00"}, "C118": {"content": "", "format": "#,##0.00"}, "B119": {"content": "", "format": "#,##0.00"}, "C119": {"content": "", "format": "#,##0.00"}, "B120": {"content": "", "format": "#,##0.00"}, "C120": {"content": "", "format": "#,##0.00"}, "B121": {"content": "", "format": "#,##0.00"}, "C121": {"content": "", "format": "#,##0.00"}, "B122": {"content": "", "format": "#,##0.00"}, "C122": {"content": "", "format": "#,##0.00"}, "B123": {"content": "", "format": "#,##0.00"}, "C123": {"content": "", "format": "#,##0.00"}, "B124": {"content": "", "format": "#,##0.00"}, "C124": {"content": "", "format": "#,##0.00"}, "B125": {"content": "", "format": "#,##0.00"}, "C125": {"content": "", "format": "#,##0.00"}, "B126": {"content": "", "format": "#,##0.00"}, "C126": {"content": "", "format": "#,##0.00"}, "B127": {"content": "", "format": "#,##0.00"}, "C127": {"content": "", "format": "#,##0.00"}, "B128": {"content": "", "format": "#,##0.00"}, "C128": {"content": "", "format": "#,##0.00"}, "B129": {"content": "", "format": "#,##0.00"}, "C129": {"content": "", "format": "#,##0.00"}, "B130": {"content": "", "format": "#,##0.00"}, "C130": {"content": "", "format": "#,##0.00"}, "B131": {"content": "", "format": "#,##0.00"}, "C131": {"content": "", "format": "#,##0.00"}, "B132": {"content": "", "format": "#,##0.00"}, "C132": {"content": "", "format": "#,##0.00"}, "B133": {"content": "", "format": "#,##0.00"}, "C133": {"content": "", "format": "#,##0.00"}, "B134": {"content": "", "format": "#,##0.00"}, "C134": {"content": "", "format": "#,##0.00"}, "B135": {"content": "", "format": "#,##0.00"}, "C135": {"content": "", "format": "#,##0.00"}, "B136": {"content": "", "format": "#,##0.00"}, "C136": {"content": "", "format": "#,##0.00"}, "B137": {"content": "", "format": "#,##0.00"}, "C137": {"content": "", "format": "#,##0.00"}, "B138": {"content": "", "format": "#,##0.00"}, "C138": {"content": "", "format": "#,##0.00"}, "B139": {"content": "", "format": "#,##0.00"}, "C139": {"content": "", "format": "#,##0.00"}, "B140": {"content": "", "format": "#,##0.00"}, "C140": {"content": "", "format": "#,##0.00"}, "B141": {"content": "", "format": "#,##0.00"}, "C141": {"content": "", "format": "#,##0.00"}, "B142": {"content": "", "format": "#,##0.00"}, "C142": {"content": "", "format": "#,##0.00"}, "B143": {"content": "", "format": "#,##0.00"}, "C143": {"content": "", "format": "#,##0.00"}, "B12": {"content": "10000", "format": "#,##0.00"}, "B13": {"content": "10000", "format": "#,##0.00"}, "B14": {"content": "10000", "format": "#,##0.00"}, "B15": {"content": "10000", "format": "#,##0.00"}, "B16": {"content": "10000", "format": "#,##0.00"}, "B17": {"content": "10000", "format": "#,##0.00"}, "B18": {"content": "10000", "format": "#,##0.00"}, "B19": {"content": "10000", "format": "#,##0.00"}, "B20": {"content": "10000", "format": "#,##0.00"}, "B21": {"content": "10000", "format": "#,##0.00"}, "B22": {"content": "10000", "format": "#,##0.00"}, "B23": {"content": "10000", "format": "#,##0.00"}, "B24": {"content": "10000", "format": "#,##0.00"}, "B25": {"content": "10000", "format": "#,##0.00"}, "B26": {"content": "10000", "format": "#,##0.00"}, "B27": {"content": "10000", "format": "#,##0.00"}, "B28": {"content": "10000", "format": "#,##0.00"}, "B29": {"content": "10000", "format": "#,##0.00"}, "B30": {"content": "10000", "format": "#,##0.00"}, "B31": {"content": "10000", "format": "#,##0.00"}, "B32": {"content": "10000", "format": "#,##0.00"}, "B33": {"content": "10000", "format": "#,##0.00"}, "B34": {"content": "10000", "format": "#,##0.00"}, "B35": {"content": "10000", "format": "#,##0.00"}, "B36": {"content": "10000", "format": "#,##0.00"}, "B37": {"content": "10000", "format": "#,##0.00"}, "B38": {"content": "10000", "format": "#,##0.00"}, "B39": {"content": "10000", "format": "#,##0.00"}, "B40": {"content": "10000", "format": "#,##0.00"}, "B41": {"content": "10000", "format": "#,##0.00"}, "B42": {"content": "10000", "format": "#,##0.00"}, "B43": {"content": "10000", "format": "#,##0.00"}, "B44": {"content": "10000", "format": "#,##0.00"}, "B45": {"content": "10000", "format": "#,##0.00"}, "B46": {"content": "10000", "format": "#,##0.00"}, "B47": {"content": "10000", "format": "#,##0.00"}, "B48": {"content": "10000", "format": "#,##0.00"}, "B49": {"content": "10000", "format": "#,##0.00"}, "B50": {"content": "10000", "format": "#,##0.00"}, "B51": {"content": "10000", "format": "#,##0.00"}, "B52": {"content": "10000", "format": "#,##0.00"}, "B53": {"content": "10000", "format": "#,##0.00"}, "B54": {"content": "10000", "format": "#,##0.00"}, "C12": {"content": "5000", "format": "#,##0.00"}, "C13": {"content": "5000", "format": "#,##0.00"}, "C14": {"content": "5000", "format": "#,##0.00"}, "C15": {"content": "5000", "format": "#,##0.00"}, "C16": {"content": "5000", "format": "#,##0.00"}, "C17": {"content": "5000", "format": "#,##0.00"}, "C18": {"content": "5000", "format": "#,##0.00"}, "C19": {"content": "5000", "format": "#,##0.00"}, "C20": {"content": "5000", "format": "#,##0.00"}, "C21": {"content": "5000", "format": "#,##0.00"}, "C22": {"content": "5000", "format": "#,##0.00"}, "C23": {"content": "5000", "format": "#,##0.00"}, "C24": {"content": "5000", "format": "#,##0.00"}, "C25": {"content": "5000", "format": "#,##0.00"}, "C26": {"content": "5000", "format": "#,##0.00"}, "C27": {"content": "5000", "format": "#,##0.00"}, "C28": {"content": "5000", "format": "#,##0.00"}, "C29": {"content": "5000", "format": "#,##0.00"}, "C30": {"content": "5000", "format": "#,##0.00"}, "C31": {"content": "5000", "format": "#,##0.00"}, "C32": {"content": "5000", "format": "#,##0.00"}, "C33": {"content": "5000", "format": "#,##0.00"}, "C34": {"content": "5000", "format": "#,##0.00"}, "C35": {"content": "5000", "format": "#,##0.00"}, "C36": {"content": "5000", "format": "#,##0.00"}, "C37": {"content": "5000", "format": "#,##0.00"}, "C38": {"content": "5000", "format": "#,##0.00"}, "C39": {"content": "5000", "format": "#,##0.00"}, "C40": {"content": "5000", "format": "#,##0.00"}, "C41": {"content": "5000", "format": "#,##0.00"}, "C42": {"content": "5000", "format": "#,##0.00"}, "C43": {"content": "5000", "format": "#,##0.00"}, "C44": {"content": "5000", "format": "#,##0.00"}, "C45": {"content": "5000", "format": "#,##0.00"}, "C46": {"content": "5000", "format": "#,##0.00"}, "C47": {"content": "5000", "format": "#,##0.00"}, "C48": {"content": "5000", "format": "#,##0.00"}, "C49": {"content": "5000", "format": "#,##0.00"}, "C50": {"content": "5000", "format": "#,##0.00"}, "C51": {"content": "5000", "format": "#,##0.00"}, "C52": {"content": "5000", "format": "#,##0.00"}, "C53": {"content": "5000", "format": "#,##0.00"}, "C54": {"content": "5000", "format": "#,##0.00"}, "A12": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",8))", "style": 28}, "A13": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",9))", "style": 28}, "A14": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",10))", "style": 28}, "A15": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",11))", "style": 28}, "A16": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",12))", "style": 28}, "A17": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",13))", "style": 28}, "A18": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",14))", "style": 28}, "A19": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",15))", "style": 28}, "A20": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",16))", "style": 28}, "A21": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",17))", "style": 28}, "A22": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",18))", "style": 28}, "A23": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",19))", "style": 28}, "A24": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",20))", "style": 28}, "A25": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",21))", "style": 28}, "A26": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",22))", "style": 28}, "A27": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",23))", "style": 28}, "A28": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",24))", "style": 28}, "A29": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",25))", "style": 28}, "A30": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",26))", "style": 28}, "A31": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",27))", "style": 28}, "A32": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",28))", "style": 28}, "A33": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",29))", "style": 28}, "A34": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",30))", "style": 28}, "A35": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",31))", "style": 28}, "A36": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",32))", "style": 28}, "A37": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",33))", "style": 28}, "A38": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",34))", "style": 28}, "A39": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",35))", "style": 28}, "A40": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",36))", "style": 28}, "A41": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",37))", "style": 28}, "A42": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",38))", "style": 28}, "A43": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",39))", "style": 28}, "A44": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",40))", "style": 28}, "A45": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",41))", "style": 28}, "A46": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",42))", "style": 28}, "A47": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",43))", "style": 28}, "A48": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",44))", "style": 28}, "A49": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",45))", "style": 28}, "A50": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",46))", "style": 28}, "A51": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",47))", "style": 28}, "A52": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",48))", "style": 28}, "A53": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",49))", "style": 28}, "A54": {"content": "=PIVOT.HEADER(\"3\",\"user_id\",PIVOT.POSITION(\"3\",\"user_id\",50))", "style": 28}}, "conditionalFormats": [], "figures": []}], "activeSheet": "a9d3b61d-b996-4144-b391-a1b246e80c9b", "entities": {}, "styles": {"0": {"fillColor": "white", "textColor": "black", "fontSize": 11}, "2": {"fillColor": "#f2f2f2"}, "3": {"fillColor": "#f2f2f2", "bold": true}, "4": {"fillColor": "#f2f2f2", "bold": true, "textColor": "#756f6f"}, "5": {"fillColor": "#f2f2f2", "textColor": "#756f6f"}, "6": {"fillColor": "#deeaf6"}, "7": {"bold": true}, "8": {"fillColor": "#f2f2f2", "bold": true, "align": "center"}, "9": {"fillColor": "#f2f2f2", "bold": true, "textColor": "#756f6f", "align": "center"}, "10": {"bold": true, "align": "center"}, "11": {"fillColor": "#f2f2f2", "bold": false, "align": "center"}, "12": {"fillColor": "#f2f2f2", "bold": false, "textColor": "#756f6f", "align": "center"}, "13": {"bold": false, "align": "center"}, "14": {"fillColor": "#deeaf6", "align": "center"}, "15": {"align": "center"}, "16": {"fillColor": "#deeaf6", "align": "center", "bold": true}, "17": {"fillColor": "#deeaf6", "align": "center", "bold": true, "fontSize": 12}, "18": {"bold": true, "align": "center", "fontSize": 12}, "19": {"fillColor": "#f2f2f2", "bold": false}, "20": {"bold": false}, "21": {"fillColor": "#f2f2f2", "align": "center"}, "22": {"fillColor": "#f2f2f2", "textColor": "#756f6f", "align": "center"}, "23": {"fillColor": "#deeaf6", "align": "center", "fontSize": 12}, "24": {"align": "center", "fontSize": 12}, "25": {"fillColor": "#e3efd9", "bold": true}, "26": {"fillColor": "#e3efd9"}, "27": {"fillColor": "#e3efd9", "bold": false}, "28": {"fillColor": "#ffffff", "bold": true}, "29": {"fillColor": "#ffffff"}, "30": {"fillColor": "#deeaf6", "bold": true}, "31": {"fillColor": "#deeaf6", "bold": true, "fontSize": 12}, "32": {"bold": true, "fontSize": 12}, "33": {"fillColor": "#fff2cd"}, "34": {"fillColor": "#c5e0b3", "bold": false, "textColor": "#756f6f", "align": "center"}, "35": {"fillColor": "#e3efd9", "bold": false, "textColor": "#756f6f", "align": "center"}, "36": {"fillColor": "#fff2cd", "bold": false, "textColor": "#756f6f", "align": "center"}, "37": {"fillColor": "#e3efd9", "bold": true, "align": "center"}, "38": {"fillColor": "#f2f2f2", "bold": true, "align": "center", "textColor": "#2f5596"}, "39": {"fillColor": "#e3efd9", "bold": false, "align": "center"}, "40": {"textColor": "#262626"}, "41": {"textColor": "#595959"}, "42": {"textColor": "#7f7f7f"}, "43": {"fillColor": "#e3efd9", "bold": false, "align": "center", "italic": true}, "44": {"fillColor": "#e3efd9", "bold": false, "italic": true}, "45": {"textColor": "#7f7f7f", "italic": true}, "46": {"bold": true, "fillColor": "#f3f3f3"}}, "borders": {}, "pivots": {"1": {"model": "crm.lead", "rowGroupBys": ["date_deadline:month", "team_id"], "colGroupBys": ["won_status"], "measures": [{"field": "recurring_revenue_monthly", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}, {"field": "recurring_revenue_monthly_prorated", "operator": "sum"}, {"field": "prorated_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "allowed_company_ids": [1], "default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1, "pivot_measures": ["recurring_revenue_monthly", "recurring_revenue_monthly_prorated", "expected_revenue", "prorated_revenue"], "pivot_column_groupby": ["won_status"], "pivot_row_groupby": ["date_deadline:month", "team_id"]}, "id": 1, "isLoaded": false, "promise": {}}, "2": {"model": "crm.lead", "rowGroupBys": ["date_deadline:month"], "colGroupBys": ["won_status"], "measures": [{"field": "recurring_revenue_monthly", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}, {"field": "recurring_revenue_monthly_prorated", "operator": "sum"}, {"field": "prorated_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "allowed_company_ids": [1], "default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1, "pivot_measures": ["recurring_revenue_monthly", "recurring_revenue_monthly_prorated", "expected_revenue", "prorated_revenue"], "pivot_column_groupby": ["won_status"], "pivot_row_groupby": []}, "id": 2, "isLoaded": false, "promise": {}}, "3": {"model": "crm.lead", "rowGroupBys": ["user_id"], "colGroupBys": [], "measures": [{"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], ["user_id", "!=", false]], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "allowed_company_ids": [1], "default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1, "pivot_measures": ["expected_revenue"], "pivot_column_groupby": [], "pivot_row_groupby": ["user_id"]}, "id": 3, "isLoaded": false, "promise": {}}}, "globalFilters": [{"id": "86fcd40e-c0b6-41ef-bb8e-3dbc380499bf", "label": "Salesperson", "type": "relation", "rangeType": "year", "fields": {"1": {"field": "user_id", "type": "many2one"}, "2": {"field": "user_id", "type": "many2one"}}, "defaultValue": [], "modelName": "res.users"}, {"id": "3b1ded51-de6c-48e7-bd53-48609b66a6d1", "label": "Year", "type": "date", "rangeType": "year", "fields": {"1": {"field": "date_deadline", "type": "date"}, "2": {"field": "date_deadline", "type": "date"}}, "defaultValue": {"year": "this_year"}}]}