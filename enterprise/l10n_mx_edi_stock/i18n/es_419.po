# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx_edi_stock
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 08:14+0000\n"
"PO-Revision-Date: 2023-09-07 08:14+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_419\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | Certification Date:</span>"
msgstr "<span> | Fecha de timbrado:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | Emission Date:</span>"
msgstr "<span> | Fecha de emisión:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | Expedition place:</span>"
msgstr "<span> | Lugar de expedición:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | Fiscal Folio:</span>"
msgstr "<span> | Folio fiscal:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | Fiscal Regime:</span>"
msgstr "<span> | Régimen fiscal:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span> | SAT Certificate:</span>"
msgstr "<span> | Certificado del SAT:</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span>Digital stamp SAT</span>"
msgstr "<span>Sello digital SAT</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span>Digital stamp of the emitter</span>"
msgstr "<span>Sello digital del emisor</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span>Emitter certificate:</span>"
msgstr "Certificado del emisor"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span>Extra Info</span>"
msgstr "<span>Información adicional</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<span>Original chain complement of digital certification SAT</span>"
msgstr "<span>Cadena original del complemento digital del SAT</span>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Code</strong>"
msgstr "<strong>Código</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Distance (KM)</strong>"
msgstr "<strong>Distancia (KM)</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Insurer</strong>"
msgstr "<strong>Aseguradora</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Intermediaries</strong>"
msgstr "<strong>Intermediarios</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Licence Plate</strong>"
msgstr "<strong>Placa</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Model</strong>"
msgstr "<strong>Modelo</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Policy No</strong>"
msgstr "<strong>Núm de Póliza</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>SCT Permit No</strong>"
msgstr "<strong>Núm de permiso SCT</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>SCT Permit Type</strong>"
msgstr "<strong>Tipo permiso SCT</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>This document is a printed representation of a CFDI</strong>"
msgstr ""
"<strong>Este documento es una representación impresa de un CFDI</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Trailers</strong>"
msgstr "<strong>Remolques</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>UOM</strong>"
msgstr "<strong>UdM</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Vehicle Configuration</strong>"
msgstr "<strong>Configuración del vehículo</strong>"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "<strong>Weight</strong>"
msgstr "<strong>Peso</strong>"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/l10n_mx_edi_vehicle.py:0
#, python-format
msgid "A maximum of 2 trailers are allowed per vehicle"
msgstr "Se permite un máximo de 2 remolques por vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__active
msgid "Active"
msgstr "Activo"

#. module: l10n_mx_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_mx_edi_stock.l10n_mx_edi_vehicle_actions
msgid "Add a new vehicle setup for MX delivery guide"
msgstr "Agregue una nueva configuración vehicular para la Carta Porte MX"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "All products require a UNSPSC Code"
msgstr "Todos los productos requieren un código UNSPSC"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.vehicle_search_view
msgid "Archived"
msgstr "Archivado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_figure__type__03
msgid "Arrendador"
msgstr "Arrendador"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf19
msgid ""
"Autorización expresa para circular en los caminos y puentes de jurisdicción "
"federal con configuraciones de tractocamión doblemente articulado."
msgstr "Autorización expresa para circular en los caminos y puentes de jurisdicción "
"federal con configuraciones de tractocamión doblemente articulado."


#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf11
msgid ""
"Autotransporte Federal de Carga Especializada cuyo ámbito de aplicación "
"comprende la franja fronteriza con Estados Unidos."
msgstr "Autotransporte federal de carga especializada cuyo ámbito de aplicación "
"comprende la franja fronteriza con los Estados Unidos de América."


#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf20
msgid "Autotransporte Federal de Carga Especializada de fondos y valores."
msgstr "Autotransporte federal de carga especializada de fondos y valores."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf03
msgid ""
"Autotransporte Federal de Carga Especializada de materiales y residuos "
"peligrosos."
msgstr "Autotransporte federal de carga especializada de materiales y residuos "
"peligrosos."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf10
msgid ""
"Autotransporte Federal de Carga General cuyo ámbito de aplicación comprende "
"la franja fronteriza con Estados Unidos."
msgstr "Autotransporte federal de carga general cuyo ámbito de aplicación comprende "
"la franja fronteriza con los Estados Unidos Unidos de América."


#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf01
msgid "Autotransporte Federal de carga general."
msgstr "Autotransporte federal de carga general."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf08
msgid "Autotransporte internacional de carga de largo recorrido."
msgstr "Autotransporte internacional de carga de largo recorrido"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf09
msgid ""
"Autotransporte internacional de carga especializada de materiales y residuos "
"peligrosos de largo recorrido."
msgstr "Autotransporte internacional de carga especializada de materiales y residuos "
"peligrosos de largo recorrido."


#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "Barcode"
msgstr "Código de Barras"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "CFDI"
msgstr "CFDI"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_origin
msgid "CFDI Origin"
msgstr "CFDI relacionado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_state
msgid "CFDI status"
msgstr "Estado del CFDI"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr001
msgid "Caballete"
msgstr "Caballete"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr002
msgid "Caja"
msgstr "Caja"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr003
msgid "Caja Abierta"
msgstr "Caja abierta"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr004
msgid "Caja Cerrada"
msgstr "Caja cerrada"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr005
msgid "Caja De Recolección Con Cargador Frontal"
msgstr "Caja de recolección con cargador frontal"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr006
msgid "Caja Refrigerada"
msgstr "Caja refrigerada"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr007
msgid "Caja Seca"
msgstr "Caja seca"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr008
msgid "Caja Transferencia"
msgstr "Caja transferencia"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr009
msgid "Cama Baja o Cuello Ganso"
msgstr "Cama baja o Cuello de ganso"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c2
msgid ""
"Camión Unitario (2 llantas en el eje delantero y 4 llantas en el eje trasero)"
msgstr "Camión unitario (2 llantas en el eje delantero y 4 llantas en el eje trasero)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c3
msgid ""
"Camión Unitario (2 llantas en el eje delantero y 6 o 8 llantas en los dos "
"ejes traseros)"
msgstr "Camión Unitario (2 llantas en el eje delantero y 6 o 8 llantas en los dos "
"ejes traseros)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c3r3
msgid "Camión-Remolque (10 llantas en el camión y 12 llantas en remolque)"
msgstr "Camión-Remolque (10 llantas en el camión y 12 llantas en remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c3r2
msgid "Camión-Remolque (10 llantas en el camión y 8 llantas en remolque)"
msgstr "Camión-Remolque (10 llantas en el camión y 8 llantas en remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c2r3
msgid "Camión-Remolque (6 llantas en el camión y 12 llantas en remolque)"
msgstr "Camión-Remolque (6 llantas en el camión y 12 llantas en remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__c2r2
msgid "Camión-Remolque (6 llantas en el camión y 8 llantas en remolque)"
msgstr "Camión-Remolque (6 llantas en el camión y 8 llantas en remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_document__state__picking_cancel
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Cancel Delivery Guide"
msgstr "Cancelar Carta Porte"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_sat_state__cancelled
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_document__state__picking_cancel_failed
msgid "Cancelled In Error"
msgstr "Cancelado por error"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr010
msgid "Chasis Portacontenedor"
msgstr "Chasis portacontenedor"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__code
msgid "Code"
msgstr "Código"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Compute Distance"
msgstr "Calcular distancia"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpta01
msgid ""
"Concesión y/o autorización para el servicio regular nacional y/o "
"internacional para empresas mexicanas"
msgstr "Concesión y/o autorización para el servicio regular nacional y/o "
"internacional para empresas mexicanas"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr011
msgid "Convencional De Chasis"
msgstr "Convencional de chasis"

#. module: l10n_mx_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_mx_edi_stock.vehicle_list_action
msgid "Create the first vehicle"
msgstr "Crear el primer vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__create_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__create_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__create_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__create_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__create_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__create_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Delivery Guide"
msgstr "Guía de entrega"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_des_lat
msgid "Destination Latitude"
msgstr "Latitud de Destino"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_des_lon
msgid "Destination Longitude"
msgstr "Longitud de Destino"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__display_name
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__display_name
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__display_name
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Distance"
msgstr "Distancia"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid ""
"Distance calculation requires both the source and destination coordinates"
msgstr ""
"El cálculo de la distancia requiere tanto las coordinadas de origen como las de destino"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Distance in KM must be specified when using federal transport"
msgstr ""
"Debe especificar la distancia en KM cuando se usa Autotransporte Federal"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_distance
msgid "Distance to Destination (KM)"
msgstr "Distancia al destino (KM)"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Download"
msgstr "Descargar"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf18
msgid "Empresas fabricantes o distribuidoras de vehículos nuevos."
msgstr "Empresas fabricantes o distribuidoras de vehículos nuevos."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf17
msgid "Empresas trasladistas de vehículos nuevos."
msgstr "Empresas trasladistas de vehículos nuevos"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr012
msgid "Equipo Especial"
msgstr "Equipo especial"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_sat_state__error
msgid "Error"
msgstr "Error"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__otroevgp
msgid "Especializado de carga Voluminosa y/o Gran Peso"
msgstr "Especializado de carga voluminos y/o de gran peso"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr013
msgid "Estacas"
msgstr "Estacas"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_transport_type__01
msgid "Federal Transport"
msgstr "Autotransporte Federal"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_uuid
msgid "Fiscal Folio"
msgstr "Folio fiscal"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_uuid
msgid "Folio in electronic invoice, is returned by SAT when send to stamp."
msgstr "El SAT regresa el folio en la factura electrónica cuando se envia a timbrar. "

#. module: l10n_mx_edi_stock
#: model_terms:ir.actions.act_window,help:l10n_mx_edi_stock.l10n_mx_edi_vehicle_actions
msgid ""
"For delivery guides that use federal highways (federal transportation),\n"
"            you need to supply lots of information about the vehicle setup "
"you are using.\n"
"            It defines not only the vehicle itself, but also which trailers "
"or drivers you\n"
"            are using, so you could have multiple setups for the same "
"vehicle."
msgstr ""
"Para las Carta Porte que usen Carreteras Federales (Autotransporte "
"Federal),\n"
"            se necesita suministrar información sobre la configuración "
"vehicular utilizada.\n"
"            No solo se define el vehículo, sino también los remolques y el "
"conductor utilizado,\n"
"            por tal motivo podría tener múltiples configuraciones para el "
"mismo vehículo."

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Generate Delivery Guide"
msgstr "Generar Carta Porte"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Get Coordinates"
msgstr "Obtener coordenadas"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr016
msgid "Grúa "
msgstr "Grúa"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr015
msgid "Grúa Industrial"
msgstr "Grúa industrial"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplata
msgid "Grúa de Plataforma Tipo A"
msgstr "Grúa de plataforma Tipo A"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplatb
msgid "Grúa de Plataforma Tipo B"
msgstr "Grúa de plataforma Tipo B"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplatc
msgid "Grúa de Plataforma Tipo C"
msgstr "Grúa de plataforma Tipo C"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplatd
msgid "Grúa de Plataforma Tipo D"
msgstr "Grúa de plataforma Tipo D"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gpluta
msgid "Grúa de Pluma Tipo A"
msgstr "Grúa de pluma Tipo A"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplutb
msgid "Grúa de Pluma Tipo B"
msgstr "Grúa de pluma Tipo B"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplutc
msgid "Grúa de Pluma Tipo C"
msgstr "Grúa de pluma Tipo C"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__gplutd
msgid "Grúa de Pluma Tipo D"
msgstr "Grúa de pluma Tipo D"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr014
msgid "Góndola Madrina"
msgstr "Góndola madrina"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__id
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__id
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__id
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__id
msgid "ID"
msgstr "ID"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__figure_ids
msgid ""
"Information corresponding to the transport intermediaries, as well as those "
"taxpayers related to the transportation method used to transport the goods"
msgstr ""
"La información correspondiente a los intermediarios del transporte, así como de "
"aquellos contribuyentes relacionados con el método de transporte utilizado "
"para transportar las mercancías"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__transport_insurer
msgid "Insurance Company"
msgstr "Compañía aseguradora"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__transport_insurance_policy
msgid "Insurance Policy Number"
msgstr "Número de poliza"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr017
msgid "Integral"
msgstr "Integral"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__figure_ids
msgid "Intermediaries"
msgstr "Intermediarios"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr018
msgid "Jaula"
msgstr "Jaula"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_attachment_id
msgid "L10N Mx Edi Cfdi Attachment"
msgstr "Archivo adjunto L10N Mx Edi Cfdi "

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_document_ids
msgid "L10N Mx Edi Document"
msgstr "Documento L10N Mx Edi"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_external_trade
msgid "L10N Mx Edi External Trade"
msgstr "L10N Mx Edi Comercio Exterior"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_is_cfdi_needed
msgid "L10N Mx Edi Is Cfdi Needed"
msgstr "L10N Mx Edi Se necesita CFDI"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_update_sat_needed
msgid "L10N Mx Edi Update Sat Needed"
msgstr "L10N Mx Se necesita actualización del Edi SAT"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_move_line__l10n_mx_edi_weight
msgid "L10N Mx Edi Weight"
msgstr "L10N Mx Edi Peso"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__write_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__write_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__write_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__write_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__write_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__write_date
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "License"
msgstr "Licencia"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__vehicle_licence
msgid ""
"License plate number of the vehicle in which the goods are transferred. "
"Alphanumeric characters only, no dashes and/or spaces"
msgstr ""
" Número de placas del vehículo en el que se trasladan las mercancías. "
"Sólo caracteres alfanuméricos, sin guiones y/o espacios"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_l10n_mx_edi_part
msgid "MX EDI Intermediary Part"
msgstr "Parte del Intermediario MX EDI"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_l10n_mx_edi_vehicle
msgid "MX EDI Vehicle"
msgstr "Vehículo MX EDI"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_l10n_mx_edi_figure
msgid "MX EDI Vehicle Intermediary Figure"
msgstr "Figura de transporte MX EDI"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_l10n_mx_edi_trailer
msgid "MX EDI Vehicle Trailer"
msgstr "Remolque para vehículos MX EDI"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr019
msgid "Media Redila"
msgstr "Media redila"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_l10n_mx_edi_document
msgid "Mexican documents that needs to transit outside of Odoo"
msgstr "Documentos mexicanos que necesitan para transitar fuera de Odoo"

#. module: l10n_mx_edi_stock
#: model:ir.ui.menu,name:l10n_mx_edi_stock.menu_stock_config_settings_mx
msgid "Mexico"
msgstr "México"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_part__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_transport_type__00
msgid "No Federal Highways"
msgstr "No usa carreteras federales"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_sat_state__not_defined
msgid "Not Defined"
msgstr "Sin definir"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_sat_state__not_found
msgid "Not Found"
msgstr "No se encontro"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_figure__type__04
msgid "Notificado"
msgstr "Notificado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__name
msgid "Number Plate"
msgstr "Número de placa"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_figure__type__01
msgid "Operador"
msgstr "Operador"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "Operator"
msgstr "Operador"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_res_partner__l10n_mx_edi_operator_licence
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_res_users__l10n_mx_edi_operator_licence
msgid "Operator Licence"
msgstr "Licencia del operador"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr020
msgid "Pallet o Celdillas"
msgstr "Pallet o celdillas"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__operator_id
msgid "Partner"
msgstr "Contacto"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__part_ids
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "Parts"
msgstr "Partes"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpxx00
msgid "Permiso no contemplado en el catálogo."
msgstr "Permiso no contemplado en el catálogo."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpta02
msgid "Permiso para el servicio aéreo regular de empresas extranjeras"
msgstr "Permiso para el servicio aéreo regular de empresas extranjeras"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpta03
msgid ""
"Permiso para el servicio nacional e internacional no regular de fletamento"
msgstr "Permiso para el servicio nacional e internacional no regular de fletamento"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpta04
msgid ""
"Permiso para el servicio nacional e internacional no regular de taxi aéreo"
msgstr "Permiso para el servicio nacional e internacional no regular de taxi aéreo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tptm01
msgid "Permiso temporal para navegación de cabotaje"
msgstr "Permiso temporal para navegación de cabotaje"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_document__picking_id
msgid "Picking"
msgstr "Recolección"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr021
msgid "Plataforma"
msgstr "Plataforma"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr022
msgid "Plataforma Con Grúa"
msgstr "Plataforma Con grúa"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr023
msgid "Plataforma Encortinada"
msgstr "Plataforma encortinada"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "Plate"
msgstr "Placa"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Please configure MapBox to use this feature"
msgstr "Por favor configure MapBox para usar esta función"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de inventario)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_figure__type__02
msgid "Propietario"
msgstr "Propietario"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr024
msgid "Redilas"
msgstr "Redilas"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr025
msgid "Refrigerador"
msgstr "Refrigerador"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_figure__operator_id
msgid ""
"Register the contact that is involved depending on its responsibility in the "
"transport (Operador, Propietario, Arrendador, Notificado)"
msgstr ""
"Registre el contacto que interviene en función de su responsabilidad en "
"el transporte (Operador, Propietario, Arrendador, Notificado)"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Retry"
msgstr "Volver a intentar"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr026
msgid "Revolvedora"
msgstr "Revolvedora"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_sat_state
msgid "SAT status"
msgstr "Estado del SAT"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__name
msgid "SCT Permit Number"
msgstr "Número de permiso SCT"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__transport_perm_sct
msgid "SCT Permit Type"
msgstr "Tipo de permiso SCT"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr027
msgid "Semicaja"
msgstr "Semicaja"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_document__state__picking_sent
msgid "Sent"
msgstr "Enviado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_document__state__picking_sent_failed
msgid "Sent In Error"
msgstr "Enviado por error"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf12
msgid "Servicio auxiliar de arrastre en las vías generales de comunicación."
msgstr "Servicio auxiliar de arrastre en las vías generales de comunicación."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf13
msgid ""
"Servicio auxiliar de servicios de arrastre, arrastre y salvamento, y "
"depósito de vehículos en las vías generales de comunicación."
msgstr "Servicio auxiliar de servicios de arrastre, arrastre y salvamento, y "
"depósito de vehículos en las vías generales de comunicación."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__otrosg
msgid "Servicio de Grúas"
msgstr "Servicio de grúas"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf14
msgid ""
"Servicio de paquetería y mensajería en las vías generales de comunicación."
msgstr "Servicio de paquetería y mensajería en las vías generales de comunicación."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf16
msgid "Servicio federal para empresas arrendadoras servicio público federal."
msgstr "Servicio federal para empresas arrendadoras servicio público federal."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_state__sent
msgid "Signed"
msgstr "Firmado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_src_lat
msgid "Source Latitude"
msgstr "Latitud de origen"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_src_lon
msgid "Source Longitude"
msgstr "Longitud de origen"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_origin
msgid "Specify the existing Fiscal Folios to replace. Prepend with '04|'"
msgstr ""
"Especificar el Folio Fiscal existente a ser reemplazado. Preponer con '04|'"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_transport_type
msgid ""
"Specify the transportation method. The Delivery Guide will contain the "
"Complemento Carta Porte only when federal transport is used"
msgstr ""
"Especifique el método de transporte. La guía de entrega contendrá el "
"Complemento Carta Porte sólo cuando se utilice transporte federal"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_document__state
msgid "State"
msgstr "Estado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__sub_type
msgid "Sub Type"
msgstr "Subtipo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_cfdi_cancel_picking_id
msgid "Substituted By"
msgstr "Sustituido por"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr028
msgid "Tanque"
msgstr "Tanque"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The CFDI document has been successfully cancelled."
msgstr "El documento CFDI ha sido cancelado con éxito."

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid ""
"The CFDI document was successfully created and signed by the government."
msgstr "El documento CFDI fue creado con éxito y firmado por el gobierno."

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid ""
"The Delivery Guide is only available for shipping in MX. You might want to "
"install comex features"
msgstr ""
"La Carta Porte está sólo disponible para transportes en México. Le recomendamos "
"instalar las funcionalidades COMEX desde las aplicaciones"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__transport_insurer
msgid "The name of the insurer that covers the liability risks of the vehicle"
msgstr ""
"El nombre de la aseguradora que cubre los riesgos de responsabilidad civil "
"del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__name
msgid "The permit number granted to the unit performing the transfer of goods"
msgstr ""
"El número de permiso otorgado a la unidad con la que se realiza el transporte"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__transport_perm_sct
msgid "The type of permit code to carry out the goods transfer service"
msgstr ""
"El tipo de código de permiso para el servicio de transporte de los bienes"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__vehicle_config
msgid "The type of vehicle used"
msgstr "Tipo de vehículo usado"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/l10n_mx_edi_vehicle.py:0
#, python-format
msgid ""
"The vehicle intermediaries must contain at least one intermediary of type: "
"Operator"
msgstr ""
"Los intermediarios del vehículo deben contener al menos uno con el siguiente "
"tipo: Operador"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_vehicle_id
msgid "The vehicle used for Federal Transport"
msgstr "Vehículo utilizado para el Autotransporte Federal"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "The warehouse address and the delivery address are required"
msgstr "Se requiere la dirección del depósito y la dirección de entrega"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr029
msgid "Tolva"
msgstr "Tolva"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s3
msgid ""
"Tractocamión Articulado (10 llantas en el tractocamión, 12 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (10 llantas en el tractocamión, 12 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s1
msgid ""
"Tractocamión Articulado (10 llantas en el tractocamión, 4 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (10 llantas en el tractocamión, 4 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s2
msgid ""
"Tractocamión Articulado (10 llantas en el tractocamión, 8 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (10 llantas en el tractocamión, 8 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s3
msgid ""
"Tractocamión Articulado (6 llantas en el tractocamión, 12 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (6 llantas en el tractocamión, 12 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s1
msgid ""
"Tractocamión Articulado (6 llantas en el tractocamión, 4 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (6 llantas en el tractocamión, 4 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s2
msgid ""
"Tractocamión Articulado (6 llantas en el tractocamión, 8 llantas en el "
"semirremolque)"
msgstr "Tractocamión articulado (6 llantas en el tractocamión, 8 llantas en el "
"semirremolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s1r3
msgid ""
"Tractocamión Semirremolque-Remolque (10 llantas en el tractocamión, 4 "
"llantas en el semirremolque y 12 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (10 llantas en el tractocamión, 4 "
"llantas en el semirremolque y 12 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s1r2
msgid ""
"Tractocamión Semirremolque-Remolque (10 llantas en el tractocamión, 4 "
"llantas en el semirremolque y 8 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (10 llantas en el tractocamión, 4 "
"llantas en el semirremolque y 8 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s2r3
msgid ""
"Tractocamión Semirremolque-Remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 12 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 12 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s2r4
msgid ""
"Tractocamión Semirremolque-Remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 16 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 16 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s2r2
msgid ""
"Tractocamión Semirremolque-Remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 8 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque y 8 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s1r3
msgid ""
"Tractocamión Semirremolque-Remolque (6 llantas en el tractocamión, 4 llantas "
"en el semirremolque y 12 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (6 llantas en el tractocamión, 4 llantas "
"en el semirremolque y 12 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s1r2
msgid ""
"Tractocamión Semirremolque-Remolque (6 llantas en el tractocamión, 4 llantas "
"en el semirremolque y 8 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (6 llantas en el tractocamión, 4 llantas "
"en el semirremolque y 8 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s2r2
msgid ""
"Tractocamión Semirremolque-Remolque (6 llantas en el tractocamión, 8 llantas "
"en el semirremolque y 8 llantas en el remolque)"
msgstr "Tractocamión semirremolque-remolque (6 llantas en el tractocamión, 8 llantas "
"en el semirremolque y 8 llantas en el remolque)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s3s2
msgid ""
"Tractocamión Semirremolque-Semirremolque (10 llantas en el tractocamión, 12 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "
"trasero)"
msgstr "Tractocamión semirremolque-semirremolque (10 llantas en el tractocamión, 12 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "
"trasero)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t3s2s2
msgid ""
"Tractocamión Semirremolque-Semirremolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "
"trasero)"
msgstr "Tractocamión Semirremolque-Semirremolque (10 llantas en el tractocamión, 8 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "
"trasero)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__t2s2s2
msgid ""
"Tractocamión Semirremolque-Semirremolque (6 llantas en el tractocamión, 8 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "
"trasero)"
msgstr "Tractocamión semirremolque-semirremolque (6 llantas en el tractocamión, 8 "
"llantas en el semirremolque delantero y 8 llantas en el semirremolque "

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__trailer_ids
msgid "Trailers"
msgstr "Remolques"

#. module: l10n_mx_edi_stock
#: model:ir.model,name:l10n_mx_edi_stock.model_stock_picking
msgid "Transfer"
msgstr "Trasladar"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Transport"
msgstr "Transportar"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_transport_type
msgid "Transport Type"
msgstr "Tipo de transporte"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf07
msgid "Transporte Privado de materiales y residuos peligrosos."
msgstr "Transporte privado de materiales y residuos peligrosos."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf04
msgid "Transporte de automóviles sin rodar en vehículo tipo góndola."
msgstr "Transporte de automóviles sin rodar en vehículo tipo góndola"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf05
msgid "Transporte de carga de gran peso y/o volumen de hasta 90 toneladas."
msgstr "Transporte de carga de gran peso y/o volumen de hasta 90 toneladas."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf06
msgid ""
"Transporte de carga especializada de gran peso y/o volumen de más 90 "
"toneladas."
msgstr "Transporte de carga especializada de gran peso y/o volumen de más 90 "
"toneladas."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf15
msgid ""
"Transporte especial para el tránsito de grúas industriales con peso máximo "
"de 90 toneladas."
msgstr "Transporte especial para el tránsito de grúas industriales con peso máximo "
"de 90 toneladas."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__transport_perm_sct__tpaf02
msgid "Transporte privado de carga."
msgstr "Transporte privado de carga."

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__type
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.l10n_mx_edi_cartaporte_report_delivery_document
msgid "Type"
msgstr "Tipo"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "Unable to connect to mapbox"
msgstr "No es posible conectarse a MapBox"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,help:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__trailer_ids
msgid "Up to 2 trailers used on this vehicle"
msgstr "Hasta 2 remolques utilizados en este vehículo"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.stock_picking_form_inherit_l10n_mx_edi_stock
msgid "Update SAT"
msgstr "Actualización SAT"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__stock_picking__l10n_mx_edi_cfdi_sat_state__valid
msgid "Validated"
msgstr "Validado"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_figure__vehicle_id
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_trailer__vehicle_id
msgid "Vehicle"
msgstr "Vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__vehicle_config
msgid "Vehicle Configuration"
msgstr "Configuración vehicular"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__vehicle_model
msgid "Vehicle Model Year"
msgstr "Año/modelo del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_l10n_mx_edi_vehicle__vehicle_licence
msgid "Vehicle Plate Number"
msgstr "Número de placa del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields,field_description:l10n_mx_edi_stock.field_stock_picking__l10n_mx_edi_vehicle_id
msgid "Vehicle Setup"
msgstr "Configuración del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.ui.menu,name:l10n_mx_edi_stock.menu_stock_mx_vehicles
msgid "Vehicle Setups"
msgstr "Configuraciones del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.actions.act_window,name:l10n_mx_edi_stock.l10n_mx_edi_vehicle_actions
msgid "Vehicle Setups (MX)"
msgstr "Configuraciones del vehículo (MX)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_vehicle__vehicle_config__vl
msgid ""
"Vehículo ligero de carga (2 llantas en el eje delantero y 2 llantas en el "
"eje trasero)"
msgstr "Vehículo ligero de carga (2 llantas en el eje delantero y 2 llantas en el "
"eje trasero)"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr031
msgid "Volteo"
msgstr "Volteo"

#. module: l10n_mx_edi_stock
#: model:ir.model.fields.selection,name:l10n_mx_edi_stock.selection__l10n_mx_edi_trailer__sub_type__ctr032
msgid "Volteo Desmontable"
msgstr "Volteo desmontable"

#. module: l10n_mx_edi_stock
#. odoo-python
#: code:addons/l10n_mx_edi_stock/models/stock_picking.py:0
#, python-format
msgid "You must select a transport type to generate the delivery guide"
msgstr "Debe seleccionar un tipo de transporte para generar la Carta Porte"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.vehicle_form_view
msgid "vehicle Form"
msgstr "Formulario del vehículo"

#. module: l10n_mx_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_stock.vehicle_tree_view
msgid "vehicle Tree"
msgstr "Árbol del vehículo"

#. module: l10n_mx_edi_stock
#: model:ir.actions.act_window,name:l10n_mx_edi_stock.vehicle_list_action
msgid "vehicles"
msgstr "vehículos"
