<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="ir_rule_worksheet_template_maintenance" model="ir.rule">
            <field name="name">Worksheet Template: Maintenance Access</field>
            <field name="model_id" ref="model_worksheet_template"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">[('res_model', '=', 'maintenance.request')]</field>
        </record>

    </data>
</odoo>
