<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.documents</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="70"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="Documents" string="Documents" name="documents" groups="documents.group_documents_manager" notApp="1">
                    <h2>Files Centralization</h2>
                    <block>
                        <setting id="deletion_delay" string="Deletion delay (days)" help="Set deletion delay for documents in the Trash">
                            <div class="content-group">
                                <div class="row mt16">
                                    <field class="w-50" name="deletion_delay"/>
                                </div>
                            </div>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>

    <record id="settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context" eval="{'module': 'general_settings', 'bin_size': False}"/>
    </record>

    <record id="configuration_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module': 'documents', 'bin_size': False}</field>
    </record>
</odoo>
