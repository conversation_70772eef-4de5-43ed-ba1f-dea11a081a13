<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="documents.DocumentsKanbanView" t-inherit="web.KanbanView" t-inherit-mode="primary">
        <xpath expr="//Layout" position="attributes">
            <attribute name="className">(model.useSampleModel ? 'o_view_sample_data' : '') + ' o_documents_content o_documents_kanban'</attribute>
        </xpath>
        <xpath expr="//Layout" position="inside">
            <t t-set-slot="control-panel-create-button">
                <t t-call="documents.DocumentsViews.ControlPanel"/>
            </t>
        </xpath>
        <xpath expr="//Layout/t[@t-component='props.Renderer']" position="attributes">
            <attribute name="inspectedDocuments">documentStates.inspectedDocuments</attribute>
            <attribute name="previewStore">documentStates.previewStore</attribute>
        </xpath>
    </t>
</templates>
