# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_sale_renting
# 
# Translators:
# <PERSON><PERSON>, 2023
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Kategorie"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Aktuální"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Customer"
msgstr "Zákazník"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Daily Rentals"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Ordered"
msgstr "Objednáno"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Ordered Qty"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Orders"
msgstr "Objednávky"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Období"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Picked-up Qty"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Předchozí"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Product Category"
msgstr "Produktová kategorie"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Quotations"
msgstr "Nabídky"

#. module: spreadsheet_dashboard_sale_renting
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_sale_renting.spreadsheet_dashboard_rental
msgid "Rental"
msgstr "Pronájem"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Rental Analysis by Customer"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Rental Analysis by Product"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Rental Analysis by Product Category"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Rental Analysis by Salesman"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Rentals"
msgstr "Pronájmy"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Returned Qty"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr "Výnos"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Salesperson"
msgstr "Obchodník"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Top Customers"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Top Product Categories"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "Top Salespeople"
msgstr "Nejlepší prodejci"

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "rental stats - current"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "rental stats - previous"
msgstr ""

#. module: spreadsheet_dashboard_sale_renting
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_renting/data/files/rental_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "od posledního období"
