# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_sign
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr "一組條件和操作，可用於與條件匹配的所有附件"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "建立"

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid ""
"Each document template can be configured to centralize signed documents into"
" a specific workspace."
msgstr "每個文件範本都可進行配置，以便將已簽署的文件集中到特定的工作區。"

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Go to Sign Document Templates"
msgstr "前往簽名文件範本"

#. module: documents_sign
#. odoo-python
#: code:addons/documents_sign/models/documents_workflow_rule.py:0
#, python-format
msgid "New templates"
msgstr "新範本"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_direct
msgid "PDF to Sign"
msgstr "要簽署的 PDF"

#. module: documents_sign
#: model:documents.workflow.rule,name:documents_sign.documents_sign_rule_sign_directly
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Sign"
msgstr "簽名"

#. module: documents_sign
#: model:ir.model.fields.selection,name:documents_sign.selection__documents_workflow_rule__create_model__sign_template_new
msgid "Signature PDF Template"
msgstr "簽名 PDF 範本"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr "簽名請求"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr "簽名範本"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Signed Document Tags"
msgstr "已簽名文件標籤"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Signed Document Workspace"
msgstr "已簽名文件工作區"
