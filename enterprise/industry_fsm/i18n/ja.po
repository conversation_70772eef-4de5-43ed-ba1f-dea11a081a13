# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "タスク数"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.date_deadline, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br><br>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br><br>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.date_deadline, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"></t>\n"
"\n"
"     <t t-out=\"object.partner_id.name or 'customer'\">お客様</t>、<br><br>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        お客様の<t t-out=\"object.name or ''\">ボイラー整備</t>は <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> から <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>の予定です。\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        <t t-out=\"object.name or ''\">ボイラー整備</t>が予定されました。 \n"
"    </t>\n"
"    <br><br>\n"
"    宜しくお願いします。\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "<i class=\"fa fa-check me-1\"/>Sign"
msgstr "<i class=\"fa fa-check me-1\"/>署名"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not partner_id or not is_task_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not partner_id or not is_task_phone_update\"/>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
msgid "<strong>Planned Date:</strong>"
msgstr "<strong>予定日:</strong>"

#. module: industry_fsm
#: model:ir.model.constraint,message:industry_fsm.constraint_project_project_company_id_required_for_fsm_project
msgid "A fsm project must be company restricted"
msgstr "FSMプロジェクトには会社の制約が必要です。"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__active
msgid "Active"
msgstr "有効化"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.mail_activity_plan_menu_config_task
msgid "Activity Plans"
msgstr "活動計画"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_config_activity_type
msgid "Activity Types"
msgstr "活動タイプ"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "管理者"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr "全てのタスク"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__allocated_hours
msgid "Allocated Time"
msgstr "割当時間"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid ""
"Analyze the progress of your tasks and the performance of your workers."
msgstr "タスクの進捗状況や作業員のパフォーマンスを分析します。"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
msgid "Assignees"
msgstr "割り当て先"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr "割り当て日"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_avg
msgid "Average Rating"
msgstr "平均評価"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__dependent_ids
msgid "Block"
msgstr "ブロック"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "プロジェクト別"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "ユーザ別"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_city
msgid "City"
msgstr "市"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Close"
msgstr "閉じる"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "会社"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
msgid "Company"
msgstr "会社"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "設定"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Confirm"
msgstr "確認"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: note that the duration "
"has automatically been rounded to 15 minutes.</i>"
msgstr "タスクに <b>費やした時間</b>を確認します。 <i>ヒント: 所要時間は自動的に15分に丸められています。</i>"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Create"
msgstr "作成"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Create Date"
msgstr "作成日"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "タスクからタイムシートを作成"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.open_create_project_fsm
msgid "Create a Project"
msgstr "プロジェクト作成"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr "カスタムワークテンプレートを作成"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr "タスクから直接新しい見積もりを作成する"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr "タスクから直接新規見積書を作成"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr "プロジェクトを作成して、タスクを整理し、プロジェクトごとに異なるワークフローを定義します。"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_uid
msgid "Created by"
msgstr "作成者"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_date
msgid "Created on"
msgstr "作成日"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
#, python-format
msgid "Customer"
msgstr "顧客"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_customer_ratings
msgid "Customer Ratings"
msgstr "顧客評価"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr "期日までの日数"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "期日"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__description
msgid "Description"
msgstr "説明"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Discard"
msgstr "破棄"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_enabled_conditions_count
msgid "Display Enabled Conditions Count"
msgstr "有効な条件数を表示"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_primary
msgid "Display Mark As Done Primary"
msgstr "プライマリ完了マークを表示"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_secondary
msgid "Display Mark As Done Secondary"
msgstr "セカンダリ完了マークを表示"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "表示名"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_satisfied_conditions_count
msgid "Display Satisfied Conditions Count"
msgstr "条件を満たした数を表示"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr "フィールドサービスモジュールにタスクを表示し、開始日/終了日で計画を立てることができます。"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Do you want to stop the running timers?"
msgstr "タイマーを止めますか？"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "終了日"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "追加見積"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "FSMタスク分析"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_project.py:0
#: code:addons/industry_fsm/models/res_company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#, python-format
msgid "Field Service"
msgstr "フィールドサービス"

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Field Service: Intervention Scheduled"
msgstr "フィールドサービス: 介入が予定されました"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
msgid "Find here your itinerary for today's tasks."
msgstr "本日のタスクの日程表はこちら"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "Find here your upcoming tasks for the next few days."
msgstr "今後の数日間に予定されているタスクはこちらで確認できます。"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future"
msgstr "今後の計画"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future Activities"
msgstr "今後の活動"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Give it a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr " <b>タイトル</b> <i>(例: ボイラー整備、エアコン設置など)</i>を付けて下さい。"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__effective_hours
msgid "Hours Spent"
msgstr "消費時間"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__id
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_task_phone_update
msgid "Is Task Phone Update"
msgstr "タスク電話更新"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr "タスクに使用されたプロダクトを記録し、顧客に時間と部材を請求する"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "最終ステージ更新日"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Late Activities"
msgstr "遅れた活動"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr "タイマーを開始して、タスクに<b>費やされた時間を追跡</b>して下さい。"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will be closed.</i>"
msgstr " <b>タスクを完了とマーク</b>しましょう。 <i>ヒント: マークすると、在庫が自動的に更新され、タスクがクローズされます。</i>"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "最初の<b>タスク</b>を作成しましょう。"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__line_ids
msgid "Line"
msgstr "ライン"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr "フィールドサービスモジュールでタスクを管理する"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr "マップ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "完了とする"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_ir_ui_menu
msgid "Menu"
msgstr "メニュー"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_id
msgid "Milestone"
msgstr "マイルストン"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_milestones
msgid "Milestones"
msgstr "マイルストン"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
msgid "My Tasks"
msgstr "自分のタスク"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
msgid "Navigate To"
msgstr "ナビゲーション"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr "プロジェクトは見つかりませんでした。作成しましょう！"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr "ステージが見つかりません。作成しましょう！"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid "No tasks found. Let's create one!"
msgstr "タスクが見つかりません。新しいタスクを作成しましょう！"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__overtime
msgid "Overtime"
msgstr "時間超過"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__parent_id
msgid "Parent Task"
msgstr "親タスク"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__personal_stage_type_ids
msgid "Personal Stage"
msgstr "個人ステージ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_phone
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_map_view_fsm
msgid "Phone"
msgstr "電話"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "予定済"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Planned Date"
msgstr "予定日"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "計画"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr "プロジェクト別の計画"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr "ユーザ別の計画"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "優先度"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "進捗"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
msgid "Project"
msgstr "プロジェクト"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "プロジェクト"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Properties"
msgstr "属性"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Rating (/5)"
msgstr "評価 (/5)"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr "<b>オンサイト介入の管理</b>をする準備ができましたか？ <i>フィールドサービスをクリックして始めましょう</i>"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
msgid "Remaining Hours"
msgstr "残時間"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours_percentage
msgid "Remaining Hours Percentage"
msgstr "残時間パーセンテージ"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "レポーティング"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Schedule your tasks and assign them to your workers."
msgstr "タスクを予定し、作業者に割当ましょう。"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Search Planning"
msgstr "計画を検索"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Select the <b>customer</b> for your task."
msgstr "タスク用に<b>顧客</b>を選択して下さい。"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_intervention_details
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr "このテンプレートをプロジェクトのステージに設定すると、タスクがステージに到達したときにメールが自動送信されます。"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "管理設定"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign"
msgstr "署名"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "Sign Task"
msgstr "タスクに署名"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid ""
"Some employees are still running their timer for this task. Are you sure you"
" want to continue?"
msgstr "このタスク用にタイマーが稼働している従業員がいます。本当に続けますか？"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
msgid "Stage"
msgstr "ステージ"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "ステージ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_begin
msgid "Start date"
msgstr "開始日"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "State"
msgstr "都道府県・州"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Stop the <b>timer</b> when you are done."
msgstr "完了したら<b>タイマー</b>を止めて下さい。"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr "有効時間数合計"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__tag_ids
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "タグ"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__task_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "タスク"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr "タスク依存関係"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
msgid "Task Done"
msgstr "タスク完了"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "タスクの繰り返し"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_type
msgid "Task Stage"
msgstr "タスク工程"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard
msgid "Task stop running timers confirmation wizard"
msgstr "タスクのタイマーを停止するための確認ウィザード"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard_line
msgid "Task stop running timers confirmation wizard line"
msgstr "タスクのタイマーを停止するための確認ウィザード明細"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_tasks_action_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
msgid "Tasks"
msgstr "タスク"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "タスク分析"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr "時間"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "消費時間"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material Invoicing"
msgstr "タイムアンドマテリアル請求"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr "このタスクに時間が記録されました"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "タスクに費やした時間（サブタスクを含む）"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#, python-format
msgid "Timer started at: %(date)s %(time)s"
msgstr "タイマー開始: %(date)s%(time)s"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/wizard/project_task_create_timesheet.py:0
#, python-format
msgid "Timer stopped at: %(date)s %(time)s"
msgstr "タイマー停止: %(date)s%(time)s"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "タイムシート"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
msgid "To Schedule"
msgstr "スケジュール対象"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Schedule/Assign"
msgstr "予定/割当対象"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid ""
"To get things done, plan activities and use the task status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"業務を完了させるために、活動を計画し、タスクステータスを使用します。<br>\n"
"リアルタイムチャットやEメールを使って、効率的に協力しましょう。"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""
"業務を完了させるために、活動とタスクのステータスを使用します。<br>\n"
"リアルタイムチャットやEメールで効率的に共同作業を行うことができます。"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Today"
msgstr "今日"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Today Activities"
msgstr "本日の活動"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__total_hours_spent
msgid "Total Hours"
msgstr "合計時間"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "作成からクローズまで、タスクの進捗を追跡しましょう。"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "ユーザ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "View Itinerary"
msgstr "日程表を見る"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__wizard_id
msgid "Wizard"
msgstr "ウィザード"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
msgid "Working Days to Assign"
msgstr "割当までの稼働日数"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Working Days to Close"
msgstr "クローズまでの稼働日数"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr "割当までの稼働時間"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr "クローズまでの稼働時間"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Worksheets"
msgstr "ワークシート"

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.date_deadline and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""
"以下が予定されています: {{ object.planned_date_begin and object.date_deadline and 'from"
" the ' + format_datetime(object.planned_date_begin, tz=object.partner_id.tz,"
" lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm_inherited
msgid "e.g. Boiler replacement"
msgstr "例: ボイラー交換"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM?"
msgstr "FSMか？"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.fsm_customer_ratings_server_action
msgid "project.project.fsm"
msgstr "project.project.fsm"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "working days to assign"
msgstr "割当までの稼働日数"
