# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_reports_post_wizard
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-08 11:15+0000\n"
"PO-Revision-Date: 2022-04-08 11:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_reports_post_wizard
#: model:ir.model,name:l10n_be_reports_post_wizard.model_l10n_be_reports_periodic_vat_xml_export
msgid "Belgian Periodic VAT Report Export Wizard"
msgstr ""

#. module: l10n_be_reports_post_wizard
#: model:ir.model,name:l10n_be_reports_post_wizard.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr ""

#. module: l10n_be_reports_post_wizard
#: model:ir.model,name:l10n_be_reports_post_wizard.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_be_reports_post_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_reports_post_wizard.view_account_financial_report_export
msgid "Post Tax Entry"
msgstr ""

#. module: l10n_be_reports_post_wizard
#: code:addons/l10n_be_reports_post_wizard/models/account_move.py:0
#, python-format
msgid "Post a tax report entry"
msgstr ""

#. module: l10n_be_reports_post_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_reports_post_wizard.view_account_financial_report_export
msgid ""
"Posting this entry will generate an XML export; please choose option(s)"
msgstr ""
