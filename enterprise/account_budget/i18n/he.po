# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "יש לכלול את \"תאריך הסיום\" של שורת התקציב בתקופת התקציב"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "יש לכלול את \"תאריך הההתחלה\" של שורת התקציב בתקופת התקציב"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "<span class=\"o_stat_text\">Budget</span>"
msgstr "<span class=\"o_stat_text\">תקציב</span>"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr "משתמשים"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "הישג"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr "סכום שנצבר / הוצא באמת."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "סכום שאתה אמור להרוויח / להוציא בתאריך זה."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""
"סכום שאתה מתכנן להרוויח / להוציא. רשום סכום חיובי אם מדובר בהכנסה וסכום "
"שלילי אם מדובר בעלות."

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "חשבון אנליטי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_plan_id
msgid "Analytic Plan"
msgstr "כלל ברירת מחדל אנליטיים"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "אשר"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "תקציב"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "פריטים תקציביים"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "שורת תקציב"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "שורות תקציב"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__name
msgid "Budget Name"
msgstr "שם התקציב"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "מצב תקציב"

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "סעיף תקציבי"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "סעיפים תקציביים"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "תקציבים"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "ניתוח תקציבים"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "בטל תקציב"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "לחץ כדי ליצור תקציב חדש."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "חברה"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""
"השוואה בין כמות מעשית ותיאורטית. מדד זה אומר לך אם אתה מתחת לתקציב או לא."

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "אשר"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr "מאושר"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Date"
msgstr "תאריך"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "בוצע"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr "טיוטה"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "טיוטת תקציבים"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "תאריך סיום"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr "רשומות ..."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "From"
msgstr "מ"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr "מזהה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "מעל התקציב"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr "שם"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr "לא בוטל"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "תאריך תשלום"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "תקופה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "סכום מתוכנן"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "סכום מתוכנן"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "סכום בפועל"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "סכום בפועל"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "החזר לטיוטה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__state
msgid "Status"
msgstr "סטטוס"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "התקציב חייב לכלול לפחות חשבון אחד."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr "סכום תיאורטי"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Theoretical amount"
msgstr "סכום תיאורטי"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "To"
msgstr "ל"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr "ממתין לאישור מנהל"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "לאשר תקציבים"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_planned_amount
msgid "Total Planned Amount"
msgstr "סה\"כ סכום מתוכנן"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_practical_amount
msgid "Total Practical Amount"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"השתמש בתקציבים כדי להשוות הכנסות ועלויות בפועל לעומת הכנסות ועלויות צפויות"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr "אושרה"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr "עליך להזין לפחות סעיף תקציבי או חשבון אנליטי בשורת תקציב."

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "e.g. Budget 2023: Optimistic"
msgstr ""
