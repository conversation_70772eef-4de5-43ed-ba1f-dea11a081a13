# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_mps
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-26 16:10+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "- Indirect Demand Forecast"
msgstr "- Pronóstico de demanda indirecta"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "ATP"
msgstr "Disponible para prometer"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "Actual"
msgstr "Real"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Actual Demand"
msgstr "Demanda real"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
#, python-format
msgid "Actual Demand %s %s (%s - %s)"
msgstr "Demanda real %s %s (%s - %s)"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Actual Demand Y-1"
msgstr "Demanda real A-1"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Actual Demand Y-2"
msgstr "Demanda real A-2"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__total_qty
#, python-format
msgid "Actual Replenishment"
msgstr "Reabastecimiento real"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
#, python-format
msgid "Actual Replenishment %s %s (%s - %s)"
msgstr "Reabastecimiento real %s%s(%s-%s)"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#: model:ir.actions.act_window,name:mrp_mps.action_mrp_mps_form_view
#, python-format
msgid "Add a Product"
msgstr "Agregar un producto"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "¿Está seguro de que desea eliminar estos registros?"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "¿Está seguro de que desea eliminar este registro?"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Available to Promise"
msgstr "Disponible para prometer"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_bom
msgid "Bill of Material"
msgstr "Lista de materiales"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__bom_id
msgid "Bill of Materials"
msgstr "Lista de materiales"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Close"
msgstr "Cerrar"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__company_id
msgid "Company"
msgstr "Empresa"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__day
msgid "Daily"
msgstr "Diario"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__date
msgid "Date"
msgstr "Fecha"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unidad de medida de uso predeterminado para todas las operaciones de "
"existencias."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,help:mrp_mps.field_res_config_settings__manufacturing_period
msgid ""
"Default value for the time ranges in Master Production Schedule report."
msgstr ""
"Valor predeterminado para los intervalos de tiempo en el reporte del "
"programa maestro de producción."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__forecast_qty
#, python-format
msgid "Demand Forecast"
msgstr "Pronóstico de la demanda"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand
msgid "Display Actual Demand"
msgstr "Mostrar demanda real"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_2
msgid "Display Actual Demand Before Year"
msgstr "Mostrar la demanda real antes del año"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_1
msgid "Display Actual Demand Last Year"
msgstr "Mostrar la demanda real del año pasado"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_replenishment
msgid "Display Actual Replenishment"
msgstr "Mostrar reabastecimiento real"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_available_to_promise
msgid "Display Available to Promise"
msgstr "Mostrar disponible para prometer"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_demand_forecast
msgid "Display Demand Forecast"
msgstr "Mostrar pronóstico de la demanda"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_indirect_demand
msgid "Display Indirect Demand"
msgstr "Mostrar demanda indirecta"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_safety_stock
msgid "Display Safety Stock"
msgstr "Mostrar existencias de seguridad"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_starting_inventory
msgid "Display Starting Inventory"
msgstr "Mostrar inventario inicial"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_to_replenish
msgid "Display To Replenish"
msgstr "Mostrar por reabastecer"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__excessive_replenishment
msgid "Excessive Replenishment"
msgstr "Reabastecimiento excesivo"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#, python-format
msgid "Export"
msgstr "Exportar"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#, python-format
msgid "External ID"
msgstr "ID externo"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_mps_forecast_details
msgid "Forecast Demand Details"
msgstr "Detalles de la demanda pronosticada"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "Forecast Report"
msgstr "Reporte de pronóstico"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "Forecasted"
msgstr "Pronosticado"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "Forecasted Demand"
msgstr "Demanda pronosticada"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Existencias pronosticadas"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_ids
msgid "Forecasted quantity at date"
msgstr "Cantidad pronosticada hasta la fecha"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Proporciona el orden de secuencia al mostrar una lista de productos"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__id
msgid "ID"
msgstr "ID"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Indirect Demand Forecast"
msgstr "Pronóstico de demanda indirecta"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_menu_planning
msgid "MPS"
msgstr "Programa maestro de producción"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Manufacturing Orders"
msgstr "Órdenes de fabricación"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Manufacturing Period"
msgstr "Periodo de fabricación"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#: model:ir.actions.client,name:mrp_mps.action_mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_report_menu
#: model:ir.ui.menu,name:mrp_mps.stock_mrp_mps_report_menu
#, python-format
msgid "Master Production Schedule"
msgstr "Programa maestro de producción"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "Maximum to Replenish"
msgstr "Máximo para reabastecer"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Minimum to Replenish"
msgstr "Mínimo para reabastecer"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__month
msgid "Monthly"
msgstr "Mensual"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__move_ids
msgid "Move"
msgstr "Movimiento"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#, python-format
msgid "No product yet. Add one to start scheduling."
msgstr "Todavía no hay productos. Agregue uno para empezar a programar."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Number of Columns"
msgstr "Número de columnas"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display
msgid "Number of Manufacturing Period Columns"
msgstr "Número de columnas del periodo de fabricación"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display
msgid ""
"Number of columns for the        given period to display in Master "
"Production Schedule"
msgstr ""
"Número de columnas para el        periodo indicado para mostrar en el "
"programa maestro de producción"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__procurement_launched
msgid "Procurement has been run for this forecast"
msgstr "Se ejecutó el aprovisionamiento para este pronóstico"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_template
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_id
msgid "Product"
msgstr "Producto"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_category_id
msgid "Product Category"
msgstr "Categoría del producto"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_product_forecast
msgid "Product Forecast at Date"
msgstr "Pronóstico del producto hasta la fecha"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla del producto"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Product UoM"
msgstr "UdM de producto"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__production_schedule_id
msgid "Production Schedule"
msgstr "Programa de producción"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__warehouse_id
msgid "Production Warehouse"
msgstr "Almacén de producción"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_purchase_order
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__purchase_order_line_ids
msgid "Purchase Order Line"
msgstr "Línea de la orden de compra"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_qty
msgid "Quantity from Incoming Moves"
msgstr "Cantidad desde los movimientos entrantes"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_qty
msgid "Quantity from Manufacturing Order"
msgstr "Cantidad desde la orden de fabricación"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_qty
msgid "Quantity from RFQ"
msgstr "Cantidad desde la solicitud de cotización"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid ""
"Quantity predicted to be available for sale at the end of the period (= to "
"replenish - actual demand)."
msgstr ""
"Cantidad pronosticada para estar disponible para la venta al final del "
"periodo (= por reabastecer - demanda real)."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Receipts"
msgstr "Recibidos"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/main.js:0
#: code:addons/mrp_mps/static/src/components/main.xml:0
#, python-format
msgid "Replenish"
msgstr "Reabastecer"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Replenish State"
msgstr "Estado de reabastecimiento"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty_updated
msgid "Replenish_qty has been manually updated"
msgstr "Replenish_qty se actualizó de forma manual"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too High"
msgstr "Reabastecimiento muy alto"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too Low"
msgstr "Reabastecimiento muy bajo"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Requests for quotation"
msgstr "Solicitudes de cotización"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Rows"
msgstr "Filas"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "Safety Stock Target"
msgstr "Objetivo de existencias de seguridad"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Save"
msgstr "Guardar"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_production_schedule
msgid "Schedule the production of Product in a warehouse"
msgstr "Programe la producción del producto en un almacén"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_purchase_order__date_planned_mps
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_bom__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_product__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_template__schedule_count
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_bom_form_view_inherit_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_normal_form_view_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_template_only_form_view_mps
msgid "Schedules"
msgstr "Horarios"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#, python-format
msgid "Starting Inventory"
msgstr "Inventario inicial"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_stock_rule
msgid "Stock Rule"
msgstr "Regla de inventario"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "Suggested Replenishment"
msgstr "Reabastecimiento sugerido"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Technical field to support filtering by replenish state"
msgstr "Campo técnico para apoyar el filtrado por estado de reabastecimiento"

#. module: mrp_mps
#: model:ir.model.constraint,message:mrp_mps.constraint_mrp_production_schedule_warehouse_product_ref_uniq
msgid "The combination of warehouse and product must be unique!"
msgstr "La combinación de almacén y producto debe ser única"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "The confirmed demand, based on the confirmed sales orders."
msgstr "La demanda confirmada según las órdenes de venta confirmadas."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid ""
"The forecasted demand to fulfill the needs in components of the "
"Manufacturing Orders."
msgstr ""
"La demanda pronosticada para satisfacer las necesidades de los componentes "
"de las órdenes de fabricación."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "The forecasted demand. This value has to be entered manually."
msgstr "La demanda pronosticada. Este valor debe ingresarse de forma manual."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "The forecasted quantity in stock at the beginning of the period."
msgstr "La cantidad pronosticada en existencias al inicio del periodo."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "The forecasted quantity in stock at the end of the period."
msgstr "La cantidad pronosticada en existencias al final del periodo."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#, python-format
msgid ""
"The master schedule translates your sales and demand forecasts into a production and purchase planning for each component.\n"
"                    It ensures everything gets scheduled on time, based on constraints such as: safety stock, production capacity, lead times.\n"
"                    It's the perfect tool to support your S&OP meetings."
msgstr ""
"El programa maestro trasforma sus ventas y pronóstico de demanda en un plan de producción y compra para cada componente.\n"
"                    Garantiza que todo se programe a tiempo según distintas restricciones como: existencias de seguridad, capacidad de producción, plazos.\n"
"                    Es la herramienta perfecta como respaldo durante sus reuniones de planeación de ventas y operaciones."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid ""
"The maximum replenishment you would like to launch for each period in the "
"MPS. Note that if the demand is higher than that amount, the remaining "
"quantity will be transferred to the next period automatically."
msgstr ""
"El reabastecimiento máximo que desea usar en cada periodo en el programa "
"maestro de producción. Tome en cuenta que si la demanda es mayor que esta "
"cifra, la cantidad restante se transferirá al siguiente periodo de forma "
"automática."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid ""
"The quantity being replenished, based on the Requests for Quotation and the "
"Manufacturing Orders."
msgstr ""
"La cantidad a reabastecer según las solicitudes de cotización y las órdenes "
"de fabricación."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid ""
"The quantity to replenish through Purchase Orders or Manufacturing Orders."
msgstr ""
"La cantidad por reabastecer mediante órdenes de compra u órdenes de "
"fabricación."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid ""
"This is the minimum free stock you want to keep for that product at all "
"times."
msgstr ""
"Las existencias mínimas libres que desea conservar para ese producto en todo"
" momento."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Time Range"
msgstr "Periodo de tiempo"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__to_replenish
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
#, python-format
msgid "To Replenish"
msgstr "Por reabastecer"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__under_replenishment
msgid "Under Replenishment"
msgstr "En reabastecimiento"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid ""
"Unless the demand is 0, Odoo will always at least replenish this quantity."
msgstr ""
"A menos que la demanda sea 0, Odoo siempre reabastecerá por lo menos esta "
"cantidad."

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
#, python-format
msgid "Week %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"
msgstr "Semana %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__week
msgid "Weekly"
msgstr "Semanal"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#, python-format
msgid "by"
msgstr "por"
