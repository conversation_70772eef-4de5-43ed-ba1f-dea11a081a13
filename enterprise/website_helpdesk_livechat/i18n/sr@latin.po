# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_helpdesk_livechat
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"Language: sr@latin\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid ""
"\n"
"                    Create a new helpdesk ticket by typing <b>/ticket <i>ticket title</i></b><br>\n"
"                    "
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%(name)s's Ticket"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_done
msgid "Alright, we should have everything we need"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_misc
msgid "Anything else to add?"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.actions.act_window,name:website_helpdesk_livechat.helpdesk_im_livechat_canned_response_action
#: model:ir.ui.menu,name:website_helpdesk_livechat.helpdesk_team_canned_response_menu
msgid "Canned Responses"
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.im_livechat_canned_response_view_search
msgid "Canned Responses Search"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Canned Responses"
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Live Chat Channel"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model.fields.selection,name:website_helpdesk_livechat.selection__chatbot_script_step__step_type__create_ticket
msgid "Create Ticket"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/messaging_service_patch.js:0
#, python-format
msgid "Create a new helpdesk ticket (/ticket ticket title)"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Created a new ticket: %s"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch
msgid "First, what is the nature of your issue?"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script__ticket_count
msgid "Generated Ticket Count"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_yes
msgid "Great, that will make our lives easier."
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script,title:website_helpdesk_livechat.chatbot_script_helpdesk_bot
msgid "Helpdesk Bot"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__helpdesk_team_id
msgid "Helpdesk Team"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_welcome
msgid "Here we go, help is on the way!"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical
msgid "I have a technical issue"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative
msgid "I have an administrative question"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_no
msgid "It's OK, we can also find your contract by other means."
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_email
msgid "Just a last thing, can we please have your email address?"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_no
msgid "No"
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.actions.act_window,help:website_helpdesk_livechat.helpdesk_im_livechat_canned_response_action
msgid "No canned reponses found. Let's create one!"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "No tickets found for <b>%s</b>. <br> Make sure you are using the right format:<br> <b>/search_tickets <i>keyword</i></b>"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_ticket
msgid "OK, I just created a ticket for you. You should receive an email confirmation very soon."
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial
msgid "Please write below the serial number of your equipment."
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_input
msgid "Please write below your customer reference."
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/messaging_service_patch.js:0
#, python-format
msgid "Search helpdesk tickets (/search_tickets keyword)"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Search helpdesk tickets by typing <b>/search_tickets <i>keyword</i></b>"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Something is missing or wrong in command"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_thanks
msgid "Thank you, that will help our engineers see what went wrong."
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.chatbot_script_view_form
msgid "Tickets"
msgstr ""

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Tickets search results for %s: "
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref
msgid ""
"To start with, do you have a customer reference?\n"
"They are written on each invoice you received, next to your name."
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_res_users
msgid "User"
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_issue
msgid "We're all set. Now, what is your issue?"
msgstr ""

#. module: website_helpdesk_livechat
#: model_terms:ir.actions.act_window,help:website_helpdesk_livechat.helpdesk_im_livechat_canned_response_action
msgid "With canned responses, you can type <i>:shortcuts</i> to insert prewritten responses into your messages. These shortcuts are replaced directly in your messages so that you can still make changes before sending them."
msgstr ""

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_yes
msgid "Yes"
msgstr ""
