# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_vehicle
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# Yedigen, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:21+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "<b>50,000 pre-orders</b> already registered."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "<b>A greener lifestyle</b>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "<b>The world is yours</b>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $14,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $19,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "<span class=\"d-inline-block mb-4\"><b>From $25,000</b></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"<span class=\"text-o-color-3\">Our uniquely designed LED headlights are not "
"only gorgeous but powerfully light your way.<br/></span>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #1"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #2"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #3"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #4"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #5"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_product_list
msgid "Brakes #6"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid ""
"Bring the KORAN to life on your smartphone or tablet so you can visualise it"
" for yourself.<br/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Configure"
msgstr "Yapılandır"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Dual Motor"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_image_text
msgid "Electric Driving"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Electrifying <b>Performance</b>."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_call_to_action
msgid "Join the early joiners or request for a trial on track."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran EQ"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran GT"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid "Koran Mini"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Long Range"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_three_columns
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris hendrerit "
"est bibendum sollicitudin vestibulum."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "No compromise"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_references
msgid "Our Partners"
msgstr "İş Ortaklarımız"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Our web store"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid ""
"Performance and design were the key words during the conception of this "
"super car. No compromises were made to keep the pleasure of driving despite "
"those constraints."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_masonry_block_image_texts_image_template
msgid "Smarter <b>Range</b>."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Start the engine"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_text_image
msgid "Take advantage of a free track trial to discover it."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "Take it all. Compliments too."
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_cover
msgid "The New KORAN X"
msgstr ""

#. module: theme_vehicle
#: model:ir.model,name:theme_vehicle.model_theme_utils
msgid "Theme Utils"
msgstr "Tema Araçları"

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features
msgid "Top Speed"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_picture
msgid "View the all-new KORAN in 3D wherever you are<br/>"
msgstr ""

#. module: theme_vehicle
#: model_terms:theme.ir.ui.view,arch:theme_vehicle.s_features_grid
msgid "Your advantages"
msgstr "Avantajlarınız"
