# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_approvals
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_approval_request
msgid "Approval Request"
msgstr "Richiesta approvazione"

#. module: documents_approvals
#: model:documents.facet,name:documents_approvals.documents_approvals
#: model:documents.folder,name:documents_approvals.documents_approvals_folder
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__documents_approvals_settings
msgid "Approvals"
msgstr "Approvazioni"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_tag_ids
msgid "Approvals Tag"
msgstr "Tag approvazioni"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_tag_ids
msgid "Approvals Tags"
msgstr "Tag approvazioni"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_folder_id
msgid "Approvals Workspace"
msgstr "Spazio di lavoro approvazioni"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_folder_id
msgid "Approvals default workspace"
msgstr "Spazio di lavoro predefinito approvazioni"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Centralize files attached to Approvals"
msgstr "Centralizza i file allegati alle approvazioni"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Default Tags"
msgstr "Default Tag"

#. module: documents_approvals
#. odoo-python
#: code:addons/documents_approvals/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:documents_approvals.approval_request_view_form
#, python-format
msgid "Documents"
msgstr "Documenti"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_enabled
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__documents_approvals_settings
msgid "Documents Approvals Settings"
msgstr "Impostazioni documenti approvazioni"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_count
msgid "Documents Count"
msgstr "Numero documenti"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Workspace"
msgstr "Spazio di lavoro"
