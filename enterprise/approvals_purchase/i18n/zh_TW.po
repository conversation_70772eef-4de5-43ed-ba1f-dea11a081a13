# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_category__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr "允許您定義請求獲得批准後要創建的文檔"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_category
msgid "Approval Category"
msgstr "審批類別"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_request
msgid "Approval Request"
msgstr "申請批准"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_category__approval_type
msgid "Approval Type"
msgstr "批核類型"

#. module: approvals_purchase
#: model:approval.category,name:approvals_purchase.approval_category_data_rfq
#: model:ir.model.fields.selection,name:approvals_purchase.selection__approval_category__approval_type__purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
msgid "Create RFQ's"
msgstr "創建詢價"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception occurred: the Approval Request"
msgstr "發生異常：批准請求"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception(s):"
msgstr "異常:"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_product_line.py:0
#, python-format
msgid "Please set a vendor on product(s) %s."
msgstr "請為產品 %s 設定一個供應商。"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_product_line
msgid "Product Line"
msgstr "產品資料行"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__product_id
msgid "Products"
msgstr "產品"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_request__purchase_order_count
msgid "Purchase Order Count"
msgstr "採購訂單計數"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__purchase_order_line_id
msgid "Purchase Order Line"
msgstr "採購訂單項目"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
#, python-format
msgid "Purchase Orders"
msgstr "採購訂單"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__po_uom_qty
msgid "Purchase UoM Quantity"
msgstr "採購計量單位數量"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_product_line__po_uom_qty
msgid ""
"The quantity converted into the UoM used by the product in Purchase Order."
msgstr "轉換為採購訂單中產品使用的計量單位的數量。"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You cannot create an empty purchase request."
msgstr "不可建立空的採購請求。"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You must select a product for each line of requested products."
msgstr "必須為每個請求的產品系列選擇一個產品。"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "cancelled"
msgstr "已取消"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid ""
"has been canceled.\n"
"            Manual actions may be needed."
msgstr ""
"已取消。\n"
"            可能需要手動操作。"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "of"
msgstr "的"
