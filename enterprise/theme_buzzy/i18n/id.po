# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_buzzy
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:21+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_call_to_action
msgid "<b>Start to build</b> your robust activity with these powerful tools"
msgstr "<b>Start to build</b> your robust activity with these powerful tools"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_image_text
msgid "A new open world"
msgstr "A new open world"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid "All data <br/>in your hands"
msgstr "All data <br/>in your hands"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_cover
msgid ""
"Bring the best for your team, start the journey with our amazing tools "
"<br/>and collaborate no matter where you are."
msgstr ""
"Bring the best for your team, start the journey with our amazing tools "
"<br/>and collaborate no matter where you are."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid "Built to <br/>your effigy"
msgstr "Built to <br/>your effigy"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid ""
"Don't miss any data anymore and get all reports you need when and where you "
"want. Analyze them easily with our friendly interface and grow your "
"activity."
msgstr ""
"Don't miss any data anymore and get all reports you need when and where you "
"want. Analyze them easily with our friendly interface and grow your "
"activity."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid "Editable from <br/>A to Z"
msgstr "Editable from <br/>A to Z"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_banner
msgid "Every clever company deserve to work with clever tools."
msgstr "Every clever company deserve to work with clever tools."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_cover
msgid "Faster, lighter and more powerful"
msgstr "Faster, lighter and more powerful"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid ""
"Feel free to edit your content the way like you want. You don't need to know"
" any technical and complex computer language. You will helped by our "
"intuitive tools."
msgstr ""
"Feel free to edit your content the way like you want. You don't need to know"
" any technical and complex computer language. You will helped by our "
"intuitive tools."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_image_text
msgid "Get what you want, where and when you want."
msgstr "Get what you want, where and when you want."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_banner
msgid "Great software <br/>for great people"
msgstr "Great software <br/>for great people"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_text_image
msgid "Improve your efficiency"
msgstr "Improve your efficiency"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_title
msgid "Increase your productivity"
msgstr "Increase your productivity"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_text_image
msgid ""
"It's not that easy to be efficient if we don't have the correct tools than "
"we need. Can you imagine Batman saving Gotham City without his bat-tools?"
msgstr ""
"It's not that easy to be efficient if we don't have the correct tools than "
"we need. Can you imagine Batman saving Gotham City without his bat-tools?"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_call_to_action
msgid "Make your company a better place."
msgstr "Make your company a better place."

#. module: theme_buzzy
#: model:ir.model,name:theme_buzzy.model_theme_utils
msgid "Theme Utils"
msgstr "Utilities Tema"

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_text_image
msgid "There is no magic, it's time to get the tools you deserve."
msgstr "There is no magic, it's time to get the tools you deserve."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_image_text
msgid ""
"Working at the office is good, but working where we want without distraction"
" is better. With our Cloud service accessing to your data has never been so "
"easy."
msgstr ""
"Working at the office is good, but working where we want without distraction"
" is better. With our Cloud service accessing to your data has never been so "
"easy."

#. module: theme_buzzy
#: model_terms:theme.ir.ui.view,arch:theme_buzzy.s_three_columns
msgid ""
"You will always find the ideal choice adapted to your needs. From the basic "
"website based on our attractive templates to the tailor-made website "
"reflecting your company's values."
msgstr ""
"You will always find the ideal choice adapted to your needs. From the basic "
"website based on our attractive templates to the tailor-made website "
"reflecting your company's values."
