# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil <PERSON>do<PERSON>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""
"%(display_name_html)s s otevřeným zůstatkem %(open_amount)s bude tímto "
"pohybem vyrovnán v plné výši."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""
"%(display_name_html)s s otevřeným zůstatkem %(open_amount)s bude ponížen o "
"%(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Rekonciliovat"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr ""
"<b class=\"tip_title\">Tip: Hromadně aktualizujte položky účetního "
"deníku</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Tip: Najděte účetního nebo registrujte účetní "
"firmu</b>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>Podívejte se, jak spravovat faktury zákazníků v <b>Zákazníci / "
"faktury</b> menu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Spustit ručně"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr "<i>Uzamknout účetní záznamy</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr "<i>Uzamknout specifické účetní záznamy</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr "<span class=\"o_stat_text\">1 Bankovní transakce</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr "<span class=\"o_stat_text\">Bankovní výpis</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Najděte účetního</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">Registrujte účetní firmu</span>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Dobrá práce!</b> Prošli jste všemi kroky této prohlídky.</strong>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Account"
msgstr "Účet"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "Šablona účtové osnovy"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Skupiny účtů"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Štítky účtů"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr "Přeúčtovat z účtu"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr "Průvodce automatickým spárováním"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr "Průvodce spárováním"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr "Účet použitý pro časové rozlišení nákladů"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr "Účet pro časové rozlišení výnosů"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Účetnictví"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr "Uzavření účetního období"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr "Datumy účetních uzávěrek"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "Účty"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Accounts with zero balances"
msgstr "Účty s nulovým zůstatkem"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Přidat nový tag"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""
"Po extrakci dat zkontrolujte a ověřte platnost účtu. Pokud nebyl nalezen "
"žádný dodavatel, přidejte ho před validací."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
#, python-format
msgid "All Transactions"
msgstr "Všechny transakce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr "Datum uzamčení pro všechny uživatele"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr "Všechny párovací modely"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr "Povolit částečné"

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Umožňuje definovat fiskální roky více či méně než rok"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Amount"
msgstr "Částka"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Amount Computation"
msgstr "Výpočet částky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Měna množství"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Amount in Currency"
msgstr "Částka v měně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr "Částka v měně společnosti"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr "Zápis přesune %(amount)s z %(from_account)s na %(to_account)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Analytic"
msgstr "Analytika"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analytická distribuce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Hledání analytické distribuce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analytická přesnost"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Anglosaské účetnictví"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Are you sure you want to delete this statement?"
msgstr "Jste si jisti, že chcete smazat tento výpis?"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
#, python-format
msgid "Auto-reconcile"
msgstr "Spárovat automaticky"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
#, python-format
msgid "Automatically Reconciled Entries"
msgstr "Automaticky spárované položky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr "Dostupný párovací model"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
#, python-format
msgid "Back to"
msgstr "Zpět k"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
#, python-format
msgid "Balance"
msgstr "Zůstatek"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Bankovní a hotovostní pohyby"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "Bankovní účet"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Bankovní párování"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Výpis z účtu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr "výpis z bankovního účtu %s.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Položka bankovního výpisu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr "Bankovní výpis.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Nástroj párování s bankou pro jednu položku výpisu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Zrušit"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Změnit datum uzamčení"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr "Zatrhněte, pokud si nejste jistí všemi informacemi protistrany."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "Vyberte řádek, jehož náhled chcete zobrazit."

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Klikněte zde pro vytvoření nového fiskálního roku."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Klikněte zde abyste našli účetního nebo chcete-li využívat účetnictví v Odoo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr "Abyste spustili párování, klikněte na bankovní transakci."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "Společnost"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr "Měna společnosti"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Gratulujeme, vše je hotovo !"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "Připojte svou banku a získejte nejnovější transakce."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Hodnoty protistrany"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr "Vytvořte výpis"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Vytvořit novou skupinu účtů"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Create model"
msgstr "Vytvořit model"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Vytvořte svůj první účet dodavatele.<br/><br/><i>Tip: Pokud žádný nemáte po "
"ruce, použijte náš vzorový účet.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
#, python-format
msgid "Credit"
msgstr "Dal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr "Poslední kontrola služby CRON"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#, python-format
msgid "Currency"
msgstr "Měna"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr "Měna použita pro párování"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Customer/Vendor"
msgstr "Zákazník/Dodavatel"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Date"
msgstr "Datum"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr "Koncové datum pro časové rozlišení nákladů/výnosů"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr "Počáteční datum pro časové rozlišení nákladů/výnosů"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_amount_computation_method__day
msgid "Days"
msgstr "Dny"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
#, python-format
msgid "Debit"
msgstr "Má dáti"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Deferral of %s"
msgstr "Časové rozlišení %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_amount_computation_method
msgid "Deferred Amount Computation Method"
msgstr "Způsob výpočtu časového rozlišení"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
#, python-format
msgid "Deferred Entries"
msgstr "Záznamy časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_journal_id
msgid "Deferred Entries Journal"
msgstr "Deník časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_entry_type
msgid "Deferred Entry Type"
msgstr "Typ časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr "Náklad příštích období"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_journal_id
msgid "Deferred Journal"
msgstr "Deník časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr "Výnos příštích období"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Definujte fiskální roky na více nebo méně než jeden rok"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Delete"
msgstr "Smazat"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr "Vklad"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Přehled"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Discard"
msgstr "Zrušit"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr "Hodnota slevy"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr "Datum slevy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Discuss"
msgstr "Diskuze"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr "Zobrazit Povolení dílčích hodnot"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr "Zobrazit měnu částky přeškrtnutě"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr "Zobrazit zůstatek přeškrtnutě"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Není přístup, přeskočit tato data pro uživatelský emailový přehled"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Edit Statement"
msgstr "Upravit výpis"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "Datum do"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Datum ukončení, zahrnuto ve fiskálním roce."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Každá platba a faktura před tímto datem obdrží stav „Od fakturace“, který "
"skryje všechny související účetní záznamy. Tuto možnost použijte po "
"instalaci účtování, pokud jste dříve používali pouze fakturaci, než "
"importujete všechna svá skutečná účetní data do Odoo."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Exchange Difference: %s"
msgstr "Kurzový rozdíl: %s"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Find Entries to Reconcile Automatically"
msgstr "Automaticky najít zázanamy pro párování"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "Fiskální rok"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Fiskální rok 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Fiskální roky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Poslední den fiskálního roku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Poslední měsíc fiskálního roku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Vlaječka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr "Vynutit cenu včetně daně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr "Od indexu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "Od"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr "Z účtů závazků z obchodního styku"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr "Z účtů pohledávek z obchodního styku"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"V libovolném zobrazení seznamu vyberte více záznamů a seznam bude "
"upravitelný. Pokud aktualizujete buňku, vybrané záznamy se aktualizují "
"najednou. Tuto funkci použijte k aktualizaci více položek vnitřní účetní "
"doklady z hlavní knihy nebo z libovolného zobrazení deníku."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_amount_computation_method__full_months
msgid "Full Months"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries Method"
msgstr "Způsob vytvoření časového rozlišení nákladů"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries Method"
msgstr "Způsob vytvoření časového rozlišení výnosů"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Expense Entries"
msgstr "Vytvořit časové rozlišení nákladů"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Revenue Entries"
msgstr "Vytvořit časové rozlišení výnosů"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "Přejít na fakturaci"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Great! Let’s continue.<br/><br/><i>Tip: If you choose to upload your bill, "
"don’t forget to attach it.</i>"
msgstr ""
"Skvělé! Pokračujme.<br/><br/><i>Tip: Pokud jste zvolili možnost nahrát "
"fakturu, nezapomeňte ji přiložit.</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr "Seskupit daně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr "Má záznamy časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related information at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""
"Pokud je toto pole zaškrtnuto, znamená to že tvůrce záznamu si nebyl jistý "
"všemi informacemi a tento záznam potřebuje být zrevidován."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Příchozí"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Nesprávné datum fiskálního roku: den je mimo rozsah měsíce. Měsíc: %s; Den: "
"%s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "Index"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr "Neplatný"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr "Neplatný výpis"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""
"Neplatné: Bankovní transakce nemůže být zaúčtována neboť je použit dočasný účet.\n"
"Platné: Bankovní transakce může být zaúčtován.\n"
"Spárované: Bankovní transakce již byla zpracována. Nezůstalo nic ke zpracování."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "Datum faktury"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Prahová hodnota přepínače fakturace"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "Je spárován"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_write_off_required
msgid "Is a write-off move required to reconcile"
msgstr "Je pro prárování vyžadováno zaúčtování zůstatku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr "Je vyžadováno přeúčtování"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr "Je pro párování  vyžadováno přeúčtování"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr "Porušuje datum datum uzamčení účtování"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Položky"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "Účetní deník"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "Měna deníku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "Datum uzamčení deníků"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "Účetní záznam"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Účetní položka"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "Účetní položky"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr "Účetní položky k párování"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "Účetní položky, u kterých není nastaveno párovací číslo"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_journal_id
msgid "Journal used for deferred entries"
msgstr "Deníky použité pro časové rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "Účet KPI bankovní hotovostní hodnota"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
#, python-format
msgid "Label"
msgstr "Popis"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Poslední den"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Launch"
msgstr "Spuštění"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""
"Pojďme automatizovat vaše faktury, bankovní transakce a účetní procesy."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Vraťte se zpět na dashboard."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "Podívejme se, jak vypadá účet ve formálním zobrazení."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Řádek"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Položka widgetu pro párování banky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Datum uzamčení pro všechny uživatele"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Datum uzamčení pro uživatele kteří nejsou poradci"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Datumy zamčení účtování"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr "Manažerská uzávěrka"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Ruční operace"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr "Ručně & Seskupeno"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr "Upraveno ručně"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: code:addons/account_accountant/static/src/components/matching_link_widget/matching_link_widget.xml:0
#, python-format
msgid "Match"
msgstr "Párování"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Match Existing Entries"
msgstr "Spárovat existující záznamy"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr "Spárováno"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Matched Transactions"
msgstr "Spárované transakce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr "Pravidla párování umožňující automatické zaúčtování"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr "Použitý způsob výpočtu částky časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Method used to generate deferred expense entries"
msgstr "Použitý způsob výpočtu záznamů časového rozlišení nákladů"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred revenue entries"
msgstr "Použitý způsob výpočtu záznamů časového rozlišení výnosů"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Misc"
msgstr "Různé"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_amount_computation_method__month
msgid "Months"
msgstr "měsíců"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "More"
msgstr "Více"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Přesunout přílohu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr "Řádky dokladu k párování"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Název"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "New"
msgstr "Nové"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "New Transaction"
msgstr "Nová transakce"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "No attachments linked."
msgstr "Nejsou připojeny žádné přílohy."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr "Žádný výpis"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "No transactions matching your filters were found."
msgstr "Nebyly nalezeny transakce vyhovující vašemu filtru."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Žádní uživatelé nemohou upravovat účetní záznamy související s daněmi před a"
" včetně tohoto datumu."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Žádní uživatelé, včetně poradců, nemohou upravovat účty před a včetně tohoto"
" datumu. Použijte jej například pro uzavření fiskálního roku."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr "Nespárováno"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "Poznámky"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Nothing to do here!"
msgstr "Není co dělat!"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
msgid "On bill validation"
msgstr "Při zaúčtování faktury"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On invoice validation"
msgstr "Při zaúčtování faktury"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Změnit data uzamčení mohou pouze správci fakturace!"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""
"Úplné vyrovnání není možné. Pokračujte v několika krocích, pokud chcete "
"provedení úplného vyrovnání."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Pouze uživatelé s rolí \"poradce\" mohou upravovat účty před a včetně tohoto"
" datumu. Použijte jej například pro uzamčení období v rámci současného "
"fiskálního roku."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount"
msgstr "Otevřená částka"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount in Currency"
msgstr "Otevřená částka v měně"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Open balance: %s"
msgstr "Otevřený zůstatek: %s"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Opposite balances one by one"
msgstr "Opačné zůstatky, jeden po druhém"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Original Deferred Entries"
msgstr "Původní záznamy časového rozlišení"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_original_move_ids
msgid "Original Invoices"
msgstr "Původní faktury"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr "Zdrojová daň"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Odchozí"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr "Měna partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "Název partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr "Účet závazků partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr "Výše závazku partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr "Účet pohledávek partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr "Výše pohledávky partnera"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "Partneři"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "Závazky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Payable:"
msgstr "Závazky:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Platba odpovídá"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Stav platby před přepnutím"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Platby"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Odpovídající platby"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr "Vyberte datum uzamčení"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Please set the deferred accounts in the accounting settings."
msgstr "Prosím nastavte účty časového rozlišení v nastavení účetnictví."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Please set the deferred journal in the accounting settings."
msgstr "Prosím nastavte deník časového rozlišení v nastavení účetnictví."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Post Deferred entries in:"
msgstr "Zaúčtovat časové rozlišení v:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr "Předvídat produkt na přijaté faktuře"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr "Předvídat produkt na přijaté faktuře"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Nastavení pro vytváření účetních záznamů během párování faktur a plateb"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification up to the defined date "
"inclusive for all users. As a closed period, all accounting operations are "
"prohibited."
msgstr ""
"Zabrání vytváření nebo úpravě účetních záznamů před definovaným datem pro "
"všechny uživatele. Pro uzavřené období jsou všechny účetní operace zakázány."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation up to the defined date inclusive. Except "
"for Accountant users."
msgstr ""
"Zabrání vytváření účetních pohybů před definovaným datem. S výjimkou "
"uživatelů účetnictví."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification up to the defined date inclusive (Journal "
"Entries involving taxes). The Tax Return Lock Date is automatically set when"
" the corresponding Journal Entry is posted."
msgstr ""
"Zabrání změnám zúčtování daní před definovaným datem (účetní záznamy "
"zahrnující daně). Datum uzamčení zúčtování daní je automaticky nastaveno, "
"jakmile je zaúčtován odpovídající účetní doklad."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Print"
msgstr "Tisk"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "Pohledávky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Receivable:"
msgstr "Pohledávky:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "Spárovat"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr "Spárovat a otevřít"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr "Spárovat účet"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr "Model párování"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Spárované"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr "Párovací model"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Zapsat náklady na prodané zboží do vašich vnitřní účetní doklady"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr "Související nákup(y)"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr "Související prodej(e)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Reset"
msgstr "Resetovat"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "Zbytková hodnota"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr "Zbytková hodnota v měně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr "Příkaz vrácení úkolu"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr "Pravidla párovacího modelu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Save"
msgstr "Uložit"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr "Uložit a zavřít"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Uložit & Nový"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr "Vyhledat účetní položky k párování"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr "Vybrané účetní položky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr "Vybraný párovací model"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Set as Checked"
msgstr "Nastavit jako zkontrolováno"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr "Režim jediné měny"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr "Zdrojová účetní položka"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr "Doklad zdrojové účetní položky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr "Název zdrojové účetní položky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr "Měna zdrojové částky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr "Zůstatek zdroje"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr "Zdroj Dal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr "Zdroj Má dáti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr "Řádek výpisu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr "Detaily transakce řádku výpisu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "Počáteční datum"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Datum zahájení, zahrnuto ve fiskálním roce."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Stát"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Výpis z účtu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "Položka výpisu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr "Navhnutá částka v měně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr "Navržený zůstatek"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr "Navržené Html"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr "Návrhy"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Daň"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr "Měny částky základu daně"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr "Daňové štítky"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "Datum daňového zámku"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "Položka přerozdělení daní"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr "Datum uzamčení zúčtování daně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr "Tax Tag"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Taxes"
msgstr "Daně"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_narration
msgid "Terms and Conditions"
msgstr "Všeobecné obchodní podmínky"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "That's on average"
msgstr "To je v průměru"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""
"Datum, které jste zadali, porušuje datum uzamčení některého z vašich "
"dokladů. Bude změněno na následující datum: %(replacement_date)s"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_payment__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr "Záznamy časového rozlišení vytvořené ´touto fakturou"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "Datum ukončení nesmí být před datem zahájení."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""
"Faktura %(display_name_html)s s otevřeným zůstatkem %(open_amount)s bude "
"plně uhrazena touto transakcí."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""
"Faktura %(display_name_html)s s otevřeným zůstatkem %(open_amount)s bude "
"ponížena o %(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr "Faktury do tohoto data nebudou brány v úvahu jako účetní položky"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_payment__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr "Původní faktura, která vytvořila záznamy časového rozlišení"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""
"Systém se bude snažit určit produkty na řádcích přijatých faktur na základě "
"popisku daného řádku"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""
"Tato bankovní transakce byla automaticky zaúčtovaná s použitím párovacího "
"modelu  '%s'."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "This can only be used on journal items"
msgstr "Toto lze použít pouze na účetní položky"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
#, python-format
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""
"Tento párovací model nelze použít v nástroji pro ruční párování, protože "
"jeho nastavení k tomu není uzpůsobeno"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Tip: hromadně aktualizujte položky účetního deníku"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "Tip: Najděte účetního nebo registrujte účetní firmu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "Na"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "To Check"
msgstr "Ke kontrole"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr "Ke kontrole"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr "Příkaz k provedení"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr "Celkový zůstatek"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "Celkem má dáti"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "Celkem dal"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr "Celkový zůstatek"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr "Celkový zůstatek v měně"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Transakce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr "Měna transakce"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Transaction Details"
msgstr "Detaily transakce"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Transactions"
msgstr "Transakce"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Transfer from %s"
msgstr "Převést z %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Transfer to %s"
msgstr "Převést na %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr "Zkuste párovat řádky výpisu automaticky"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "Nespárované"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
#, python-format
msgid "Unreconciled statements lines"
msgstr "Nespárované řádky výpisu"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "Platný"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "Potvrdit"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "View"
msgstr "Zobrazení"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "View models"
msgstr "Zobrazit modely"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Průvodce"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr "Průvodce - měna společnosti"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Write-Off"
msgstr "Odpis"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Write-Off Entry"
msgstr "Účet odpisu zůstatku"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Nemohou se překrývat dva fiskální roky, opravte datum zahájení a / nebo "
"ukončení fiskálních let."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr "Párovat lze pouze položky s nejvýše dvěma rozdílnými účty: %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""
"Nelze změnit účet položky časového rozlišení v %(move_name)s pokud řádek již"
" byl využit."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr ""
"Nemůžete vytvořit záznam časového rozlišení s počátečním datem, ale bez "
"koncového data."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr ""
"Nemůžete vytvořit záznam časového rozlišení s počátečním datem pozdějším než"
" je koncové datum."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr "Nemůžete vytvořit záznam časového rozlišení pro běžný účetní záznam."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""
"Fakturu, která je přiřazena k dokladu časového rozlišení nelze vrátit do "
"stavu návrh. Místo toho můžete vytvořit dobropis."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "You cannot set a lock date in the future."
msgstr "Nemůžete nastavit datum uzamčení do budoucnosti."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr "Mážná byste chtěli %(btn_start)splně spárovat %(btn_end)s dokument."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""
"Možná byste míto toho chtěli %(btn_start)sčástečně spárovat %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr ""
"Mážná byste chtěli zaznamenat %(btn_start)sčástečnou platbu %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""
"Mužná byste chtěli nastavit fakturu jako %(btn_start)splně uhrazenou "
"%(btn_end)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "You reconciled"
msgstr "Odsouhlasili ste "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr "účetní položka"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr "automatické_vyrovnání"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "např. Bankovní poplatky"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr "předčasná_platba"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr "směnný_rozdíl"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "in"
msgstr "v "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr "likvidita"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr "ručně"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr "nová účetní položka"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "sekund za transakci."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr "daňový_řádek"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "ke kontrole"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "transaction in"
msgstr "transakce za"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "transactions in"
msgstr "transakce v"
