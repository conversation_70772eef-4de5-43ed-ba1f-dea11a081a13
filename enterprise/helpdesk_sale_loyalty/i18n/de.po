# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_loyalty
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/wizard/helpdesk_sale_coupon_generate.py:0
#, python-format
msgid "Compose Email"
msgstr "E-Mail verfassen"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupon"
msgstr "Gutschein"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__program
msgid "Coupon Program"
msgstr "Gutscheinprogramm"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__points_granted
msgid "Coupon Value"
msgstr "Gutscheinwert"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/wizard/helpdesk_sale_coupon_generate.py:0
#, python-format
msgid "Coupon created"
msgstr "Gutschein erstellt"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
#, python-format
msgid "Coupons"
msgstr "Gutscheine"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_ticket__coupons_count
msgid "Coupons count"
msgstr "Anzahl Gutscheine"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.loyalty_card_view_form_helpdesk_sale_loyalty
msgid "Discard"
msgstr "Verwerfen"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: helpdesk_sale_loyalty
#: model:ir.model,name:helpdesk_sale_loyalty.model_helpdesk_sale_coupon_generate
msgid "Generate Sales Coupon from Helpdesk"
msgstr "Verkaufsgutschein aus Kundendienst generieren"

#. module: helpdesk_sale_loyalty
#: model:ir.actions.act_window,name:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_action
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
msgid "Generate a Coupon"
msgstr "Einen Gutschein generieren"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_ticket__coupon_ids
msgid "Generated Coupons"
msgstr "Generierte Gutscheine"

#. module: helpdesk_sale_loyalty
#: model:ir.model,name:helpdesk_sale_loyalty.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Kundendienstticket"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__points_name
msgid "Portal Point Name"
msgstr "Name des Portalpunktes"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.loyalty_card_view_form_helpdesk_sale_loyalty
msgid "Send"
msgstr "Senden"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
msgid "Send by Email"
msgstr "Per E-Mail versenden"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__ticket_id
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__valid_until
msgid "Valid Until"
msgstr "Gültig bis"
