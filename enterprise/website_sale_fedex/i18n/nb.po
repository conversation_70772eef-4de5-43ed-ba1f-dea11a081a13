# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_fedex
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:22+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_fedex
#: model:ir.model.fields,help:website_sale_fedex.field_delivery_carrier__fedex_use_locations
msgid ""
"Allows the ecommerce user to choose a pick-up point as delivery address."
msgstr ""

#. module: website_sale_fedex
#. odoo-python
#: code:addons/website_sale_fedex/models/fedex_locations_request.py:0
#, python-format
msgid "Fedex Server Not Found"
msgstr ""

#. module: website_sale_fedex
#: model:ir.model.fields,field_description:website_sale_fedex.field_delivery_carrier__fedex_locations_radius_unit
msgid "Locations Distance Unit"
msgstr ""

#. module: website_sale_fedex
#: model:ir.model.fields,field_description:website_sale_fedex.field_delivery_carrier__fedex_locations_radius_value
msgid "Locations Radius"
msgstr ""

#. module: website_sale_fedex
#: model:ir.model.fields,help:website_sale_fedex.field_delivery_carrier__fedex_locations_radius_value
msgid "Maximum locations distance radius."
msgstr ""

#. module: website_sale_fedex
#. odoo-python
#: code:addons/website_sale_fedex/models/fedex_locations_request.py:0
#, python-format
msgid "No Fedex pick-up points available for that shipping address"
msgstr ""

#. module: website_sale_fedex
#. odoo-javascript
#: code:addons/website_sale_fedex/static/src/xml/fedex_pickup_locations.xml:0
#, python-format
msgid "Select this location"
msgstr ""

#. module: website_sale_fedex
#: model:ir.model,name:website_sale_fedex.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Fraktmetoder"

#. module: website_sale_fedex
#. odoo-python
#: code:addons/website_sale_fedex/models/fedex_locations_request.py:0
#, python-format
msgid ""
"There was an error retrieving Fedex localisations:\n"
"%s"
msgstr ""

#. module: website_sale_fedex
#: model:ir.model.fields,field_description:website_sale_fedex.field_delivery_carrier__fedex_use_locations
msgid "Use Fedex Locations"
msgstr ""
