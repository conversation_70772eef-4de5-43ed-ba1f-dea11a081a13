# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation_sms
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2023
# <PERSON><PERSON><PERSON> Gk, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_campaign__mailing_sms_count
msgid "# SMS Mailings"
msgstr "# Correus SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr "<i class=\"fa fa-exclamation-circle\"/>Rebotat després "

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> Clicat després"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr "<i class=\"fa fa-hand-pointer-o\"/>No s'ha fet clic a "

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/>Detalls "

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"sms_click\" class=\"fa fa-hand-pointer-o o_ma_text_processed o_add_child_activity text-success\" title=\"Clicked\" role=\"img\" aria-label=\"Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_not_click\" class=\"fa fa-hand-pointer-o o_ma_text_rejected o_add_child_activity text-danger\" title=\"Not Clicked\" role=\"img\" aria-label=\"Not Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_bounce\" class=\"fa fa fa-exclamation-circle o_ma_text_rejected o_add_child_activity text-danger\" title=\"Bounced\" role=\"img\" aria-label=\"Bounced\"/>"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__activity_type
msgid "Activity Type"
msgstr "Tipus d'activitat"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Bounced"
msgstr "Rebotat"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Clicked"
msgstr "Clicat"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
#, python-format
msgid "Exception in SMS Marketing: %s"
msgstr "Excepció en el màrqueting per SMS:%s"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Estadístiques de correu"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr "Tipus de correu"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation_sms.field_sms_composer__marketing_activity_id
msgid "Marketing Activity"
msgstr "Activitat de màrqueting"

#. module: marketing_automation_sms
#: model:ir.actions.act_window,name:marketing_automation_sms.mail_mass_mailing_action_marketing_automation_sms
msgid "Marketing Automation SMS"
msgstr "S'està comercialitzant l'automatització dels SMS"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Campanya de marketing"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_trace
msgid "Marketing Trace"
msgstr "Traça de màrqueting"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Enviament massiu de correus "

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
#, python-format
msgid "Parent activity SMS bounced"
msgstr "S'ha retornat l'SMS de l'activitat pare"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
#, python-format
msgid "Parent activity SMS clicked"
msgstr "S'ha fet clic a l'SMS de l'activitat pare"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__activity_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__mass_mailing_id_mailing_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_category__sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_participant_view_form
msgid "SMS"
msgstr "SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Content"
msgstr "Contingut de l'SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Options"
msgstr "Opcions de SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "SMS Sent"
msgstr "SMS enviats"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_view_form
msgid "SMS Template"
msgstr "Plantilla de SMS"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
#, python-format
msgid "SMS canceled"
msgstr "SMS cancel·lat"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/mailing_trace.py:0
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
#, python-format
msgid "SMS failed"
msgstr "SMS fallit"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_bounce
msgid "SMS: bounced"
msgstr "SMS: retornat"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_click
msgid "SMS: clicked"
msgstr "SMS: premut"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_not_click
msgid "SMS: not clicked"
msgstr "SMS: no clicat"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Assistent d'enviament de SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Sent"
msgstr "Enviat"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
#, python-format
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""
"Per a utilitzar aquesta funció ha de ser un administrador o pertànyer al "
"grup d'automatització de màrqueting."

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr "Categoria de l'activador"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_type
msgid "Trigger Type"
msgstr "Tipus d'activació"
