# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_cobalt
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:23+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_picture
msgid "<em>Our office in Dhaka</em>"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_color_blocks_2
msgid ""
"Are you a startup or a seasoned company looking for a new brand identity?"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_color_blocks_2
msgid "Are you an established brand looking for ongoing creative services?"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_banner
msgid ""
"Building game-changing digital experiences designed to set your brand apart."
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid "Digital Transformation"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_three_columns
msgid "Latest projects"
msgstr "Viimased projektid"

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid ""
"Leader in digital business, we’re helping companies of all sizes to thrive "
"in an ever-changing landscape."
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_banner
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_image_text
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid "Marketing"
msgstr "Turundus"

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_title
msgid "Projects"
msgstr "Projektid"

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_call_to_action
msgid "START NOW"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_image_text
msgid "Strategy synchronising multiple channels"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_banner
msgid "The ultimate marketing platform for brands"
msgstr ""

#. module: theme_cobalt
#: model:ir.model,name:theme_cobalt.model_theme_utils
msgid "Theme Utils"
msgstr "Theme Utils"

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_image_text
msgid ""
"We create a consistent customer experience through a detailed digital "
"strategy built on precise customer research."
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid ""
"We have collected solid experience in building native and cross-platform "
"mobile applications and websites. Check our portfolio from clients around "
"the world."
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid "Web &amp; App Development"
msgstr ""

#. module: theme_cobalt
#: model_terms:theme.ir.ui.view,arch:theme_cobalt.s_text_image
msgid "What we can do, <b>for you</b><br/>"
msgstr ""
