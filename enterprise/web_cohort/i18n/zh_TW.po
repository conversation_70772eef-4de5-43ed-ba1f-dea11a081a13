# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_cohort
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:47+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
#, python-format
msgid "%s - By %s"
msgstr "%s － 由 %s"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
#, python-format
msgid "- By"
msgstr "－ 由"

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "動作窗檢視"

#. module: web_cohort
#. odoo-javascript
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
#, python-format
msgid "Average"
msgstr "平均"

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_base
msgid "Base"
msgstr "計稅基數"

#. module: web_cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_actions_act_window_view__view_mode__cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_ui_view__type__cohort
msgid "Cohort"
msgstr "隊列"

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
#, python-format
msgid "Cohort %(title)s (%(model_name)s)"
msgstr "群組 %(title)s (%(model_name)s)"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
#, python-format
msgid "Cohort view has not defined \"date_start\" attribute."
msgstr "群組檢視畫面未有定義 date_start 屬性。"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
#, python-format
msgid "Cohort view has not defined \"date_stop\" attribute."
msgstr "群組檢視畫面未有定義 date_stop 屬性。"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
#, python-format
msgid "Day"
msgstr "日"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
#, python-format
msgid "Download as Excel file"
msgstr "下載為 Excel 檔案"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
#, python-format
msgid "Main actions"
msgstr "主要行動"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
#, python-format
msgid "Month"
msgstr "月"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
#, python-format
msgid "No data available."
msgstr "無可用資料."

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.js:0
#, python-format
msgid ""
"Period: %(period)s\n"
"%(measure)s: %(count)s"
msgstr ""
"時期：%(period)s\n"
"%(measure)s：%(count)s"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
#, python-format
msgid "The argument %s is not a valid interval. Here are the intervals: %s"
msgstr "引數 %s 並非有效間隔。該些間隔如下：%s"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
#, python-format
msgid "The argument %s is not a valid mode. Here are the modes: %s"
msgstr "引數 %s 並非有效模式。該些模式如下：%s"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
#, python-format
msgid "The argument %s is not a valid timeline. Here are the timelines: %s"
msgstr "引數 %s 並非有效時間線。該些時間線如下：%s"

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_ui_view
msgid "View"
msgstr "檢視"

#. module: web_cohort
#: model:ir.model.fields,field_description:web_cohort.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_cohort.field_ir_ui_view__type
msgid "View Type"
msgstr "檢視類型"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
#, python-format
msgid "Week"
msgstr "星期"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
#, python-format
msgid "Year"
msgstr "年"
