# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# Wil O<PERSON>o, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:47+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. "
"Configure it from the delivery method."
msgstr ""
"Zásilka nemůže mít jako měrnou jednotku KGS/IN nebo LBS/CM. Nakonfigurujte "
"jej ze způsobu doručení."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Access License number is Invalid. Provide a valid number (Length should be "
"0-35 alphanumeric characters)"
msgstr ""
"Číslo přístupové licence je neplatné. Zadejte platné číslo (délka by měla "
"být 0-35 alfanumerických znaků)"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this delivery provider."
msgstr ""
"Přístupové licenční číslo je pro tohoto poskytovatele doručování neplatné."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this provider.Please re-license."
msgstr ""
"Přístupové licenční číslo je pro tohoto poskytovatele neplatné. Znovu "
"proveďte licenci."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is revoked contact UPS to get access."
msgstr ""
"Přístupové licenční číslo není přijato, kontaktujte UPS a získejte přístup."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Authorization system is currently unavailable , try again later."
msgstr ""
"Autorizační systém není momentálně k dispozici, zkuste to znovu později."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr "Platba na můj účet"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr "Možnost financování na dobírku"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr ""
"Zrušení zásilky není v tuto chvíli k dispozici , zkuste to prosím později."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Přepravce"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr "Pokladní šek nebo peněžní příkaz"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr "Centimetry"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr "Šek, pokladní šek nebo peněžní příkaz"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr "Dobírka"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr "Kolkovné zaplaceno"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr "EPL"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"Chyba:\n"
"%s"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr ""
"Překračuje celkový počet povolených kusů na jednu zásilku World Wide "
"Express."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""
"Pokud je zaškrtnuto, uživatelům elektronického obchodu se zobrazí výzva k "
"zadání čísla účtu UPS a na něj budou účtovány poplatky za doručení."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr "Palce"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr "Kilogramy"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr "Formát štítku"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr "Možnosti"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr "PDF"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr "Jednotka velikosti balíku"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr "jednotková hmotnost balíku"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Packages %s do not have a positive shipping weight."
msgstr "Balíčky %s nemají kladnou přepravní hmotnost."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Packages:"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid City in the warehouse address."
msgstr "U adresy skladu uveďte platné město."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in recipient's address."
msgstr "U adresy příjemce uveďte platnou zemi."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in the warehouse address."
msgstr "U adresy skladu uveďte platnou zemi."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid State in the warehouse address."
msgstr "U adresy skladu uveďte platný stát."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Zip in the warehouse address."
msgstr "U adresy skladu uveďte platné PSČ."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the recipient address."
msgstr "U adresy příjemce uveďte platné město."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the shipper's address."
msgstr "U adresy odesílatele uveďte platné město."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid country in the shipper's address."
msgstr "U adresy odesílatele uveďte platnou zemi."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Please provide a valid package type available for service and selected "
"locations."
msgstr "Uveďte prosím platný typ balíčku dostupný pro službu a vybraná místa."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid phone number for the recipient."
msgstr "Uveďte prosím platné telefonní číslo příjemce."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr "Uveďte prosím platné číslo odesílatele/účtu přepravce."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper number/Carrier Account."
msgstr "Uveďte prosím platné číslo odesílatele/účtu přepravce."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper phone number."
msgstr "Uveďte prosím platné telefonní číslo odesílatele."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the recipient address."
msgstr "U adresy příjemce uveďte platný stát."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the shipper's address."
msgstr "U adresy odesílatele uveďte platný stát."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in shipper's address."
msgstr "U adresy odesílatele uveďte platnou ulici."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the recipient address."
msgstr "U adresy příjemce uveďte platnou ulici."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the warehouse address."
msgstr "U adresy skladu poskytněte platnou ulici"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid warehouse Phone Number"
msgstr "Uveďte prosím platné telefonní číslo skladu"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the recipient address."
msgstr "U adresy příjemce uveďte platné PSČ."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the shipper's address."
msgstr "U adresy odesílatele uveďte platné PSČ."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the warehouse address."
msgstr "U adresy skladu uveďte platné PSČ."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zipcode in the recipient address."
msgstr "U adresy příjemce uveďte platné PSČ."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship"
msgstr "Zadejte alespoň jednu položku k odeslání"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Zadejte alespoň jednu položku k odeslání."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the recipient address."
msgstr "U adresy příjemce nastavte platnou zemi."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the warehouse address."
msgstr "U adresy skladu nastavte platnou zemi."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr "Libry"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Access License Number not found in the UPS database"
msgstr "Poskytnuté přístupové licenční číslo nebylo nalezeno v databázi UPS."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Tracking Ref. Number is invalid."
msgstr "Poskytnuté sledovací ref. číslo je neplatné."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Poskytovatel"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr "Adresát"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr "Telefon příjemce musí mít alespoň 10 alfanumerických znaků."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr "Telefonní přípona příjemce nesmí překročit délku 4."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension must contain only numbers."
msgstr "Telefonní přípona příjemce musí obsahovat pouze čísla."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Return label generated"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr "SPL"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr "Sobotní doručení"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr "Odesílatel"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Zásilka číslo %s byla zrušena"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment created into UPS"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr "Telefon odesílatele musí mít alespoň 10 alfanumerických znaků."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper number must contain alphanumeric characters only."
msgstr "Číslo odesílatele musí obsahovat pouze alfanumerické znaky."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr "Telefonní přípona odesílatele nesmí překročit délku 4."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension must contain only numbers."
msgstr "Telefonní přípona odesílatele musí obsahovat pouze čísla."

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Přepravní metody"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr "Typ balení"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr ""
"Uživatelské Id je momentálně uzamčené; zkuste to prosím znovu za 24 hodin."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Adresa vaší společnosti chybí nebo je špatná\n"
"(chybějící pole (nebo více): %s)"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Adresa vašeho skladu chybí nebo je nesprávná.\n"
"(Chybějící pole(e): %s)"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr "Dodání nemůže být provedeno, protože chybí váha vašeho výrobku."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Odhadovanou cenu dopravy nelze vypočítat, protože u následujících produktů chybí hmotnost:\n"
" %s"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The maximum number of user access attempts was exceeded. So please try again"
" later"
msgstr ""
"Byl překročen maximální počet pokusů o přístup uživatele. Zkuste to tedy "
"znovu později"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The name of the customer should be no more than 35 characters."
msgstr "Jméno zákazníka by nemělo mít více než 35 znaků."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Adresa příjemce chybí nebo je nesprávná.\n"
"(Chybějící pole(e): %s)"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The requested service is unavailable between the selected locations."
msgstr "Požadovaná služba není mezi vybranými lokacemi dostupná."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid from the requested warehouse, please choose "
"another service."
msgstr ""
"Vybraná služba z požadovaného skladu je neplatná, vyberte prosím jinou "
"službu."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid to the recipient address, please choose "
"another service."
msgstr ""
"Vybraná služba je pro adresu příjemce neplatná, vyberte prosím jinou službu."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is not possible from your warehouse to the recipient "
"address, please choose another service."
msgstr ""
"Vybraná služba není možná z vašeho skladu na adresu příjemce, vyberte si "
"prosím jinou službu."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The selected service is not valid with the selected packaging."
msgstr "Vybraná služba není platná s vybraným balením."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""
"Tento systém měření není platný pro vybranou zemi. Přepněte z LBS/IN na "
"KGS/CM (nebo naopak). Nakonfigurujte jej ze způsobu doručení"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery "
"method."
msgstr ""
"Tento systém měření není platný pro vybranou zemi. Přepněte z LBS/IN na "
"KGS/CM (nebo naopak). Nakonfigurujte jej ze způsobu doručení."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""
"Tato služba s přidanou hodnotou umožňuje společnosti UPS vyzvednout platbu "
"od zákazníka."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr "Tato služba s přidanou hodnotou vám umožní poslat balíček i v sobotu."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Tracking Numbers:"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr "UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr "Přístupový klíč UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr "Číslo účtu UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr "UPS BE"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr "Konfigurace UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr "Typ souboru štítků UPS"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
msgid "UPS Legacy"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Legacy Package Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr "UPS heslo"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr "UPS sobotní doručení"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "UPS Server Not Found"
msgstr "Server UPS nenalezen"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr "UPS typ služby"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr "Způsoby přepravy UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr "UPS US"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr "UPS Uživatelské jméno"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr "Číslo účtu UPS"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""
"Řádky adresy UPS mohou obsahovat maximálně 35 znaků. Chcete-li se tomuto "
"omezení vyhnout, můžete adresy kontaktů rozdělit do více řádků."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr "Platba cla Ups"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr "Jednotka hmotnosti balíku Ups"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Username/Password is invalid for this delivery provider."
msgstr ""
"Uživatelské jméno/heslo je pro tohoto poskytovatele doručování neplatné."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr "Skladový telefon musí mít alespoň 10 alfanumerických znaků."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must contain only numbers."
msgstr "Skladový telefon musí obsahovat pouze čísla."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr "Telefonní přípona skladu nesmí překročit délku 4."

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/sale.py:0
#, python-format
msgid "You must enter an UPS account number."
msgstr "Musíte zadat číslo účtu UPS."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"
