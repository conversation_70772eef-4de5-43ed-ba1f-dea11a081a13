# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_inter_company_rules
# 
# Translators:
# <PERSON><PERSON><PERSON> <albena_v<PERSON><PERSON>@abv.bg>, 2023
# <PERSON> <<EMAIL>>, 2023
# KeyVillage, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: KeyVillage, 2023\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "%s Invoice: %s"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_generated
msgid "Auto Generated Document"
msgstr "Автоматично създаден документ"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Фирми"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid "Create as"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__not_synchronize
msgid "Do not synchronize"
msgstr ""

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/res_company.py:0
#: code:addons/account_inter_company_rules/models/res_config_settings.py:0
#, python-format
msgid ""
"Generate a bill/invoice when a company confirms an invoice/bill for %s. The "
"new bill/invoice will be created in the first Purchase/Sales Journal of the "
"Journals list view."
msgstr ""

#. module: account_inter_company_rules
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Transactions"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_transaction_message
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_transaction_message
msgid "Intercompany Transaction Message"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move
msgid "Journal Entry"
msgstr "Записи в Дневника"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_line
msgid "Journal Item"
msgstr "Счетоводна Операция"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr ""
"Отговорен потребител за създаването на документи, задействани от правила на "
"интеркомпания."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Rule"
msgstr "Правило"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rules_company_id
msgid "Select Company"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr "Изберете типа, за да настроите правилата за избраната компания."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_invoice_id
msgid "Source Invoice"
msgstr "Източник на фактура"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__invoice_and_refund
msgid "Synchronize invoices/bills"
msgstr ""
