# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from contextlib import contextmanager
from unittest.mock import Mock, patch
from odoo.tests.common import TransactionCase, tagged, Form


@tagged('-standard', 'external')
class TestDeliveryDHL(TransactionCase):

    def setUp(self):
        super(TestDeliveryDHL, self).setUp()

        self.iPadMini = self.env['product.product'].create({
            'name': 'Ipad Mini',
            'weight': 0.01,
        })
        self.large_desk = self.env['product.product'].create({
            'name': 'Large Desk',
            'weight': 0.01,
        })
        self.uom_unit = self.env.ref('uom.product_uom_unit')

        self.your_company = self.env.ref('base.main_partner')
        self.your_company.write({
            'street': "Rue du Laid Burniat 5",
            'street2': "",
            'city': "Ottignies-Louvain-la-Neuve",
            'zip': 1348,
            'state_id': False,
            'country_id': self.env.ref('base.be').id,
            'phone': '******-555-5555',
        })
        self.agrolait = self.env['res.partner'].create({
            'name': 'Agrolait',
            'phone': '(*************',
            'street': "rue des Bourlottes, 9",
            'street2': "",
            'city': "Ramillies",
            'zip': 1367,
            'state_id': False,
            'country_id': self.env.ref('base.be').id,
        })
        self.delta_pc = self.env['res.partner'].create({
            'name': 'Delta PC',
            'phone': '(*************',
            'street': "1515 Main Street",
            'street2': "",
            'city': "Columbia",
            'zip': 29201,
            'state_id': self.env.ref('base.state_us_41').id,
            'country_id': self.env.ref('base.us').id,
        })
        self.stock_location = self.env.ref('stock.stock_location_stock')
        self.customer_location = self.env.ref('stock.stock_location_customers')

        self.delivery_carrier_dhl_eu_dom = self.env.ref('delivery_dhl.delivery_carrier_dhl_eu_dom', raise_if_not_found=False)
        if not self.delivery_carrier_dhl_eu_dom:
            product_dhl = self.env['product.product'].create({
                "name": 'DHL EU',
                "default_code": 'Delivery_014',
                "type": 'service',
                "categ_id": self.env.ref('delivery.product_category_deliveries').id,
                "sale_ok": False,
                "purchase_ok": False,
                "list_price": 0.0,
                "invoice_policy": 'order',
            })
            self.delivery_carrier_dhl_eu_dom = self.env['delivery.carrier'].create({
                "name": 'DHL EU',
                "product_id": product_dhl.id,
                "delivery_type": 'dhl',
                "dhl_product_code": 'N',
                "dhl_SiteID": 'v62_X4e7G4Ww0y',
                "dhl_password": '7UvboGP0tD',
                "dhl_account_number": '*********',
                "dhl_default_package_type_id": self.env.ref('delivery_dhl.dhl_packaging_BOX').id,
            })
        self.delivery_carrier_dhl_eu_intl = self.env.ref('delivery_dhl.delivery_carrier_dhl_eu_intl', raise_if_not_found=False)
        if not self.delivery_carrier_dhl_eu_intl:
            product_dhl = self.env['product.product'].create({
                "name": 'DHL EU International',
                "default_code": 'Delivery_015',
                "type": 'service',
                "categ_id": self.env.ref('delivery.product_category_deliveries').id,
                "sale_ok": False,
                "purchase_ok": False,
                "list_price": 0.0,
                "invoice_policy": 'order',
            })
            self.delivery_carrier_dhl_eu_intl = self.env['delivery.carrier'].create({
                "name": 'DHL EU International',
                "product_id": product_dhl.id,
                "delivery_type": 'dhl',
                "dhl_product_code": 'D',
                "dhl_SiteID": 'v62_X4e7G4Ww0y',
                "dhl_password": '7UvboGP0tD',
                "dhl_account_number": '*********',
                "dhl_default_package_type_id": self.env.ref('delivery_dhl.dhl_packaging_BOX').id,
            })

    def wiz_put_in_pack(self, picking):
        """ Helper to use the 'choose.delivery.package' wizard
        in order to call the 'action_put_in_pack' method.
        """
        wiz_action = picking.action_put_in_pack()
        self.assertEqual(wiz_action['res_model'], 'choose.delivery.package', 'Wrong wizard returned')
        wiz = Form(self.env[wiz_action['res_model']].with_context(wiz_action['context']).create({
            'delivery_package_type_id': picking.carrier_id.dhl_default_package_type_id.id
        }))
        choose_delivery_carrier = wiz.save()
        choose_delivery_carrier.action_put_in_pack()

    def test_01_dhl_basic_be_domestic_flow(self):
        SaleOrder = self.env['sale.order']

        sol_vals = {'product_id': self.iPadMini.id,
                    'name': "[A1232] Large Cabinet",
                    'product_uom': self.uom_unit.id,
                    'product_uom_qty': 1.0,
                    'price_unit': self.iPadMini.lst_price}

        so_vals = {'partner_id': self.agrolait.id,
                   'order_line': [(0, None, sol_vals)]}

        sale_order = SaleOrder.create(so_vals)
        # I add free delivery cost in Sales order
        delivery_wizard = Form(self.env['choose.delivery.carrier'].with_context({
            'default_order_id': sale_order.id,
            'default_carrier_id': self.delivery_carrier_dhl_eu_dom.id
        }))
        choose_delivery_carrier = delivery_wizard.save()
        choose_delivery_carrier.update_price()
        # DHL test server will return 0.0...
        # self.assertGreater(sale_order.delivery_price, 0.0, "DHL delivery cost for this SO has not been correctly estimated.")
        choose_delivery_carrier.button_confirm()

        sale_order.action_confirm()
        self.assertEqual(len(sale_order.picking_ids), 1, "The Sales Order did not generate a picking.")

        picking = sale_order.picking_ids[0]
        picking.move_ids[0].picked = True
        self.assertEqual(picking.carrier_id.id, sale_order.carrier_id.id, "Carrier is not the same on Picking and on SO.")

        picking.move_ids[0].quantity = 1.0
        self.assertGreater(picking.shipping_weight, 0.0, "Picking weight should be positive.")

        picking._action_done()
        self.assertIsNot(picking.carrier_tracking_ref, False, "DHL did not return any tracking number")
        # self.assertGreater(picking.carrier_price, 0.0, "DHL carrying price is probably incorrect")

        picking.cancel_shipment()
        self.assertFalse(picking.carrier_tracking_ref, "Carrier Tracking code has not been properly deleted")
        self.assertEqual(picking.carrier_price, 0.0, "Carrier price has not been properly deleted")

    def test_02_dhl_basic_international_flow(self):
        SaleOrder = self.env['sale.order']

        sol_vals = {'product_id': self.iPadMini.id,
                    'name': "[A1232] Large Cabinet",
                    'product_uom': self.uom_unit.id,
                    'product_uom_qty': 1.0,
                    'price_unit': self.iPadMini.lst_price}

        so_vals = {'partner_id': self.delta_pc.id,
                   'carrier_id': self.delivery_carrier_dhl_eu_intl.id,
                   'order_line': [(0, None, sol_vals)]}

        sale_order = SaleOrder.create(so_vals)
        # I add free delivery cost in Sales order
        delivery_wizard = Form(self.env['choose.delivery.carrier'].with_context({
            'default_order_id': sale_order.id,
            'default_carrier_id': self.delivery_carrier_dhl_eu_intl.id
        }))
        choose_delivery_carrier = delivery_wizard.save()
        choose_delivery_carrier.update_price()
        # DHL test server will return 0.0...
        # self.assertGreater(sale_order.delivery_price, 0.0, "DHL delivery cost for this SO has not been correctly estimated.")
        choose_delivery_carrier.button_confirm()

        sale_order.action_confirm()
        self.assertEqual(len(sale_order.picking_ids), 1, "The Sales Order did not generate a picking.")

        picking = sale_order.picking_ids[0]
        self.assertEqual(picking.carrier_id.id, sale_order.carrier_id.id, "Carrier is not the same on Picking and on SO.")

        picking.move_ids[0].quantity = 1.0
        picking.move_ids[0].picked = True
        self.assertGreater(picking.shipping_weight, 0.0, "Picking weight should be positive.")

        picking._action_done()
        self.assertIsNot(picking.carrier_tracking_ref, False, "DHL did not return any tracking number")
        # self.assertGreater(picking.carrier_price, 0.0, "DHL carrying price is probably incorrect")

        picking.cancel_shipment()
        self.assertFalse(picking.carrier_tracking_ref, "Carrier Tracking code has not been properly deleted")
        self.assertEqual(picking.carrier_price, 0.0, "Carrier price has not been properly deleted")

    def test_03_dhl_multipackage_international_flow(self):
        SaleOrder = self.env['sale.order']

        sol_1_vals = {'product_id': self.iPadMini.id,
                      'name': "[A1232] Large Cabinet",
                      'product_uom': self.uom_unit.id,
                      'product_uom_qty': 1.0,
                      'price_unit': self.iPadMini.lst_price}
        sol_2_vals = {'product_id': self.large_desk.id,
                      'name': "[A1090] Large Desk",
                      'product_uom': self.uom_unit.id,
                      'product_uom_qty': 1.0,
                      'price_unit': self.large_desk.lst_price}

        so_vals = {'partner_id': self.delta_pc.id,
                   'carrier_id': self.delivery_carrier_dhl_eu_intl.id,
                   'order_line': [(0, None, sol_1_vals), (0, None, sol_2_vals)]}

        sale_order = SaleOrder.create(so_vals)
        # I add free delivery cost in Sales order
        delivery_wizard = Form(self.env['choose.delivery.carrier'].with_context({
            'default_order_id': sale_order.id,
            'default_carrier_id': self.delivery_carrier_dhl_eu_intl.id
        }))
        choose_delivery_carrier = delivery_wizard.save()
        choose_delivery_carrier.update_price()
        # DHL test server will return 0.0...
        # self.assertGreater(sale_order.delivery_price, 0.0, "DHL delivery cost for this SO has not been correctly estimated.")
        choose_delivery_carrier.button_confirm()

        sale_order.action_confirm()
        self.assertEqual(len(sale_order.picking_ids), 1, "The Sales Order did not generate a picking.")

        picking = sale_order.picking_ids[0]
        self.assertEqual(picking.carrier_id.id, sale_order.carrier_id.id, "Carrier is not the same on Picking and on SO.")

        move0 = picking.move_ids[0]
        move0.quantity = 1.0
        move0.picked = True

        self.wiz_put_in_pack(picking)
        move1 = picking.move_ids[1]
        move1.quantity = 1.0
        move1.picked = True
        self.wiz_put_in_pack(picking)
        self.assertEqual(len(picking.move_line_ids.mapped('result_package_id')), 2, "2 Packages should have been created at this point")
        self.assertGreater(picking.shipping_weight, 0.0, "Picking weight should be positive.")

        picking._action_done()
        self.assertIsNot(picking.carrier_tracking_ref, False, "DHL did not return any tracking number")
        # self.assertGreater(picking.carrier_price, 0.0, "DHL carrying price is probably incorrect")

        picking.cancel_shipment()
        self.assertFalse(picking.carrier_tracking_ref, "Carrier Tracking code has not been properly deleted")
        self.assertEqual(picking.carrier_price, 0.0, "Carrier price has not been properly deleted")

    def test_04_dhl_flow_from_delivery_order(self):
        StockPicking = self.env['stock.picking']

        order1_vals = {
                    'product_id': self.iPadMini.id,
                    'name': "[A1232] iPad Mini",
                    'product_uom': self.uom_unit.id,
                    'product_uom_qty': 1.0,
                    'location_id': self.stock_location.id,
                    'location_dest_id': self.customer_location.id}

        do_vals = {'partner_id': self.delta_pc.id,
                   'carrier_id': self.delivery_carrier_dhl_eu_intl.id,
                   'location_id': self.stock_location.id,
                   'location_dest_id': self.customer_location.id,
                   'picking_type_id': self.env.ref('stock.picking_type_out').id,
                   'state': 'draft',
                   'move_ids_without_package': [(0, None, order1_vals)]}

        delivery_order = StockPicking.create(do_vals)
        self.assertEqual(delivery_order.state, 'draft', 'Shipment state should be draft.')

        delivery_order.action_confirm()
        self.assertEqual(delivery_order.state, 'assigned', 'Shipment state should be ready(assigned).')
        delivery_order.move_ids_without_package.quantity = 1.0

        delivery_order.button_validate()
        self.assertEqual(delivery_order.state, 'done', 'Shipment state should be done.')

@tagged('standard', '-external')
class TestMockedDeliveryDHL(TestDeliveryDHL):

    @contextmanager
    def patch_dhl_requests(self):
        """ Mock context for requests to the DHL API. """

        class MockedSession:
            def __init__(self, *args, **kwargs):
                self.headers = dict()

            def mount(self, *args, **kwargs):
                return None

            def close(self, *args, **kwargs):
                return None

            def post(self, *args, **kwargs):
                response = Mock()
                if b'<ns0:DCTRequest' in kwargs.get('data'):
                    response.content = '<res:DCTResponse xmlns:res="http://www.dhl.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.dhl.com DCT-Response.xsd"><GetQuoteResponse><Response><ServiceHeader><MessageTime>2022-09-19T12:28:10.687+00:00</MessageTime><MessageReference>ref:2022-09-19T12:28:10.041655</MessageReference><SiteID>XXXXXXXXXXXXXX</SiteID></ServiceHeader></Response><BkgDetails><QtdShp><OriginServiceArea><FacilityCode>DG1</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></OriginServiceArea><DestinationServiceArea><FacilityCode>LGG</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></DestinationServiceArea><GlobalProductCode>N</GlobalProductCode><LocalProductCode>L</LocalProductCode><ProductShortName>EXPRESS DOMESTIC</ProductShortName><LocalProductName>EXPRESS DOMESTIC</LocalProductName><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>N</POfferedCustAgreement><TransInd>Y</TransInd><PickupDate>2022-09-19</PickupDate><PickupCutoffTime>PT19H30M</PickupCutoffTime><BookingTime>PT18H</BookingTime><CurrencyCode>EUR</CurrencyCode><ExchangeRate>0.000</ExchangeRate><WeightCharge>37.890</WeightCharge><WeightChargeTax>6.580</WeightChargeTax><TotalTransitDays>1</TotalTransitDays><PickupPostalLocAddDays>0</PickupPostalLocAddDays><DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays><PickupNonDHLCourierCode></PickupNonDHLCourierCode><DeliveryNonDHLCourierCode></DeliveryNonDHLCourierCode><DeliveryDate><DeliveryType>QDDF</DeliveryType><DlvyDateTime>2022-09-20 11:59:00</DlvyDateTime><DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset></DeliveryDate><DeliveryTime>PT23H59M</DeliveryTime><DimensionalWeight>19.008</DimensionalWeight><WeightUnit>KG</WeightUnit><PickupDayOfWeekNum>1</PickupDayOfWeekNum><DestinationDayOfWeekNum>2</DestinationDayOfWeekNum><QuotedWeight>19.500</QuotedWeight><QuotedWeightUOM>KG</QuotedWeightUOM><QtdShpExChrg><SpecialServiceType>FF</SpecialServiceType><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>12.890</ChargeValue><ChargeTaxAmount>2.240</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><TaxAmount>2.240</TaxAmount><BaseAmount>10.650</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>12.890</ChargeValue><ChargeTaxAmount>2.240</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><TaxAmount>2.240</TaxAmount><BaseAmount>10.650</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><PricingDate>2022-09-19</PricingDate><ShippingCharge>50.780</ShippingCharge><TotalTaxAmount>8.820</TotalTaxAmount><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><WeightCharge>37.890</WeightCharge><TotalAmount>50.780</TotalAmount><TotalTaxAmount>8.820</TotalTaxAmount><WeightChargeTax>6.580</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>6.580</WeightChargeTax><BaseAmt>31.310</BaseAmt></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>6.580</WeightChargeTax><BaseAmt>31.310</BaseAmt></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><PickupWindowEarliestTime>10:00:00</PickupWindowEarliestTime><PickupWindowLatestTime>19:30:00</PickupWindowLatestTime><BookingCutoffOffset>PT1H30M</BookingCutoffOffset></QtdShp><QtdShp><OriginServiceArea><FacilityCode>DG1</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></OriginServiceArea><DestinationServiceArea><FacilityCode>LGG</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></DestinationServiceArea><GlobalProductCode>7</GlobalProductCode><LocalProductCode>I</LocalProductCode><ProductShortName>EXPRESS EASY</ProductShortName><LocalProductName>EXPRESS EASY DOC</LocalProductName><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><PickupDate>2022-09-19</PickupDate><PickupCutoffTime>PT19H30M</PickupCutoffTime><BookingTime>PT18H</BookingTime><CurrencyCode>EUR</CurrencyCode><ExchangeRate>0.000</ExchangeRate><WeightCharge>43.000</WeightCharge><WeightChargeTax>7.460</WeightChargeTax><TotalTransitDays>1</TotalTransitDays><PickupPostalLocAddDays>0</PickupPostalLocAddDays><DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays><PickupNonDHLCourierCode></PickupNonDHLCourierCode><DeliveryNonDHLCourierCode></DeliveryNonDHLCourierCode><DeliveryDate><DeliveryType>QDDF</DeliveryType><DlvyDateTime>2022-09-20 11:59:00</DlvyDateTime><DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset></DeliveryDate><DeliveryTime>PT23H59M</DeliveryTime><DimensionalWeight>19.008</DimensionalWeight><WeightUnit>KG</WeightUnit><PickupDayOfWeekNum>1</PickupDayOfWeekNum><DestinationDayOfWeekNum>2</DestinationDayOfWeekNum><QuotedWeight>19.500</QuotedWeight><QuotedWeightUOM>KG</QuotedWeightUOM><PricingDate>2022-09-19</PricingDate><ShippingCharge>43.000</ShippingCharge><TotalTaxAmount>7.460</TotalTaxAmount><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><WeightCharge>43.000</WeightCharge><TotalAmount>43.000</TotalAmount><TotalTaxAmount>7.460</TotalTaxAmount><WeightChargeTax>7.460</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>7.460</WeightChargeTax><BaseAmt>35.540</BaseAmt></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>7.460</WeightChargeTax><BaseAmt>35.540</BaseAmt></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><PickupWindowEarliestTime>10:00:00</PickupWindowEarliestTime><PickupWindowLatestTime>19:30:00</PickupWindowLatestTime><BookingCutoffOffset>PT1H30M</BookingCutoffOffset></QtdShp><QtdShp><OriginServiceArea><FacilityCode>DG1</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></OriginServiceArea><DestinationServiceArea><FacilityCode>LGG</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></DestinationServiceArea><GlobalProductCode>C</GlobalProductCode><LocalProductCode>O</LocalProductCode><ProductShortName>MEDICAL EXPRESS</ProductShortName><LocalProductName>MEDICAL EXPRESS</LocalProductName><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><PickupDate>2022-09-19</PickupDate><PickupCutoffTime>PT19H30M</PickupCutoffTime><BookingTime>PT18H</BookingTime><CurrencyCode>EUR</CurrencyCode><ExchangeRate>0.000</ExchangeRate><WeightCharge>51.380</WeightCharge><WeightChargeTax>8.920</WeightChargeTax><TotalTransitDays>1</TotalTransitDays><PickupPostalLocAddDays>0</PickupPostalLocAddDays><DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays><PickupNonDHLCourierCode></PickupNonDHLCourierCode><DeliveryNonDHLCourierCode></DeliveryNonDHLCourierCode><DeliveryDate><DeliveryType>QDDF</DeliveryType><DlvyDateTime>2022-09-20 11:59:00</DlvyDateTime><DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset></DeliveryDate><DeliveryTime>PT23H59M</DeliveryTime><DimensionalWeight>19.008</DimensionalWeight><WeightUnit>KG</WeightUnit><PickupDayOfWeekNum>1</PickupDayOfWeekNum><DestinationDayOfWeekNum>2</DestinationDayOfWeekNum><QuotedWeight>19.500</QuotedWeight><QuotedWeightUOM>KG</QuotedWeightUOM><QtdShpExChrg><SpecialServiceType>FF</SpecialServiceType><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>17.470</ChargeValue><ChargeTaxAmount>3.030</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><TaxAmount>3.030</TaxAmount><BaseAmount>14.440</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>17.470</ChargeValue><ChargeTaxAmount>3.030</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><TaxAmount>3.030</TaxAmount><BaseAmount>14.440</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><PricingDate>2022-09-19</PricingDate><ShippingCharge>68.850</ShippingCharge><TotalTaxAmount>11.950</TotalTaxAmount><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><WeightCharge>51.380</WeightCharge><TotalAmount>68.850</TotalAmount><TotalTaxAmount>11.950</TotalTaxAmount><WeightChargeTax>8.920</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>8.920</WeightChargeTax><BaseAmt>42.460</BaseAmt></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>8.920</WeightChargeTax><BaseAmt>42.460</BaseAmt></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>21.000</TaxTypeRate><TaxTypeCode>EU_VAT</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><PickupWindowEarliestTime>10:00:00</PickupWindowEarliestTime><PickupWindowLatestTime>19:30:00</PickupWindowLatestTime><BookingCutoffOffset>PT1H30M</BookingCutoffOffset></QtdShp><QtdShp><OriginServiceArea><FacilityCode>DG1</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></OriginServiceArea><DestinationServiceArea><FacilityCode>JC1</FacilityCode><ServiceAreaCode>JCC</ServiceAreaCode></DestinationServiceArea><GlobalProductCode>B</GlobalProductCode><LocalProductCode>B</LocalProductCode><ProductShortName>EXPRESS BREAKBULK</ProductShortName><LocalProductName>EXPRESS BREAKBULK</LocalProductName><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><PickupDate>2022-09-19</PickupDate><PickupCutoffTime>PT19H30M</PickupCutoffTime><BookingTime>PT18H</BookingTime><ExchangeRate>0.000</ExchangeRate><WeightCharge>0</WeightCharge><WeightChargeTax>0.000</WeightChargeTax><TotalTransitDays>3</TotalTransitDays><PickupPostalLocAddDays>0</PickupPostalLocAddDays><DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays><PickupNonDHLCourierCode></PickupNonDHLCourierCode><DeliveryNonDHLCourierCode></DeliveryNonDHLCourierCode><DeliveryDate><DeliveryType>QDDF</DeliveryType><DlvyDateTime>2022-09-22 11:59:00</DlvyDateTime><DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset></DeliveryDate><DeliveryTime>PT23H59M</DeliveryTime><DimensionalWeight>19.008</DimensionalWeight><WeightUnit>KG</WeightUnit><PickupDayOfWeekNum>1</PickupDayOfWeekNum><DestinationDayOfWeekNum>4</DestinationDayOfWeekNum><QuotedWeight>19.500</QuotedWeight><QuotedWeightUOM>KG</QuotedWeightUOM><QtdShpExChrg><SpecialServiceType>CR</SpecialServiceType><LocalServiceType>CR</LocalServiceType><GlobalServiceName>EMERGENCY SITUATION</GlobalServiceName><LocalServiceTypeName>EMERGENCY SITUATION</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>5.850</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>5.850</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>5.850</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>5.850</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><QtdShpExChrg><SpecialServiceType>FF</SpecialServiceType><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>1.990</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>1.990</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>1.990</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>1.990</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><PricingDate>2022-09-19</PricingDate><ShippingCharge>0.000</ShippingCharge><TotalTaxAmount>0.000</TotalTaxAmount><QtdSInAdCur><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><WeightCharge>0</WeightCharge><TotalAmount>0.000</TotalAmount><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax></QtdSInAdCur><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode><WeightCharge>0</WeightCharge><TotalAmount>0.000</TotalAmount><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax></QtdSInAdCur><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode><WeightCharge>0</WeightCharge><TotalAmount>0.000</TotalAmount><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax></QtdSInAdCur><PickupWindowEarliestTime>10:00:00</PickupWindowEarliestTime><PickupWindowLatestTime>19:30:00</PickupWindowLatestTime><BookingCutoffOffset>PT1H30M</BookingCutoffOffset></QtdShp><QtdShp><OriginServiceArea><FacilityCode>DG1</FacilityCode><ServiceAreaCode>BRU</ServiceAreaCode></OriginServiceArea><DestinationServiceArea><FacilityCode>JC1</FacilityCode><ServiceAreaCode>JCC</ServiceAreaCode></DestinationServiceArea><GlobalProductCode>D</GlobalProductCode><LocalProductCode>D</LocalProductCode><ProductShortName>EXPRESS WORLDWIDE</ProductShortName><LocalProductName>EXPRESS WORLDWIDE DOC</LocalProductName><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>N</POfferedCustAgreement><TransInd>Y</TransInd><PickupDate>2022-09-19</PickupDate><PickupCutoffTime>PT19H30M</PickupCutoffTime><BookingTime>PT18H</BookingTime><CurrencyCode>EUR</CurrencyCode><ExchangeRate>0.000</ExchangeRate><WeightCharge>260.680</WeightCharge><WeightChargeTax>0.000</WeightChargeTax><TotalTransitDays>3</TotalTransitDays><PickupPostalLocAddDays>0</PickupPostalLocAddDays><DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays><PickupNonDHLCourierCode></PickupNonDHLCourierCode><DeliveryNonDHLCourierCode></DeliveryNonDHLCourierCode><DeliveryDate><DeliveryType>QDDF</DeliveryType><DlvyDateTime>2022-09-22 11:59:00</DlvyDateTime><DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset></DeliveryDate><DeliveryTime>PT23H59M</DeliveryTime><DimensionalWeight>19.008</DimensionalWeight><WeightUnit>KG</WeightUnit><PickupDayOfWeekNum>1</PickupDayOfWeekNum><DestinationDayOfWeekNum>4</DestinationDayOfWeekNum><QuotedWeight>19.500</QuotedWeight><QuotedWeightUOM>KG</QuotedWeightUOM><QtdShpExChrg><SpecialServiceType>CR</SpecialServiceType><LocalServiceType>CR</LocalServiceType><GlobalServiceName>EMERGENCY SITUATION</GlobalServiceName><LocalServiceTypeName>EMERGENCY SITUATION</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>5.850</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>5.850</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>5.850</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>5.850</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><QtdShpExChrg><SpecialServiceType>FF</SpecialServiceType><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><CurrencyCode>EUR</CurrencyCode><ChargeValue>90.620</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>90.620</BaseAmount></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet><QtdSExtrChrgInAdCur><ChargeValue>90.620</ChargeValue><ChargeTaxAmount>0.000</ChargeTaxAmount><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><TaxAmount>0.000</TaxAmount><BaseAmount>90.620</BaseAmount></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur><QtdSExtrChrgInAdCur><ChargeTaxAmountDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode></ChargeTaxAmountDet></QtdSExtrChrgInAdCur></QtdShpExChrg><PricingDate>2022-09-19</PricingDate><ShippingCharge>357.150</ShippingCharge><TotalTaxAmount>0.000</TotalTaxAmount><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><WeightCharge>260.680</WeightCharge><TotalAmount>357.150</TotalAmount><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax><BaseAmt>260.680</BaseAmt></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><QtdSInAdCur><TotalTaxAmount>0.000</TotalTaxAmount><WeightChargeTax>0.000</WeightChargeTax><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet></QtdSInAdCur><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax><BaseAmt>260.680</BaseAmt></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><WeightChargeTaxDet><TaxTypeRate>0.000</TaxTypeRate><TaxTypeCode>All Bu</TaxTypeCode><WeightChargeTax>0.000</WeightChargeTax></WeightChargeTaxDet><PickupWindowEarliestTime>10:00:00</PickupWindowEarliestTime><PickupWindowLatestTime>19:30:00</PickupWindowLatestTime><BookingCutoffOffset>PT1H30M</BookingCutoffOffset></QtdShp></BkgDetails><Srvs><Srv><GlobalProductCode>N</GlobalProductCode><MrkSrv><LocalProductCode>L</LocalProductCode><ProductShortName>EXPRESS DOMESTIC</ProductShortName><LocalProductName>EXPRESS DOMESTIC</LocalProductName><ProductDesc>EXPRESS DOMESTIC</ProductDesc><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>N</POfferedCustAgreement><TransInd>Y</TransInd><LocalProductCtryCd>BE</LocalProductCtryCd><GlobalServiceType>N</GlobalServiceType><LocalServiceName>EXPRESS DOMESTIC</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><ServiceDesc>FUEL SURCHARGE</ServiceDesc><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>N</MrkSrvInd><GlobalProductDesc>FUEL SURCHARGE</GlobalProductDesc><GlobalServiceType>FF</GlobalServiceType><LocalServiceName>FUEL SURCHARGE</LocalServiceName></MrkSrv></Srv><Srv><GlobalProductCode>7</GlobalProductCode><MrkSrv><LocalProductCode>I</LocalProductCode><ProductShortName>EXPRESS EASY</ProductShortName><LocalProductName>EXPRESS EASY DOC</LocalProductName><ProductDesc>EXPRESS EASY DOC</ProductDesc><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><LocalProductCtryCd>BE</LocalProductCtryCd><GlobalServiceType>7</GlobalServiceType><LocalServiceName>EXPRESS EASY DOC</LocalServiceName></MrkSrv></Srv><Srv><GlobalProductCode>C</GlobalProductCode><MrkSrv><LocalProductCode>O</LocalProductCode><ProductShortName>MEDICAL EXPRESS</ProductShortName><LocalProductName>MEDICAL EXPRESS</LocalProductName><ProductDesc>MEDICAL EXPRESS</ProductDesc><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><LocalProductCtryCd>BE</LocalProductCtryCd><GlobalServiceType>C</GlobalServiceType><LocalServiceName>MEDICAL EXPRESS</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><ServiceDesc>FUEL SURCHARGE</ServiceDesc><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>N</MrkSrvInd><GlobalProductDesc>FUEL SURCHARGE</GlobalProductDesc><GlobalServiceType>FF</GlobalServiceType><LocalServiceName>FUEL SURCHARGE</LocalServiceName></MrkSrv></Srv><Srv><GlobalProductCode>B</GlobalProductCode><MrkSrv><LocalProductCode>B</LocalProductCode><ProductShortName>EXPRESS BREAKBULK</ProductShortName><LocalProductName>EXPRESS BREAKBULK</LocalProductName><ProductDesc>EXPRESS BREAKBULK</ProductDesc><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>Y</POfferedCustAgreement><TransInd>N</TransInd><LocalProductCtryCd>BE</LocalProductCtryCd><GlobalServiceType>B</GlobalServiceType><LocalServiceName>EXPRESS BREAKBULK</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><ServiceDesc>FUEL SURCHARGE</ServiceDesc><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>N</MrkSrvInd><GlobalProductDesc>FUEL SURCHARGE</GlobalProductDesc><GlobalServiceType>FF</GlobalServiceType><LocalServiceName>FUEL SURCHARGE</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>CR</LocalServiceType><GlobalServiceName>EMERGENCY SITUATION</GlobalServiceName><LocalServiceTypeName>EMERGENCY SITUATION</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>Y</MrkSrvInd><GlobalServiceType>CR</GlobalServiceType><LocalServiceName>EMERGENCY SITUATION</LocalServiceName></MrkSrv></Srv><Srv><GlobalProductCode>D</GlobalProductCode><MrkSrv><LocalProductCode>D</LocalProductCode><ProductShortName>EXPRESS WORLDWIDE</ProductShortName><LocalProductName>EXPRESS WORLDWIDE DOC</LocalProductName><ProductDesc>EXPRESS WORLDWIDE DOC</ProductDesc><NetworkTypeCode>TD</NetworkTypeCode><POfferedCustAgreement>N</POfferedCustAgreement><TransInd>Y</TransInd><LocalProductCtryCd>BE</LocalProductCtryCd><GlobalServiceType>D</GlobalServiceType><LocalServiceName>EXPRESS WORLDWIDE DOC</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>FF</LocalServiceType><GlobalServiceName>FUEL SURCHARGE</GlobalServiceName><LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName><ServiceDesc>FUEL SURCHARGE</ServiceDesc><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>N</MrkSrvInd><GlobalProductDesc>FUEL SURCHARGE</GlobalProductDesc><GlobalServiceType>FF</GlobalServiceType><LocalServiceName>FUEL SURCHARGE</LocalServiceName></MrkSrv><MrkSrv><LocalServiceType>CR</LocalServiceType><GlobalServiceName>EMERGENCY SITUATION</GlobalServiceName><LocalServiceTypeName>EMERGENCY SITUATION</LocalServiceTypeName><SOfferedCustAgreement>N</SOfferedCustAgreement><ChargeCodeType>SCH</ChargeCodeType><MrkSrvInd>Y</MrkSrvInd><GlobalServiceType>CR</GlobalServiceType><LocalServiceName>EMERGENCY SITUATION</LocalServiceName></MrkSrv></Srv></Srvs><Note><ActionStatus>Success</ActionStatus></Note></GetQuoteResponse></res:DCTResponse>'
                elif b'<ns0:ShipmentRequest' in kwargs.get('data'):
                    response.content = '<res:ShipmentResponse xmlns:res="http://www.dhl.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.dhl.com ship-val-res.xsd"><Response><ServiceHeader><MessageTime>2022-09-19T12:28:15.376+00:00</MessageTime><MessageReference>ref:2022-09-19T12:28:14.148648</MessageReference><SiteID>v62_X4e7G4Ww0y</SiteID></ServiceHeader></Response><RegionCode>AM</RegionCode><Note><ActionNote>Success</ActionNote></Note><AirwayBillNumber>1159191305</AirwayBillNumber><DHLInvoiceLanguageCode>en</DHLInvoiceLanguageCode><DHLInvoiceType>CMI</DHLInvoiceType><BillingCode>IEA</BillingCode><CurrencyCode>EUR</CurrencyCode><CourierMessage>MY DESCRIPTION</CourierMessage><DestinationServiceArea><ServiceAreaCode>JCC</ServiceAreaCode><FacilityCode>JC1</FacilityCode><InboundSortCode>.</InboundSortCode></DestinationServiceArea><OriginServiceArea><ServiceAreaCode>BRU</ServiceAreaCode><FacilityCode>DG1</FacilityCode><OutboundSortCode>.</OutboundSortCode></OriginServiceArea><PackageCharge>260.680</PackageCharge><Rated>Y</Rated><ShippingCharge>357.150</ShippingCharge><WeightUnit>K</WeightUnit><ChargeableWeight>0.33</ChargeableWeight><DimensionalWeight>19.0</DimensionalWeight><CountryCode>BE</CountryCode><Barcodes><AWBBarCode>iVBORw0KGgoAAAANSUhEUgAAAWgAAABeAQMAAAA0fxySAAAABlBMVEX///8AAABVwtN+AAAAX0lEQVR42mNkYGBItL2tetjCb+M54VwLD0b5z0CO76T3Rr7GrbczdjC8Ez9p+FDh7edcKQYGJgZSwKjqUdWjqkdVj6oeVT2qelT1qOpR1aOqR1WPqh5VPap6VDW9VAMApSASvAgvXu4AAAAASUVORK5CYII=</AWBBarCode><OriginDestnBarcode>iVBORw0KGgoAAAANSUhEUgAAARoAAABeAQMAAADGw0k3AAAABlBMVEX///8AAABVwtN+AAAAUElEQVR42mNkYGBItL2t4Gt8bhNr9YkdrCeEGFVv8z40P2zB4LH3sO1t9gYGBiYGIsCoolFFo4pGFY0qGlU0qmhU0aiiUUWjikYVjSoiXREAupgNvKWXKdIAAAAASUVORK5CYII=</OriginDestnBarcode><ClientIDBarCode>iVBORw0KGgoAAAANSUhEUgAAAQAAAAA4AQMAAAAo4rRRAAAABlBMVEX///8AAABVwtN+AAAAPUlEQVR42mNkYGBItL2teusdw++2Tf8f5jbKf1Z4J35C6J0w7+dcKQYGJgYCYFTBqIJRBaMKRhWMKhg5CgDQWw5wNnFXcgAAAABJRU5ErkJggg==</ClientIDBarCode><DHLRoutingBarCode>iVBORw0KGgoAAAANSUhEUgAAAcIAAABeAQMAAACKBYaKAAAABlBMVEX///8AAABVwtN+AAAAcElEQVR42u3LoQ5FYACG4Y9NlE1yG5LsGpx+RiEagl/TlbMTzrizf5qk2gTnHjTb+/THkdT4nQ3K5XNZnWasq+hwolyvfkhSfb3JekW8vmcFRpn2VtdvC43k6i4mk8lkMplMJpPJZDKZTCaTyXza/AMS0ha8KSF+kAAAAABJRU5ErkJggg==</DHLRoutingBarCode></Barcodes><Contents>MY DESCRIPTION</Contents><Consignee><CompanyName>Ready Mat</CompanyName><AddressLine1>51 Federal Street</AddressLine1><AddressLine2>Suite 401</AddressLine2><City>San Francisco</City><Division>California</Division><DivisionCode>CA</DivisionCode><PostalCode>94107</PostalCode><CountryCode>US</CountryCode><CountryName>United States</CountryName><Contact><PersonName>Ready Mat</PersonName><PhoneNumber>(*************</PhoneNumber><Email><EMAIL></Email></Contact></Consignee><Shipper><ShipperID>*********</ShipperID><CompanyName>YourCompany</CompanyName><AddressLine1>Rue du Laid Burniat 5</AddressLine1><City>Ottignies-Louvain-la-Neuve</City><PostalCode>1348</PostalCode><CountryCode>BE</CountryCode><CountryName>Belgium</CountryName><Contact><PersonName>YourCompany</PersonName><PhoneNumber>+**************** </PhoneNumber><Email><EMAIL></Email></Contact></Shipper><CustomerID>131297</CustomerID><ShipmentDate>2022-09-19</ShipmentDate><GlobalProductCode>D</GlobalProductCode><Billing><ShipperAccountNumber>*********</ShipperAccountNumber><ShippingPaymentType>S</ShippingPaymentType></Billing><ExportDeclaration><InvoiceNumber>CI00002</InvoiceNumber><InvoiceDate>2022-09-19</InvoiceDate><ExportLineItem><LineNumber>1</LineNumber><Quantity>1</Quantity><QuantityUnit>PCS</QuantityUnit><Description>Large Cabinet</Description><Value>320.0</Value><Weight><Weight>0.330</Weight><WeightUnit>K</WeightUnit></Weight><GrossWeight><Weight>0.330</Weight><WeightUnit>K</WeightUnit></GrossWeight><ManufactureCountryCode>BE</ManufactureCountryCode></ExportLineItem></ExportDeclaration><DHLRoutingCode>US94107+********</DHLRoutingCode><DHLRoutingDataId>2L</DHLRoutingDataId><ProductContentCode>DOX</ProductContentCode><ProductShortName>EXPRESS WORLDWIDE</ProductShortName><InternalServiceCode/><DeliveryDateCode/><DeliveryTimeCode/><Pieces><Piece><PieceNumber>1</PieceNumber><Depth>54</Depth><Width>44</Width><Height>40</Height><Weight>0.33</Weight><DimWeight>19.01</DimWeight><PieceContents>Bulk Content</PieceContents><DataIdentifier>J</DataIdentifier><LicensePlate>JD014600004532812683</LicensePlate><LicensePlateBarCode>iVBORw0KGgoAAAANSUhEUgAAAZYAAABeAQMAAAA6+qC4AAAABlBMVEX///8AAABVwtN+AAAAa0lEQVR42mNkYGCo5K2e+6CjuF7xeAN7g9ALtvk/OqffYmDMYNj3P7MqbeP5iW2zEtWcljZIMLy8P6mDgYGJgXQwqmdUz6ieUT2jekb1jOoZ1TOqZ1TPqJ5RPaN6RvWM6hnVM6pnVM9g0gMA7e4WvOeQ35sAAAAASUVORK5CYII=</LicensePlateBarCode></Piece></Pieces><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode><PackageCharge>260.680</PackageCharge><ShippingCharge>357.150</ShippingCharge></QtdSInAdCur><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode><PackageCharge>260.680</PackageCharge><ShippingCharge>357.150</ShippingCharge></QtdSInAdCur><QtdSInAdCur><CurrencyCode>EUR</CurrencyCode><CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode><PackageCharge>260.680</PackageCharge><ShippingCharge>357.150</ShippingCharge></QtdSInAdCur><LabelImage><OutputFormat>PDF</OutputFormat><OutputImage>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</OutputImage><MultiLabels><MultiLabel><DocName>CustomInvoiceImage</DocName><DocFormat>PDF</DocFormat><DocImageVal>JVBERi0xLjQKJeLjz9MKNiAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDUxPj5zdHJlYW0KeJwr5HIK4TJQsDAx1LOwVAhJ4XIN4QrkKlQwVDAAQgiZnKugH5FmqOCSrxDIBQD9uwpXCmVuZHN0cmVhbQplbmRvYmoKOCAwIG9iago8PC9Db250ZW50cyA2IDAgUi9UeXBlL1BhZ2UvUmVzb3VyY2VzPDwvUHJvY1NldCBbL1BERiAvVGV4dCAvSW1hZ2VCIC9JbWFnZUMgL0ltYWdlSV0vWE9iamVjdDw8L1hmMSAxIDAgUj4+Pj4vUGFyZW50IDcgMCBSL01lZGlhQm94WzAgMCA1OTUuMjggODQxLjg5XT4+CmVuZG9iagoyIDAgb2JqCjw8L1N1YnR5cGUvVHlwZTEvVHlwZS9Gb250L0Jhc2VGb250L0hlbHZldGljYS1Cb2xkL0VuY29kaW5nL1dpbkFuc2lFbmNvZGluZz4+CmVuZG9iago5IDAgb2JqCjw8L0xlbmd0aDEgMjkyOTIvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCAxNTkyNz4+c3RyZWFtCnic7b0JfJTV9T987n322WcyexJmJpN9EhLChBCI5AmEiEYgrCZoJCAg4EKQRbQVIiq7Ci6AiJK6UtAyJKgJS4lL3Vor1qVoa6WKxY1KW0WrZOY9984Egdr293v/7/t5P5/3Y8L3Offe5567nHvuOefeSQIQANCgDQSwXb54YfCvh+/+DZZsBVAGz2q94uoHhb86Mf0mgHT+FVddP8sSvf4OAHMGQCR79sxpM165wzgIYLCOPINmY4HjOftLmF+C+ezZVy9ccnXbxb/FPLbnbL9q3uXT4I27NgOMrsL8w1dPW9JqMtq/BJh2BOsHW6+d2fqQe7kLYDpmbVdJeyGD4zHIEHMB+0wc7UN8TuIoe8co/RSAZCaR+uqAx+H3JJ8EoZN8Cx74hvjIALgARPgaZ7oLeuEecMJE2EgckA1umAQXEBHrRGAduS+xOPEJnAd3woOJp8nyxA58fwe8AN/gCP4kEqiAMVh/EsyET4SPoCmxBVRYCUYYCuOJG6bB2/j9FY7hLrgbfkl+mvgGe3XCcmyvCmqgJvFM4hQUwjpxvXRYexI2wD4iJy5PzIF+kAVraCTxduJ9yIUmeAgexzFFSI84CkJwJdwKm4lPeAFT98DDECcm2iyMkA5iTxfAZLgGroM1sANeIQ7SIB2WTiR+kjgGMqRBPo5pDnxCyslo+ohoSgxLvAuXQDe8hPNl3z3iJeJj0iXx6sT9iWfBBU8TA9lPnpHKpNt7b0r8LPELMOF4BqBExmA/0+FmeAZehr/B3+myxDIYBROw51+RTBIkuSjxt6mPLqVLhTegP862GUe7CLZBDFdkL+yDAyibP8AR+Ig4STq5kEwnG8jfqYnOoK8J9wl7hDdFIv4c5R2GHJTRQngEnoLfwKvwGpGw/VLSQOaSeWQTuZ8coTH6Of1aVMWbxe/EXik3fiT+XWJM4ivwgh8ughtgGcr2IeiEPfBbeAv+Dv+Ak8RGBpPZ5GckRo6Qz6lGs+hY2ko30kfoE8IYYYPwjFguDhevFF8V35VWSGuVaUr81KPxu+JPxF9PPJ14HXXHgu3nQh1K9CbUikfgILyBrb8D78EHTH+w/aFkCrkMe1lAVpG7yRPkV+R18inOEvh3Fh1Ka7HXefRalNNyehe9G3t/Db8P0Xfpe/Qz+pUgCVnCIGG+8DMhJnQJh4S/iDYxV+wvDhDHilPEBK5MmXS+NEHaLu2UnpVOyFXyDLlV/lhZrtyi/qa3sPdPcYjPjsfinai7KmrSDSiJB+BB1Ps9uAavoER/iyM+Al/iKvhJiOThuCtJHakno8nF5FIykywnK8mdZDO5jzxIfoEzwDlQBcceoTV0Ap1GZ9Jb6Ep6G92D33vpy/Rtepgex5F7hLAQEQYIFwhThEuEa3AOC4Wlwi0o2Q3CDuE14Q3hmPCxcBxXzSP2ExeJN4j3io+Je8TXpYukq/H7Qemg1CO9Lp2STslU9ssZcok8V94uf6DIyiClQVmtvKn8Q20lGaQQRx6EM76oD/dgP7qDOsVl5DgWZBIRrDjzCK7DBNwV/4BqIY7rYmHvcWwu6hPTGKesizHkX0j2QTn5FSyTqYBWUTwCHeSP9Ij4HD0P3iItxCc+JlwjvUJDsBOt0Xq6n+4jw2EPraKT6VYByEdkO3yE+r4E7iZXkgWwkxwnQ8iNpIIsgzepW5hAboGqxINUJBq5gJwAHAHcJM6Ay+A/fpFK+CN8En9ANIs/RfvUBRtxRR+H98nP4VsiJT5H6yagNZqGVmYd6vutwKxeM+6zZbgffWhBrpJfgz1ERiteIQ8Tb4AT8E/4RNqLGjUcLemx+BzxAfHDREWiGHcY7jLYjvtuNpyPO+Yj1JIDmGe5S3GnG9CWlOGuboApMANuRKu3IRFLbE3cnLg+MQ9+jbzfkiLyLWnHHdGFHFXwEn7fAe+QtbgPz//P8/x3X/EZ0AOfEi/JIWW4H45Li6X10g5pj/RL6VV5AEr7FrgPNfoD1GYDzuByeB0+ha+JimvjgyKI4ngH49gb4SraJByAEcQPrbhn89GOD0/NZAG2shyltxX38wHcGyfQTlwKv4TDhBIPzuhy7F/FdupRzlOx9qO4gjeTTiyZgVa7ED7DeVvIYLoQ+9OxpY1otXpwTH+Ev6C0E3xcRWgXaslkbOtruBhmYA+DoIHshrrEU2ipxkCt8BuUdzaxwXCSRR5GvhbcoRbIhErpQ0KhKD4mMZjOEQ6gj0lgeTt6r3Q4j8zHUVhxHr3gImOhPD4einRdrx52XtXQIZWDK8qjA8sGlJb0Ly6KFBbk5+XmZIezQsFAv8yMdL/P63G7nGkOu81qMZuMBk1VZEkUKIGikeG6lmAstyUm5oZHjSpm+fA0LJh2RkFLLIhFdWfXiQVbeLXg2TV1rDnrnJp6sqZ+uiaxBaugqrgoODIcjL1aGw52kSnjGjF9W224KRg7ztOjeXo9T5sxHQohQ3Ckd3ZtMEZagiNjdYtnrxnZUovN7TYaRoRHzDQUF8FugxGTRkzFPOHW3cQzjPAE9YwcspuCasZBxfzh2pExX7iWjSAm5IycNiPWMK5xZG16KNRUXBQjIy4PT49BeHjMGuFVYATvJiaPiCm8m+AcNhtYG9xd1LNmXZcNprdETDPCM6Zd2hgTpjWxPuwR7Lc25rnhqPf7LDbuGNG48sy36cKakd45QZZds2ZlMNYzrvHMtyH2bGrCNpCX5tS1rKnDrtehEOsnBLE3emtTY4zcil0G2UzYrJLzmxkeyUpa5gZjWnh4ePaauS24NP41MRh/fajD79e7E0fAPzK4ZmJjOBSrTg83TavN2O2ENeOv7/TpQd/Zb4qLdtvsScHutlhTCZP5zMTM0+94ildnqfrxpyVL2IjCF6BCxIKXB3EkjWGc02D2mDkY1lw+GKvhVxNBrtgMXJE5MW1EyxrbEFbO+GNSji0cXPMVoAaEj39+dsm0VImcY/sKWJLpyWlVw/d96VgkEissZCqijMA1xTEO4/ny4qLFXXRQuNUWRILigwaU7bSmISUo/lCILfDaLh2mYybWNq4xmQ/C9PQO0EsiTTHawt709L1xTWJv2vrenGZvCaMm7wEWj7tiau7pf1abO23k7CEx4v4Pr2cm39dPCNePm9IYHLmmJSXb+oln5ZLvB59+l0rF0kY0Cuk0laLpAn+LSnnp6cos02iKiTn4T+ZKPSMmoFLyAhKsi9laRiWfTYZQ6N/ydCnqGUxdiROMi5Pv2VKjjA2JnJ0felb+rNGZ1gg4XjGX1k+csmaN4ax3dWiA1qypCwfr1rSsmdaVaJseDtrCa7rpY/SxNa0jW/oWtCuxd216rG5dE05iNhlSjCEBk7aE3+hhFRi+h5K4rHTRaj0NJDEugEER4wR8qizFqbCf5IKGgaUXvBHbyareqjG2L6tG91ZBNaZtp/AxoDRkD9lz8EHQWZ8KCj2ndAm+g6DYg31hTA3kA4wsWF/99XRhMJHlwaJB2yVQKueSoFQqUWmX+upO1n4za7TqJFQfrz4+oDQN2yWIl4kvfgwPCWZGT/2DPVnLbBYH8bSkgIGUdYOSOKxrFZVROR8fSleiR9fyy6Oyjg/MHdYbQnn4Dh8FUCgWSvmGEtNgqJCqTXNhLp0pzJJmq1cYPhasF8qEqhoRDJomKhpB96+ws6KsiWJQkp2SJKsG3Z85zMC6MPozo4YcKgiyqHWR/bpFVqgk4nFKNXk8fuii03RjgPAgv40IpItm61pAI6Vam0a1vTQbRKyhBSUi+YyXXe6NoHSbR/f6TjbP/7J5vrd3zMiZtX9BUaNQqqtGH7c7KkuqeiORqpVS/8jKG59f2d/LiGKrqlr5/PMDSkl9zDihPtYP1aobhES8QxUNexNxFM2p3bI4mH01kfnNEf4VCgn4TUJpgiAdjP+yrfep6+Mv0KGksvCVF8joeKe099QaGuw9giu3ESU9HSWdBkGMBQ7r1dcVktmWJYV/EU+KohZyaXJ+USjH7Qi4xrpoqWuXi7pcznBWjiNNDTpzCND0vFa5DUPf+vy8XSZiQsl1asaoqYuu00Ol/fX+Df1b+rf2b+u/vn97fzXYv7Q/7e/MCkIwrTSNpnXRtZ3FAyYkhcN0b7Stef7JyPzRx79sPs71kMFeWdI8HzdmN7gSbR2ZlS7spMPPSNvutEqcbxNWIihBQJwWlRVFtdsQRLE0Q3NaqKwfdTllxY0BhCxj2BAi9oFlFYMGlUdz83LDgj2UyuSGN9ILf7Fz5ZR5U1esb/7Z4gvjH8XNJP/ZJwovurj+wqLXdxBHe2T4BP36V6S9mZfeO/WKxyN5+5fNODDfrFLxhfgTknbx+bWTNKm3O75EMzWPGX5pIW4TmJY4Jl0mvYEnvTf10Su01c7V7m2wWX5Re1N40/iVoOVo+aZ8c4GzwL1IWqStkFQlTfF40jyeAloo5EhKvlRNxpJ7pU3ay8KvjAoZbwNyBGMuCkzidm+UU4MZKZmie7zFomrRLY6opX6qlYy1Eqvu8katXSRfz3IUGwTrF5bJ8AVgk5T4SzNIhiuvXSFWJaCUKgJajHWd6UtTy4KLMcbWfLIZ1wSXpPfLSPP8oxFGWWJAKTST5uZmIsliOAh2G4SCHrdHQjFmyXabe2DZILGaBIbHX/08/sf4KnIDiRLz9hll8T/4H1n80K9fal+8g6ZfcuITcgeeQa8h92y7LFZ37S2fxr+Nf/r5RmYJ7kb9nIb6aYMALNMH5uPmPt8zU5xpkgo9lZ5R7ib3bLdU6RmUvjL9XmmjUQrYmVKmOXKsNtWXt0shSkoj2aT0tLYQCYZKQzRkd6AO2kpt1MZ0MPiDOnhaAdks5xOmRB632+Fy4tkOv8NJFRpGmdagDt1NM59uuamrpbhi1uibpz/c+wbJf++nFaOmVlVdNWHYk9LejNxn48d+++TN7ZfXFwbEZ0+VWxyTf7Vjx1OzHBamIZvwBHcLzlSDa/VqVRJlKUcJqqXqQfV9VSxR16tUVUEQcyihGqhKtTwWd914AW0+9QeNpUZqFLUgmrRSphM4J8OZc+IrWDUaMzgpNiUH21NIBQktzIDSgfaQK8SxSTjeO5TO6N0q7f0m/sg3vRvY2JiVKMSxGeEhPUcTJYNANUOO6NglEEEAWZJwEIqqGkGV1KD8GpM6Xatn6eYGc4tZaDW3mWnQXGpuN/eYRTM1JofZg+aHDdQ0YNHZwr/2ZHPKD3HjiA82Wm4ABNz5mZVC0gAw8r0B4DPpm8zp740kn9aS/Pjh3v3S3t6DtObbOnpT7zKc0zqc2B6ckwDzukFCJSmLRiWmLOEcTvVqpycKki41SG3SEUkKSC1Sq3RCEtsk3DRUAJUK76C/jeGpR+hhW5FN6hDmRLhGHLAttXuuTU2lGgdGMIujZeNbR/Klvd/W4ThQwLIPx2Eij+tGo5Cr5hoFEeWK1k3XMoZEDcEhQ6NaV+JIZ4rqD2f0x1J8yJpq+FD73ICW2mBIoxmiTQsYwrRIDGolhivobHGmNtdwHV0iPqztMDyp7TWc1L41uLeJ67Vthhe0lw2/p4fFt7V3DMfox+JH2qcG83XaEsPNdJ14s7bOsJ4qjcaZdK54hTbbsJheLyq1tF6s1eoNF6sXa40GxWsosUTpEDGqDTVUWxSBmkRZ0wwu6hc9Gm67oXoxetqgqGpamSA6BUGkRoOhTKCYpEZVEEwipSYDOmNFDViIpYuYO9l5bi8dzNfjkubkOngmTIxKZYquLFOJemAZiuaAMWg00S46WHfgAuhYEXSsBGUB5oGwGTNTKduX849HIraqv9qq/D5b7/ze+VV+rw19LBbYjs7HNbFx/XJ4Ks92uhHuQ9ImoL6piSO7jUHmW5v5F1+/CETmN+MiEsI0jKAZ2ED24ZlZIfvjx+PvxT+M/wk9rFf4+Ns6cfl3SxlwnTfjHgqz/U0e1S2aIKs+waOKDtQiXGrodBirmT6zWTOqF+KEhDJFdSqKKqiUKoKG4kJRCSKbsMgmLJbJr2F8wfaaTzc2GFuMQquxzUjbjT1GmrQJqpZqlFHdMmFCVCs7a/cZzth9GH7g/uvbgJjjess8ayUgVvZnk0cBDSgdwfdi21PGcrXNWM4HfJ6/f1SdgA9JcAtlgi6IdcKtaLba1Q71qCA/L7ymvqsKQaFEjQpD1bHqncI2tV3YpcaEg6oxGdQNLI9SfSAP6o7o5pKyKA2yh+Isx5JNuhbqH6UT8cFr1/ULYg4fKlUULxU8ShHNU4bSgcoYqiuX0smK5qTpymg6Utmi7FR+Td+hH9Njyj+pMY/mKxcqS5RVyuNUZnvy2kjfF/QtcRPwFWY2hNg3kyBtJGnx3/fuxoUtFt74tk7Yf6qW+acm9OvH0K9bIR0e1Cdtkjapm02bLaJKFItqVbx53iXadQ7lOvsS1wpxtbratMJyq2O1c5VrlWeVd4XfpDhwhf0uh9/p97r8SlqxWfMVK4I7b5eBgMFmCBoEA/NcwdJMPbMlszWzLbM9Uw5mnsikmba8diBWdIylfC3XdWYsfe60z+ZerJl7MR5xowLPxzgoilFOxaCBKVcNxOlAF40ODN1104iyJ65Y3Ulqya3xpfED8e74UjLgL7t3f/je008foW8e2dzaERkSvya+JX5/fB467Nn/jCcSiVPffMfkcA96r29Qu5kcrtNzZKnb2e0VzpfIFdLbEnXYc8wWC6TbmPeygur+F8/sDmSWpuYnZdqsZzqyjLOd82nfnHJj3/tnXDAMPlIBXjjsozi1VHx3D/kDsYxfumP6pjFzX37mwV2LR1w2qrxd2usOvbdrZdccu6v39+Kz8Zb+02saZpsN2DHzeOgxwAUh+EZfXmm9wHqxMtc417RDe8zSHn7KclgzyKps8KhuwyBLnaXOqqg2ze60OK1O2yDLIOv51kWW621vGIxLtCW+xZmrtFW+FZmy5nZqJqtlgmWR5RbL3ZaHLJIlaDY5zWaT1eQye9w5aTYnaXG2O6nTCcEQExcKzgWqhR1B8sBsM1Pzm+l57XJM7pEPyaK8sjVMguHSMA2HXGdKLWvA5d9LjetCKqbmNu/7AIDvbtzZzZYbbc8TeyqGxqhufjMTaBmXJ4bNnrSQ0J+Gw3b791LFUHneZ2+1PftMy41zO+MPvH3txMtmVf3hrblVY0dl7zkm7R37yvJHfp8xeMXO+AekemdTqHerMCa7cfiFl5gkFlVcmPiL+HfcO0Ukpp/Xbe/KfCr/hSIRA18XBr4ub2SmNDN/obzEvDD/HdPbYVOTYZJlUlZTeLZpluOK0Jz8K4quy1yRuTFkcoSZd+wXiDKqz/T5o+OyxoWfyXomLM7Pmh++Keum8J+z/hyWI4ZCc3ZWdrjSHA3XG+rNtVkjwnPNM8PXm2/IWm1ek/Wo4THz9qw0zaCZ5Sw57DP4zO4sJStsMIvEM9mr+4LReV4yz7vNS7176UxIRytk8lcG0kl6sVOAUYSZpQv8wWgp0UkDaSHrSTuer3uISv4q6v5Km0jE4kLN+0XCQzx6mifqqVfycv39A3ntthhGovXkC3tyAX3Fv0vpfP2Ext2gD27iJ6IxtpNII9eyOHx+5MvmyNEkvTZyFJ1Y0nTxMCkL5ZGeOQzlcShFP+xIq8xC8SDB3MsdDpY7pFsdleago9LAYWVlH+sWE5aZKw1eBh5eff/V1BcK57jdScORx7/Lo4PQqIjJwF+RXU6PW+Saw04FF5Kgf9vKOzacd1G0+68tK5d98XPiJB4lfjjtxhtvuqCkaDCJvbZoXQIOxj+Nv03ey9iw6vpx0QvSHf2HTr7+F63Pzfr7K+b5l5dnVUZzSmZdfWDt0j9eSQjTnyK0Od38luBaPVyilYqlUoPWiufv9ZoiE4nmiAJVQNXwuC4uY36SFOsGWcETOyxjuwSzdsHSQFtpG11PRepTex9PSX1c426KUucRc28VPvC0fjRlc6p4JIeOoZzFy+T9+GjxtvgY8dlvvvluGI5KAVDW8njuId0RESJy0DjQKIJMjLp/SFTGsK4TqXAG7fCVo3c+pmvsxsGHD1NfDlhOYlp9mTszKgbxoWCIJZv84NIKIEdTPjEcM32t/dPwtUl6UXrZ8KLpXXgTI7q3TZ/CR5q2U3xI2ml4xLRP7JT2GZ40vSRq/cUsqcQQNN0n3iXdZ7jHpD5rFKVgV6K0U8ZgrStRpl8qgCkIAqVBAk6UtAHj+zKjwWk0GjRZwTOJ5lRVTTSaTKmwzihjLIfnENEkSAajoqmyqiiSJGK8QpIBHtovXIQSjN+6SKluCMoHjAf0EhbkYtYUZHcolPjMfdckft/o3ma/t7fX7+tt9vbdlCSjNVvqm9st/GfnT7CzIG70mVHc2QR1l/tA3CQpT88e85vZLQlGcGlICSEz4w+SkveICfcu+TMpjG+Nv4DH1vfQ6duFL05hYI8R3ajvulB5Lkh8LPYXh0EYysgYfbbiVzOkTLf/wvRRGRfk/MH2vl0b5KvzXZw7y3dF7orcO313+R/1d6e/6H8p3STLZpdb9rnz5AJXk+86uoI+Kj8pvyCbDkbfsdHM7LIB9iJzth7pH83Ws/Lx4cuMzss+lU2z6zKZhSm1WKPnZRLItGXGMv+ZKWZmFpGBoGMpiwUoTArpGfbqkJ5uw4fXHw110YVPiorJbChiHhffcYqvOcUaRVhD153GfgNy1QIt39wUMG0zUQymExhP6xZ31OQfGyXRFtTu20tRTAMLQlM95H0PGeuZ6pnnETy+gXNq+s47aKHmH29mB85IMneUnX8wEo/g9sGgktst7n0ix9l9Fa6jYLEld9X8ZmZd8tCWML8jON2eEDMvshzO4iamYlBFMlYhzMW7nG52hVMxqJzMTER+99r+rnohPSf+qdGmCKMebn74wOT77vzVRQ3z6ieSywZ9ml3RWHvRyIE2I/2g/5a7m1Y/He9ad+tFGRU+ta6uY9WU2+ozcoIZ40YOjf/OUebNqxo6uSy3InsmszOTcK2rca198Gd9XKO1ydHknm2d45jjvtF7vW8T3WR6wfaC9/e2t72fyJ+on6R94vpGThucNth1oeNCd523yTTHpAxxVLgrvMJ10nXWldIK62rfdsdj7m7HU27NwtcgPcrokw5n1DLQzEp8/aKcWu1R814iggFXyGE3go5VQcd6MHA9rsRe3J8ivgp6FMJKSQhKzCxhDo3FI5U/XQk5ff7G5OKwixx2jxP58niE3eQ0H40kL3KOss3BJD+/mSSvbrhoB1VITPIsVMT1EAfEP7NcPnbOjcuubJjlIs7Il69+Ev+MuI8/+xH9vGzCxA07Dmy9ZF7JL58luUTEs1DOYywurEHZ5aHsnJBBHuoGW+Ibvc5Yea+2xbzRtl16zLBP22fu8quqk4yi58t1hrH9tpufkp/yv2h4yfS24bDpG+VrsznDmuHS0zOjLt1ij1pdB12vuQQXl06/ak4tHqT0Nh1DK0eDpcVCLV4H88ZP+dKjZKCD341lBpN3ZFkFSRopTlJvBqe6FTdQO/u4wobDnupw4J7oFI0OL9sb2UYFQqTElRRqSb+p/eb129ZP7GcNqbrZGlV9mSn9j5x1WXYcnbHu9Or5zmqv3s+KD9x0XrY7uS+t7uXO2oGDwBoONhis5EhtTkY7+qp+mTJbnAHwhaOSDbrDw0isUzMM49maUDU3bE1H2bZq5t1bdJSShXVqYd1bdBQWN35N/MIZQwa0pQP55QQ0Rwhb8iA6dLbmIIS4m09LenUP/ZZ4B32yK/7ZrXOI843jxCH36sLyacOn5AlLJl9aVUXI+JItP3tyw3sY8UTiL8YP3Lh2FLnqhmUjRixg+8iLCvEXjPfc0KWXDRJJoRi0Be1NYptXUsWDXupy26nT4bZb0qxgs6QRsFGnplqNZKoxgUdZthAGmditbpJwEzfL9rNhuyewaTnNadAGVuOxsgFPy/m2EvtUO7V3EVE3W9JyqXMqtLt73NTNdEIzRd0+z5JuOgeSaza/ajT7tONUMzp631Hwop1ioTGiGh+VZVb8ShmntIE80inzKHyXuAa6wnhEDHu3Vt67aMmC3BHDziv/3e/ix7aKuQ0rbpmQ/bytclz9e6eeFi5ge2FifJzYwn1GCSnTW67LXJlJHSZz64AV5rYBYpBgDC+UkoF0oKCTEXSE0GRtcjblTC6YjEv1jf2bNPtQ80D30PyBRRi6uuvza4tOmHo9htvRRhtNZmOhyZxncXtcxWYTBl/ebKb/T3L952pusXMV6TSakjS/MKn+4ZwkHRBNbgPNlc4N/VQJBdwRsOYxYjEUM3EbXYrXJxcWGHP93i5SoGs+n99/xwAygHSRLt0AA7NDDl9pY1XK2nyJe2H+STyyHbf18uAJD6Job1Kn7aMRdOEeft6oZMDzEzvIoQlqnj+iUTfPsc5xzsm5omBWZE6J3IzncY/k9vQZ/HK0Sykl9ZSH8NhFw0H0EGlO9+nj7PWkRs3Mn3xNRU6aeWnP2zdOJ+Tgr9qIMqx13x3xv39w6uaWK25fNXvmzXV5g139Qu4B4cvue/zJO94iRuJ/4p5T5+/fO7eq+3YLvfnn9//sgUfa70eR3InxZhNGdm7o0CNWEiCVbLFsw8lw+5/IP4mmSG4pmzbaZ9slQmia0+5IE5yUWJnoMgVFMxicLoMbwGjIVTU9mB3dpZGERjQUJgrenZUdXe9t99JW7wkv/cJLvODMdbu4acK67S5ywkVcPk91UrwY66cudjF1MpXjESoLk46jTD3cr6o8WsXznB3Vth91obpGuYmXWZLsXHVg2taxmfFjwXHn1V0zMI6ntt6Pto1qXXVH7wY64LEp5bWrV/R+jpNG/b0LN9vj/MZUgeu6QWN3pHZDta41aLRNi2k92iHtC00KaC3aMq0dCyRBVkASBSsQnd+MCtBMcbNKsiIaqIJ+gmtcKDsq+tTUvL6fRzXfgt9f6+IGvDbS98HhXckPDsWniBg/9d2FYu537+IKrcYVmsrvqf/M7qfe6zTb+a2afqOvOKoINiFNztNmybsMBw0vab82vGswTBBaBGpWvFqdfLG6WJae0t4Xj4unxK9kaYwyRp0l3yiuE+8Tt0pb5C3KFtUQEB1yRIxIhXKhUqiWmOvFesmAwQgeGlWDZNAEWTRKosw+iDUaVcUgGAxGsYterfulErUyoBBlppkac0kbkAAO2Geq/kkqcGLz9tlOzvfivmEBbt9RPXlBqeLpPLmU7B7n2ubkLTILWkNEsa8mPnIBmRK/h9wafz3+1c0YsJ4ki+M/7b2MvLc6/jh2tBRA3oy2J48M7YYClEgzrhzaepNLdpuiQlSNeqPhWjpSHemtDZuCQknBBK2loK1gW8HD8mPKo6Yn5SdNsYJDBUcKLFBQUtCALw4WvF8gF+j+jGg15tv4S0kJiYo/kxnnDoMS4jZaVGx2e156RkZungEX32rLddj1KeUtdjIPl7KL1ulWf3puZgaWzcsgLRkkA8v25OTm5qFZKegAyOOuXqtmVB+E487Dqnl6DaIKkZ0XzdOHnBctyXst7/08wZoXyGvLEyAvmFeal8gT83z5H1b1BaepY2zSJlWdRK+Khv/k/GZG+jYPP2egqTrjE8VrI8z4k0hayMVCUw8PUD1uvpnyTm+m7/fVUiKs7Zm1sbTuwUsXPZiPuyszb9zQ2f3jx/pVD6qZXRw/JuZu+PnESZMmTr20dnNvE536QP+qUWs3ximtu29KUd0t9/aeSn4yIDbhmrlhm+5V0jxpU9TZqtglElwtW61aa/3EJsncuNgVi1k2GY14WKMk1w3cuABJYCP/zrgYjLkmC5Ov2Ww6bWNM5AR6k7NtDJfUv5iZpGoeT90Uhc4yKlxIaGrEpvix7HGVFyyM4FaV1r7RvGVsgPZ7fObghls64gExd+ueEbNv+QmzLOMxStyCMzVjjL1JH/UxOaZ+nfa1S3yRfixRh0/yabTJNjltsrvJu4luljerm0xd2lv0D9IftbdMx6Rj8sdm22Pqr+lv5OfUF0zSInW1fIsq2LkWGj1MRE5RcVYq/pb01nSabgnBWUHx/JOnr0lP+x9tjm2WY5Z7jlckzPmQ5rSoA6cFLieEs7Jzc87wNOPX9G79G4nGX/78zvjXa0hw4zXX3HPPNddspFnriLwm/uIXf4s/d0ti+wPbt7dv3b6dzXdt/CpxE87XBhmwRe8/OG1UGnVEhUpzZVo0vVa4wHxBWm36P9O1yfJkQ5NjsnuytynjpPLPdBX3j5/NSlKcbFZuo9FmtXhCqr+1H+lnL7BYrLk2G9s2urEV2thNUmZ1cp7zRx+vwoW0He2bbtId93kH5nFnybMMc3DOs7xzMrjHZerO5owHADyA5dlD5IxZryXywF/M7SY0fqq78Y6xuMTu22dNX77i8itW4dI2zIj/Kd4bPxl/p25S7ydCd+fO+zsfe3AbKuRKAKGCz327nr9JIpqFTJBmSYskocTRaJltaXWIBs1qCpjoHaaEiVabxpqoqYtepxcoCuq3QGVDPmg2rVRr1UTNv8yxzUGnOpY5djkOOUSHDXLZrQLOn9I20s6uFezV3SQjGerNP0OdTzb7RieDPZQEandlWVIU86E+5plQHyvnPz9QNhjlEOI6fTrsk+2knWn0iCtrW5ouPv+8oeNLxNxNV9aWf9W/Zkf8bzjHUtRnG86xkF6jPyDb5bCa57F7wpsdm52b8u4p1BRnnZM69pm7LS+GPgp/Yz6ZJReYJ5lnmu8xbnI8ltVtUmrCenZt7hVZM3JXOlY6V2TdnK1V5I6U64wXmsda60LDs5Ss7LzcClN5qDyrPFyercgGya6FvOY8U1ZWVljJztKLFpiWOK93LS5YVLjKdUvhFtc9hXuy9oTNbeQOzzrvvYU/L4wVyVldiV+zWDCUoln8KjWb5Y90BrKTeZ+f5/V0TFxpJoOy6rI2m+/Oej7rzSw5lGUyi6IfUtEmDGRxZ6enuJqkDiY8n5UT5bejmegtgCTvR8UW0kZOEAGIjd+WirxmmhtrEqK3gkimiidEKtblG906Nu0e6NGxXY+OjXr08oqoh92RePScAnxgu1ZPgF9HiJ5Jfh3tndVPGvwJP/XXpSmekFsPhaNuPSMQDbjJ+3h+GKiGGnLuyKE5ujczmuMv4h81onNpKCKlRaSkiBT1C5XaiG0gnqlTDij5sxaG6mSgrJmj4Iss6WKadQqdCr/4SBlK/iMszN2wq9pmflebCnpZlv0MxbXJLAuB+66kkmeM+fjVnPy0Ozvxsq4ZHdXWfHzgCnz+lLnS5DRVsmSHid3WfrrbWMkPc4R9ZDU/dS+LATK6pbxsfi/LPNWZ17LsR2jZfUop8Tuuufzqihyn64L445csffejd9/Mj39tn9o4rzSYkUueaWr88ot3eklJZPyk/IySoMtprx82+d41+29fO2DY8IA73M+VMevC+hV3/i6GGh9IfEw3SPej/X5VLwgCHmYMBdYhlgstTVbF5wKv4HaBx5HmJB4HdRKvoCkGxeRlC20FT7sn5hFakPR4BA8e2jpchJm3TnCxn15bqFtMRq3EUAJ4bpqKO5od6/K9Qq7HMclV7dzm3OUUWpxtzvXOQ84TTgmcNmfQWeoUnT7/kvY+x18fq8A9PZT/+JQz0cOudk8lb3ZtX/Iz33H+U29Y9Si6fPvA1JmvmeABz8ll6mFCY1fd9nD5wPIcO72hx5iXkXehd/pPL7qh0qjddBPxi7lH4hOXRzLS3y0cOG7kgHvIa0feeDi+GuVzG1qECWIu+vKtuudi+xX2jZKgyT65ilbZ62m9/RhV+DnBLhrdYHA58TiLZ9pclwuYMbO4uUdPHnz/g0fX1NOuXCUnVKL+++NC0h2c48mbQ+X8rg0nGUpOe9AglhTGDDkw58odFxFfYHz1qGsLiW/bpOmX7dhI2+PeIzOHjl10lPRgAI7zNGLMMgXnaYS/6i4p318SVdhDZg+VPTAcP9yJlIf+Qf+Q6BaRyIJRVQ0mI55vqEPwa35DFhQbXzSacKOd0PMzg1EDSEYn+Iw5UGiMwhDjStCMYBCNBk2jlMiY1irZfZnuzciPGs0Bc6lZN4tmj8dvM1QbxvIPUUt1o0grjWK1OFYUxL20FAOkNt1qKgcSRIMkEJ/pedQXH1OYiHf08Wb0FM0+fvvM8zw+tPGfRiF4XmW7NdLMrkySP3NHQmkedkGZhjH50/GJJO+lIR7ZYnuFhOIokN4PnhzpLi6m/fg5xRIfJ45HKaWR6B5HvkTSmHX1mqxR1W22RhX2kNlDcmMZZfMKoKQwXBfNRotso5Ami2lUFATCrj5a0Ep1kV26w2g1l1jyIegqdbW4BHZc5LY3N8pPkY6MflEX+yykUtC9vugyfv2ep2uU5yihLOcglaBnDIqmPodyPp/aQZHRvT58Mskkf3QxEpl/7Wjbl3iuP95ckpQLSiX5+SG/llcs/DyfEk9zfcyGG3AIbsAO0QZ7EydQ+id2CzbCf3SRmzuJffiEJ7Y0W5oPHw5vtYTr34kZRjswn2yrKS2UhlJWLEI4Ky+PX1RaSCT+DQnHV4/IGXHxsoZxY3zDy6df5kO5W+jfT9Hu5unnZdn/aF7QBPwHkunrn99mJLdNtVZ9pfpU/lsbD35YxX8b78nzOl779ttTvbaR6nSsqyFI8tdaAJRh8TEwwgbffhsfZxuZKj/9JeXLqSJamcIO6BJ+Da3iAnAg6pRMaJJehCnkGFyK765EjBAyIUN8HCZh/UWYX4D0LlqZ6MX6kxEPIgYiRiNyEZcgLk5hAqIGeV5G7MA2prJ2OP0Q5iqvwnnYFyA2IqYh7pYmwz34bpNcCdNZOfa1DtsIY/peLL9f3gEbML0Z3zexupwy/slwIb4vwvRd0uREQrkNFCwDTPdiuRv7v5ONGWku9r9AXJA4julCbPsCfL8S6SSkE1Pj9fL0h4yHz5XNcTVLo3yWYvkGxHjEWsQlKB/GX4p8AczfhmkjjktDakJYRIAsrFNFz4MY0mLsf0Rq3sDnjfM4PSccPx/TD4PJtOZM4JjYvD5BvIo4dMbYzsVtZ2EB1AoD+fqxOZsRQ+mrMBzlEmfzkj5KfM2AmncY57UPIYkzYIAKiR04zmppD2zGfBmiimMBEHErzBO+xDXYAzfIG+FnWA50AOIk5NDPwS/nQAXKrxHbvxgxE9t8juvDDDaGxOdIA+JH4Me2WhBzse+X++TEZIP5UbiujVj3FNsRKNdbEHNQBpsR17LxYf8lTOa47l+TyfGfY90j2E89A/YZ4MC5J9cVFiH/fGyL8H6S65CkCHw/F2X6C8RBxDNsDH3gepYCb2sHCHRH4h9I0xB+xKuIDUzfEC2IdlYH+zdgfQPXV9QZpptMP5huSC9yXZ3Axp6cA98La1N75mrkvwThQ+TLj8OlKeRjXSaf6Uxn2X7pa5vpFtPrPsp1+kqm9+RTNk+mU2fQu6UeGMfGwPtF3eqjbN9hu9czKrj4mLYIb8F6prNM3/ookwvTNbYf2Z5I0YYz5lqU2iNFyN+P6zrqYh/tk8Vp+hpswTYnyxtQTz+DMeK7MEb4DYyRrkd6J86vG8twPuJbaMMiMFbtgQJcy7HIe+85dDOD8haZi33dIe5EWbwF93O5vkWzxLeIJO1MfCIBeVnaSZfy9L/Qc0F6ku8YZTjz3f+2/P8O6NvSTpiF6U+ltxIJnM+dbE8on5FSRLCPYnkHog1RqEbIZvVK0qVMApsM8CVinqjDEEmHCrEHqkUX6CinHCyfJJ/P7e56bP9F8hnchuu1QnFBWPgEbSP2Rd9G/4Bg7SMdfYYenaVz5+pSH+3T13Mp0xlmd5FKSH247/Yi9iHeTeHPiA9QH6/h+xd9A7PP3D+gjUbcltTXxPHT+vkybEV6e59+nqOnhefop3KuXp5LmW9h9p37FtynOI7b+ubP7COzccxGMjvHfF9f/XPpGfz3oO34PbfDr8KU1L4uQJQiSrCN/Sk7sg9j0y9xj34sv5HYp1Qn9gmvJPbJ9yYeVa5MvCTvSWzFeRec9qk9SVvG9lOfL2VyYn6xz49KuTArZc+28LrYP/ejk7kdAPl63H9zYTq2+xvmV9k+FLbivkN5YnvLxe1wlfgBrMexW4VdyXJxAoxhNlFcjGksR5vO3huF9fz9ePEfsFgswPR2pPeBXVZgsfws40m8yss+TL5jZdIU2IR6VyKuhoel3dDI1orNg5YnXmFrj3ver7bB/QqgDn8AW8Rvcc49OMcXOb2P6xPj7Ux8y+anDAWPJOD8WB0E48EzYTAlj41cFj1cRvdwHUZZsDblN3m8AdJhrL8NblQNsEXNQ/v0FfgVtCW8r91wsapzuYvcX/8N98dnqGOTYJXkTPyT6//jiYTwLe6hz3B/MRB85wKf9Bnch3tpFZdPkq5l+0f4DFxMR3B+E3k88Rnq+CNwrbwT1sk9qHdvoS94C9ftM5zLlTAY0xvEnYnvsO5IbANY31g+jscnzE/piUNsvyg94FV07B/rsDHw+A/7FT7C8d4Fq9CW1KifwUNykMU1hKDu9UMMSILnlyGWItYlwctsSUpC2MaNvHwmvER3CBT1m71/Wfw57r37oEZ4DM9GszB++BSW0xJYKYxBvTuOPkNAPsyLRZAvHId64Rvuf1ZKBqjg9dzoxz+GBrEJ+XtghtgBM4QEpr2Ie1AfkU/qginS5RhnXYbtpEAHIY8GDfJaTJckHmf1eB/fJNwM4vVQxvnOAB9rH9iYHzxjzPegbG9CfWDjZT9ZesZ42VhPjzM1xh8aH58naxf5eJ0/QA1A4o+InCSNj6O3wU5EO30X4/AeWEo2YrCyFerIR4itKTwBozjdjRgHdeJSsgrRgBDFpfAA0mKknyLeQmxF7Ef8VSyHW7HtZ5B2snMBA/0l2i6k+P4RxAHEn/renQnW1w+VnwnxL3BWXiqDZQy0CGPCIvjX+g9AVFyCdrgU5YkQFkMDg2yBeYoK8+gHWM5s0jl5KR82ifOg338bz38DeQ1KuQyT0M+cY996IHX/D/DHM2iQUdxfxcw//5+O8X8LXN9liCu4/NuhP9ehj1H+CmhkP1xGjqD+bYWLGFL5Fi7PB3Dfp9YJy1fx8nPWD3VlkDAe9HPLMb2coS9/7rr+tzy2u+tM9OlBH5QyjEUQ4p+wPuLcPPqDFQwy07Einr+RoS9/ut9/h4kQRTnVIQWuY+fkZRssYqCtmN8MTM+vZjidn4hx1cSkfjKgbOcwoAzZb8SxsisYUHbAgHVvYThDro1Mrtgn44W+9enT83PXh41LfB7rHcWYeSL4z6Wn9TtlL87S+XFJfT+dZ7bko3PqfL8nvt8buFf+XZv/fwLunVcQLyJe+H+7L2ZlmI2wMTvxBsYbMYxVH8Iz5q/hNoDeVQDfPQNwairaITxVn3oCyyZhOhfp3xBeLJuDFL3Rd6hlp1Ab428iXkW0i+mwJBVX+jA/Msnb+2iqvZwkP+P7FqOd7wYl+b9bibgP079FoJZ99xzSu5F+hfVjyNeEdCmWLUcaxXwDog7zr2N+GIJiegjiEwSO8xSGMadKkP8BxGIWj/zAOfT/Wfpvzh//U5q8A4BmHnPieM89Q/yPad96/hd67lmjb/3/G+07S/wLTckBY75XGM44+/zHM04fxfX8ZwpfIr4QVyd6MaZUeByNsSyPuVn8mKI83n6Lx5MkdafIKYudWfzKYmcWvyLdivRW6TUczwK4iJ3z2bhSF5VX/X+AF/49KItGmGU/hN5tbxLy5CSUQkTH2dC2ABhx/5iWAJhHJWF5HHf25wD25d8jbfb/DK6JSbjXJ+FFr+BDpF8KkMEQT6KfN4nAbwCCTwGEcH9nPZhEeAVALo4nD+eR3wZQ8Oj3iGT/iB/xI37Ej/gRP+JH/Igf8SN+xI/4ET/iR/yIH/EjUiD8M8e/QxVcDhJQsEEJ++RWHm27CgSgB4QtYCXsB857hM2dNmeZ3iXc22lNK9NrbMI90ICgEBNGQw+CwjxhAyxDUKxe31E8oKybJToNljIb1l8LQUQbQoB2fBKe1xGs/trONDdr/uYOq53z/aSjNJpMdNq8ZQ01TmEJEGGmcA2EISAsRdoP6eVIM5FOF2aAmY9T77Taytqwv2qsXi24oABf1whuKENaK/ghnVdb1GFJ9rOoI7+wrMYgjBC8vIpVMEMUqSooHWWB4D5Bx5HqwqpOzcjGt6rD5io7INwqKODEWm1YyxOwHhAMUIJgM5nYqZnL1teYhIk4zYkolgCOkcA2/tSFazqwIexvpJABbnx3pZAJLqR1Qr8OV6Bnn3AXr3YnawX7G9ahDmSk02wp66nRBPZXT2LC7Sjx23lv6ztzB5dBTa6QD6UIikJdhin2N1dswhpMrcFlWoNLswaXZg2OYg3IAMJqfLMa65QIN0CrcB2sR2zDtIhNujpQgt08kZ1f1i34BC9KwrYPZUew1N+pWdjIvB2ONF7N22mylFUfEBbAWATFwS/s9HjL5u0TCvlUijq96YyhtUMzoeg8ybVARjdbgwNChtCPSyKTSyBWE8A8AasQAEJfoYeYdOgb9C22vuw/QuD01yn6aor+NkkTPfRQJ/aid9HfMXqkJoN+hI1Npe/BNkxRuo8+B6XI8C7tYqOg79BuqEZ6GPMzkHYjHYh0b0fopUAX7epEgmO/r8PsZpOlz3VESlKJQE4q4UlPJRzuspoc+ix9BjKwid8jzUb6DO2BLKQHkXqR9tCF8BLSJ2k5DEW6J0Wfp/uZTtOn6VMwGGlnh4UNIdahMLKrQ2bkFx2QzDWUBPbTX9Cd4MeqT3Tk+rF0e2dudsC6D9sj9BG6sCMz4Kgx0J+RRvIlVmqHw4yCgz7YUcEaWd+xPxjopuvpet1boefoxfqjQmlOaXHpo0IwJ1gcrAg+Gqyx0dvRNGyjuGHpWnxWQJCi9iB0xHq6ukOsiNX04pzYvCi04bOdp1rw2cpTgE/b6bcneKqa3gpjERTbWIpYhmhD3AQiPm9A/ATxU8SNvGQhYhHiOjQfrcjRihytyNHKOVqRoxU5WpGjlXO08t4XIRhHC3K0IEcLcrRwjhbkaEGOFuRo4RxsvC3I0cI5GpCjATkakKOBczQgRwNyNCBHA+doQI4G5GjgHDpy6MihI4fOOXTk0JFDRw6dc+jIoSOHzjlKkaMUOUqRo5RzlCJHKXKUIkcp5yhFjlLkKOUcQeQIIkcQOYKcI4gcQeQIIkeQcwSRI4gcQc5hQw4bctiQw8Y5bMhhQw4bctg4h42vzyIE4ziCHEeQ4whyHOEcR5DjCHIcQY4jnOMIchxBjiP0ut3CoZpfIcshZDmELIc4yyFkOYQsh5DlEGc5hCyHkOVQauoLuTAoqs1SxDJEG4Lx9iBvD/L2IG8P5+3h6rUIwXhjyBFDjhhyxDhHDDliyBFDjhjniCFHDDlinKMdOdqRox052jlHO3K0I0c7crRzjnauuIsQjON/r5T/66WhN5FGFZ0rbSMFnC6DzzldCoc5vRF2c/pTeJTTn8ByTm+ACk6vg1xOsT1OF0JAJR2BCmuNG03AWMRUxDzENsQuxEGEwlOvId5HJGi5niValbHKNmWXclCRdilHFGqVx8rb5F3yQVnaJR+RabAmnZq5HUXTAnfw5zJ8foFAJ4LPap6qplHsN4p2thy/ozSq248HvygkrxWSg4VkVyG5o5DUaPR8InJLF4QKigMnjbopd1jgMKIiN28YWqbbn/rcE+jIHRToIvuTpECPIP0csRvxKGI5ogJRhihG5CACvKwQ6zfqWakm9yPyECFEkHUBbjcAOOyq3k3N5NHOX5mB/dHxjrx85NvXkVeKpKsjbyySpzvypgdqNPIU5LEwiDyJK7cT6a6OwFF8/USSPN4R2Idke0cgiqS5I68/kks68l4N1JjJJAiIjHViik7AeTM6viMwGauN6wgUIIl05OWy2oXYUQ6+LSCNcBRpToorO9lTuCMwFElWR6CS1VYhjy08kaGYD09CMCp04oC+6CaNItGNgeOBuwKfI/tnKFhUj3eCXSKS13K6yGTdENhf/ABWrgl01BhYffQPu1M0xuiTgUdzVgfuw7ZIzlOBewP9A7cXd6lYfBuOezXvoiOwPNhFd+ppgbZAaWBh8dHAgsCFgWmB8YHmHCzvCFwa2M+GCU2kke58KtCADV6As8jpCJyf08WHWBe4PqAH8gKVwf1MvjA42W5F8X4mAShL9l6E8i3M6WI6Pqmii9j1QuWEsl65RBmuDFXCSpbST8lUnKpDtakW1aQaVFWVVVGlKqhO9ktfEfaLRU6Z/R0bkEX2FHnaRtmTJn/viBKVwoUQSxPqaf2E4aQ+1nM51E8Pxk5OCHcRw7gpMSk8nMQc9VA/cXhscKS+S0mMj1VE6mNKwyWNuwm5vQlLY3RVF4GJjV0kwYpuTWf/h8duArfelt4NhPhuva2pCbzuxdXeascwe2Vd7Q88WlLPM/4koPfMZGZsY/2ExtiOzKZYGUskMpvqYzex/+Gjm1qpeWRtN7Uw0tTYLbZS68jxrFxsrW3Cakd5NdRmC1aDPEawmjocgqwa2pPhrBquUbJeLrJjvRAjWM9ghlxeL9dg5vVEwurtPhwcWbs7GOR1cgAO8zqHc+CMOqgxyFu7OzeX1woHSSOrRRrDQT6wAt5QIIBVigO8CsG4jjcUILyzWMn3VXJSVcpPVynnfQnk+zqBZB1nfl8dZz7Wifwffs0cHiGdAxYtfY79pykt4ZEzES2xtYtne2Nt04PB3UsXpf43ldyW6ZfPZnTazNii8Mza2NJwbXD3gOd+4PVz7PWAcO1ueG7kxMbdz+kzazsG6ANGhqfVNnVWVzXWnNXX6tN9NVb9QGNVrLFG1ld1zQ+8rmGvq1lfNayvGtZXtV7N+xo5h+l9Q+NuFYaz3xbktJMaDajDLemhpuFuW+swptDdQ0Pepel7RSDbwRhpipnCw2NmBHtVXFNcw17hPmOvLOx/xkm98i4dGkrfS7anXtmw2B4eDn2iBVaJ/U2A+lhowpRGpioxfdoPr9kC9sVfe2HknFr8h/mFHPh9Zk1Y8INfC3/oa9GiRQvYY1FkAUB9rHBCfWwQ+wsFioJdtdQ2YVn/vjJB4GW7NW1kV6IHX0ZwEGQh646lIoT9HTHdgKcuhbbL7QplR4WFnf7MsnkH0IMvQ+A5jl7XUcLPy/S6zqwcdn5Z2FlSnqR4PmW0wx8qY79iWoGsjOYkqW4vxsT6nPXF6yvac9qL2ytk9oe3HsXCwKPMlXaUPCrAwsiCPkFgcmETJP+8Gfb3s46MTN5xO0tEIk2RBfw3P+FcUUdSv12KQj8t2AWpVhfw5hf2LUiyfAEkKydfRhb1MS1KsfCXizgLJv8vnN6LwwplbmRzdHJlYW0KZW5kb2JqCjEwIDAgb2JqCjw8L0Rlc2NlbnQgLTIxMC9DYXBIZWlnaHQgNjk5L1N0ZW1WIDgwL1R5cGUvRm9udERlc2NyaXB0b3IvRm9udEZpbGUyIDkgMCBSL0ZsYWdzIDI2MjE3Ni9Gb250QkJveFstNjI3IC0zNzYgMjAzMyAxMDQ3XS9Gb250TmFtZS9SS1hVQ0IrQXJpYWwtQm9sZE1UL0l0YWxpY0FuZ2xlIDAvQXNjZW50IDcyOD4+CmVuZG9iagoxMSAwIG9iago8PC9EVyAxMDAwL1N1YnR5cGUvQ0lERm9udFR5cGUyL0NJRFN5c3RlbUluZm88PC9TdXBwbGVtZW50IDAvUmVnaXN0cnkoQWRvYmUpL09yZGVyaW5nKElkZW50aXR5KT4+L1R5cGUvRm9udC9CYXNlRm9udC9SS1hVQ0IrQXJpYWwtQm9sZE1UL0ZvbnREZXNjcmlwdG9yIDEwIDAgUi9XIFszWzI3N10yOVszMzNdMzZbNzIyIDcyMiA3MjIgNzIyXTQxWzYxMF00M1s3MjIgMjc3XTQ4WzgzMyA3MjIgNzc3IDY2Nl01M1s3MjIgNjY2IDYxMF01OFs5NDNdNjhbNTU2XTcwWzU1Nl03Mls1NTYgMzMzIDYxMCA2MTAgMjc3XTc4WzU1Nl04MFs4ODkgNjEwIDYxMCA2MTBdODVbMzg5IDU1NiAzMzMgNjEwIDU1Nl05Mls1NTZdXS9DSURUb0dJRE1hcC9JZGVudGl0eT4+CmVuZG9iagoxMiAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDQxND4+c3RyZWFtCnicXZNPi9swEMXv/hQ6dunBf6SRGghz2bKQw7alScteHVsOhkY2jnPIt688bzsLFfgHevKIeQ9N+Xz4ekjjasofy9Qd42qGMfVLvE33pYvmHC9jKurG9GO3vu+E3bWdizIXHx+3NV4PaZiK/d6UP/PhbV0e5tPp9Pa5eirK70sflzFdsuKaX7+zcrzP8594jWk1VcFs+jjkq17b+Vt7jaaUwg/x9JijaWRfo4Nu6uNtbru4tOkSi32VF+9f8uIipv6/Y0uoOg8fv1tWNhVvUt2z0rYiNY6VroZErHQNJM9KZyEFVjoHacdK5yGdWem+QOpY6XYi2YqVrodUs9JFSA0r3QBJ3IEEj1b6BgndW+kbJHRvpW+Q0H0ORElBJOdY6RGO86z0uCv7UnqCtGOlRxJOLgb9+/WIRegRjkMsQo9wsnulP4tEFSs98iJJCvTIixpWeuRFlpUBeRGSEgbkRUhKGOCRAisD8iIxDAbYJjEMBtgmsQKGnTzdf290e8XbgOlQdPdlyfMiUyhTsc3DmKIO6jzNW5XJX/EXs9zy6wplbmRzdHJlYW0KZW5kb2JqCjMgMCBvYmoKPDwvU3VidHlwZS9UeXBlMC9UeXBlL0ZvbnQvQmFzZUZvbnQvUktYVUNCK0FyaWFsLUJvbGRNVC9FbmNvZGluZy9JZGVudGl0eS1IL0Rlc2NlbmRhbnRGb250c1sxMSAwIFJdL1RvVW5pY29kZSAxMiAwIFI+PgplbmRvYmoKMTMgMCBvYmoKPDwvTGVuZ3RoMSAzNDM0NC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDIwMzI2Pj5zdHJlYW0KeJzcvHl8VEX2N1xVd+l7b9/uvr0v6STd6XRnaZZANhqjuci+hz0BIkF2ApKwCQgSZAkEENQRwYVNVECRAAED4hgdBleEUUHFUVxAcYkyM8iokO7n1O0OAjPv+3ve5/28/7zdObeWW/dW1anvOXVOVXUQRgiJqAYxSBk3d7ZvQ9X7cyHnSYT4nIlVk6bfm7HrF4i/ixBXP2na/In/Mn8LT8gLEer2/eQJY8e/HbmyHqFBk+GZgsmQYZlu3g3prZBOnzx99ry3t89SIX0MIdvWaTPGjcVDJ11BaOJ4SO+YPnZelfie/AhC9wlQ3lc1c0LVD3uqv4J0JkJSC3cEuYE83HPIzYaQC6HYt0AXaRidErtI79OQfA9PNyYIoZ1oD56C9qBX0ev4Ejy1Fx1GDehN5ETdoF8L0Z9QLeLRSMhZhQbDl4P8P2F3rAG1R9uAD9vQCSg7At2PjiAHdsW+Q4vRcuYDeGo5MqA01AWVoBloLe4Xm4NGo3PsUlSI+qF7UBWuiZXGHow9HNuBnkGHmTdjLUiPPGgcfE/EfuI+jv0dtYUnHkWb0Dn8sHgQqVBLDZR8Cs1EjzPlLI5Niv0OLfCje6ENLOqPTuAmEoa3T0DfYhdeyHSFtzwdq48dg1JeVI4mo8fREZyPexI/NzrWP3YCOaCOefDWTWg/OgTfRvQKOotl7lJsR+wScqM2qDf0pwG9h5uYaMuSaDFwjAMuZaEI3JmB/ozeQKdwAL9GZnAy15FTuQWxD5ENdUDDoLXPwZPf4H+T++G7mDnO9ojdiYzAl4cot9Ff0ZfYg9vjgXg4ySIzyGZmJhKgxg7wHY+mAL83wts/x2F8iMjkJPM0+zx7lU+OfhEzwoiE0BPoKfQaNkBPfXgWfgCfwV+TrmQMeYJ8xfyJ3cW+rxsLvb4LTUdr0fPo39iCO+FBeBSejBfiWvwQ3oRP4FP4IulChpJK8jMzmalmXmHvhO8Qdha7lFvBreYvRkujx6J/i/471jG2Ag0CPCyB1j+KNkPPDqOT6BP4nkNfYQ7rsRG+PuzHw/B98L0fr8Xb8U68CzdALafwV/g7/E/8C75KEHx5kkT8JA2+ATKT3Ev+RJ4kJ+F7ivxIfmOcTBoTZvKZIqaMmQGtqmXWw/cg8yXrYU+yMeBzR24Dt4XbyT3Pvc5d4mXdAwIS3r32dEt2y+dRFF0Z3RDdH22IfYnsMIYe4EIqKoLWj4XvVBjvDYC4vegDLAPvPDgb34H7AWfG4Km4Gs8DTi7Dj+NntLa/iI8Clz7CP0ObDcSrtbkdySd3koHwvYtMINVkPXmYNJAz5HdGx+gZE2NnspmeTDkzgZnNzGc2MPXMu8xnzFfMFeYafGOsxKayaWyIDbM92THsHHYz+y37LTeae4e7wEv8dH4F38j/Q1egu0NXohukK9et0x3SfShUADr/gg6il9ANH/wFs4TpzhxED5Jc1k3eI+8Bnseg8Ux/AkglO/FKsgg3kHRuHn8buQ0PQJfYEPD6ONlCrpDbmP64Lx6CppIO8bfxNha0ESpi/4Ka2aPQt/fgzfN4Gd9PfuZltB8jEoE6/8rksGHmHXSWOYd17Db0KSthJ24mzzElgIJX2Du4UuRnnkQvMtV4ETpIuoN2uiqsARwPwLtBLwzFHfGvTAwxZACgqJD5Gi1FleRj1AxyvBI9hsezk9CDKBcvRN+iZ0Eqsrh7+Gzejt8iU9g6YsUNiLC7oHcRnI4ZzoaW4XLmcf5n8gmag06yEvqceQFaf5K8yPRnL3GD8WSQgEVoBaqOLUHzuVL2fTwJMXg4CrJfgHZbyHRk/RAuBq0yGnTaIZDuI6AHujD9IccFyOkHuBgGGuJx+G4EPcECgqaAjI8ALfYeauCHkkY0iTNi0DoIse9EB6ORsWfRptgkdE/sYdQW9EFtbCG8cSe6gNahnXh59D5UhVJAcj7H/bge5CTXI9aW1JFPyBCy4ebxBW4HsQt9D98XUQ90B/cyqmM/QkNQcWxN7DSgOxM07CZ0N+qDzkMvf4IaejFNKDc6gOyL9WCqoL/n0KDYc7FULKHJsWloIDqKntFxaKwurHbpohbfcXvRbZ0jnQrz83I7dshp365tm3B2VmZGKJgeSPP7UlOSvUket8vpsNusFrNiMhpkvSQKOp5jGYJRm+6BHhW++lBFPRsK9OrVlqYDYyFj7A0ZFfU+yOpxc5l6X4VWzHdzSRVKTrylpBovqV4viRVfESpq28bXPeCrP9Et4GvEIweVQnxtt0CZr75Zi/fX4uu1uAHifj884OvumtzNV48rfN3re8ydXNe9ohu8bp9e6hroOkFq2wbtk/QQ1UOs3hmo2oedd2AtQpzdO+8jSDBAo+o9gW7d692BbrQF9Uyw+9jx9SWDSrt3S/L7y9q2qcddxwXurkeBO+tNYa0I6qpVU893rddp1fim0N6g1b59bZrq1jQq6O6KsDw+MH7s6NJ6ZmwZrcMchnq71TsXnHf9kYSXW7qW1t54N4mp6+6a4qPJurpaX/3WQaU33vXTa1kZvAOeJcEeFXU9oOo1wMS+Q3xQG1leVlqPl0OVPtoT2qt4/yYEutOciqm+ejFwZ2By3dQKGBpPXT0aPN+/3+NRD8e+QJ7uvrqhpQF/fXFSoGxsN+8+G6obPP+AW/W5b77Tts0+xRxn7D6jKRGRDTdGJly/p8W04jTWd/B1zmLaokBvAES9b5wPWlIagD51opcJnVDduE5QDD5lGJ6qHw8jMqVe7FpRp3Sm+fT5ei6oBHx1vyBAQKD5x5tzxiZy+KDyC6JRipPrUIP7rfH6cLg+O5tCRNcVxhTaeIeWzm/bZm4jCQSqFB8EwD5UArwdW9a5PbDf76cDvLpRRXdDor5mUGk87UN3J+1HavtwWT2poHeaWu/Yh9E7Na13rj9eEQAkNyBqiNrrhdD1P5PisHaf3LkeO/5vbk+I3+87JNB30MhSX/e6igRv+w69KRW/3+n6vUSs3tq1lEkiiRhJYrS7AMrR1wvTRKlczwbhj9dAPb5RJwAqtRzs61GvVPSKX8skv/9/86HG2CX6lBb88ViimfWdwzenb7spfVPz5DoGGgyTYN+hI+vqpJvuAdTiFfZOBIB4NLTU7+taj4aBZAbhrzHW1IlSWVK9CizrSgsA/uJZieRNBZMS8TL4UHS2bdMDFF1dXY+Ar0ddRd3YxljN3QGfEqg7TF4nr9dVda9oBU5j7MjqpPoea8qAV5Nx57aIYM345BBYszp0ZwPB53ldI9mkWhHHnmeQpGPPY+QWeO48YY7CpC6CidcOucLKlaKWogHK5aL+LUWoGOLKNbh0yPGb/eYgXDBMadd8TNM1lUNXkY9tgmkHLYwOIhXcB0hBt6tShgkjxaITFKUR5x5AW4wChKpZt8V4F2IUxscwzAvmp9bQqspbrjQrV5qhnmKoApfjEDHnFRYU5vI6+NoVjM89+l7/kUeXzM+4PRDG4eigo/hXbPzpbMvVU2V1G15+JZoa9d1Sv5xJMhUiSgpGFpG2QNrCYNoCE9rC3GUyphqJ8QXLf6/fGkDmvIwQfHMdMI8ppGUJDofTbs9YsOToyP4no4PwF/jLo4c31I18/2rL2Z+i/4wKUPtMsIA6s4fAI+mkpqJ7RPKbwNzD6XjxHomVfuPwPcVg/hHilkeMdIWBteX9Lxc1Fynni4pQ+8vA3MsdcoJmf77fnGv22/1mgqPVeN1uvC5a3Ywf3knDndF7oJ7d0c/xUvBeJDTgoATD+jzfiEvUEGaKCMESLkISYSCB+E66zgPBspsBdspWgMBW/baN0NvL5ZfPK1AvDCu9Ks1KSzM2WyIdcnLzc+02XpdRUFB46ETJiI6RAubEierVof7usaOg3i64kUwl0wFJbVR3FaliSH/cH6oMIOLhqqCAm61aS3t2vlz5BrXv39whB1UDM/P99i4kCzcePEgxcgQutdB6BgVVF6GNLYo3cS9it8L9razWyivl5TAezfFGHTlx4gR9FrxSEoHxZdCQw4iJfb7fFiGNsc9Vny3yGIMJs4XZyxBmLsI2KA3QB3wzFxG5COO+CypnDyyANxcpl5uV+FjXcu3C5YuUY3TMw2E7zsV41/poqZv78Xd4A0HDYt+yZq4J8JSMDjbwPrfiBXWyn/j0f4apzAFkATLFvlDvZvlaslK/0vSWkRN1ehfpbu1n7+PumjTUOto+2j04qVJXqR9nnWavdFckzSf38nP1C0y1/EbdBuUt11lyhj+j/9Tk8aSwnC3FYHDOElV/IC9HxEhURCKuTzXPQqARVCPk+sCpJWh9yhurNTaFAbnl1WHKLMouXF4NTmsn+sFAVsVSkNvR4bAAiPlAWkbIqjhyOxaYlVAgTccPq/xg69z9s++c+sG2D+c/dHjXwoW7dt2/sE85+QCz+PYXxhyIxs5Go9G/7Nn4En4q+tjPl8APnPrTlBV0LM4Bg64CbyQ0S/UxqsGcV8kuJuvIJoF9gcUi4jnCiByWCX5b0tpuoT1CmMqpR+ZUgymPa+1SDod94AYTzq0/govwchRHUXU4rPUtroOKnRFsjtAeovKwP2DmeV0+IDWXXG3o8sHQx75qP5u9746FqS/2fHsMbV8RjLcO2peCvlULbuNu41/mXuVf1r0hvOXV9ZbL5KHGSnm8cYFlgXWV5ajlgudC0iWP/Kr+JStJUrxKspKi8H8Gd14HAyxAKMYuqZ4USRF4/m2vx+b1egSvBzAneLyMIUVpJDsODDRjcyN2HTSk2DiU0kheVk2YyNIs5wfQHjqe+GWyBPmQgjupsvlgMbjdM8hiwpIjJB2l4nX74gMK6LwSpiDV1G5xc0v5ebOF9h0utcZ2YSMANi6vqHWUO6FyXD4zaPeHCoEjBQX5eTC8miDD2INIgxLldazuWiFxBp9+/Oedm+574El82Prr3z640uu517ePTtmzp0vRuKb7j12YWPnIk3XWk598v6d099EdK8d2AE4Oj33DOoCTYfSBmskZHIbuhhUGtrt5hHluEjPYMU2ZahvvmGOYb1thqLOtSnrGIHE+phGEQq+XDUZWhwMGGVMGqfCylzFdCDHg/AZZtrOuI2QHcpPJaro9xcuxKVkGy6wxvhk+4qvRzQppMhDCKKSESGh9W1cj7rTf/QE+gjuBJmhS9X8IQ5tG/PC+Vnm4nJCIy+VxoWgB/kXag5qjjIzzEVAEnAMg4WproYNKhMYyXeH1aCv3KPt09IoCaaHhDamPVi7eu31Rbj+bRT+rccXUKWtsDf7vX5z3duXE8Q+sj14881oML3Vtqq1/YOE222Yyb9G4B5Yt8x18Y9L+8WOebJfyyoNN0V++gRZ7AJ0KdwSkx4AuqwWWUnmy/Li8S35L5vox/Qx/YhkLYAvJPKPjJD2jQ7JsMLzNsDaGYRkDIrKB1TEvk5eRAFP8VlVCLAtF0NsS20gmvsRxkpqcmic14kLVoFPTAnm6Gn++br2JUIkzGGx5iCjERxhy0NiI12ic+7EcuBcOXwbofaNo8gaT/5Uic4RyLBKpbRdmAXgmkwl4B5bXYWQA/WuJGBpjH6r63AiT1jbCsMnJRWH4lAFnoYxqk1V9RK4pichqKCKneSFsGwlrJcCEyMe55lx7wMyYMdnQsow89cjx4w3RfDzmGebQtT7PRLeBaDzaUgmgoXrYzz0Lsvy9mtzXMz+5LnmD9TnrX+Qz8qdJgmh1GbM9jJjD5eiPgLgyAD3FKtktVuvbRpPNaLUZTQbAn2o1Sil21bgVZn6jSbVju91rATF9ycTiDyg2QXjVAJviNZjHKDOUxco6hVUAhy4Nhy6MXIqLuNb7LEdxPjLhRwHFnfYbD/43PKbejMc/EEknXcBhcTMgstwMBNPv+VqhXZgD5iJNqjWBxtXlNwIT0GgFm4ABRCK7TQeaPDTsFfumaQ807FkzYk3mrgfJJy0vDVz2UBMWZq+9/GYLrlHqVh/b/vj+gcUO8o8XonNHR6/87Y2H9n9BZ7X+wE07yHMyykb71YxKN+6mU+3d3N18Iy1DfZXMeN14YaplvG+2MMe7XFjhPSN86DDrQKAbMnwBn59KtjkzRTWUGAhAKQl/MIbyDoRY5FKSuLQUmwHm206qHR0MzlI03oENpigKUda3kSizUnBElYqdY5wznIudrLORpB8IJ2az5lZOJURXE9n25c2tbKEiqwPDjM5evI5KqIXqt0AaMiuFVF6x7QauMVcPuNr0rhzeZdjdpMvRSQ0t955a9mX0/FOrLu75rKVw4IMDZu7Yft+C3ewQ49Sc/jl3/PT3cRXRf79f13w/7osX4l2v7Xz92mflu8saN2/cu5fOKWNBah3ccyCzVarxmAGz8EcEVgSJpOo+h2BWlA2zGIbQbg/UFDxDPCZhlvgDGojH4DGEKYZgBl4ME6zbmEAJtQWri/pfbh6gXKGzHbVOqO6PmCNxRU/RAFYUjxheFyiwWArHMgfXRJv7FpgOMw/8axX7+541j0Yt0auNn+7B3+M3nqTW/hAYZTeMshMFUA46pxbmO3CWo7ejd+gb+bscTszBi9AivJCdLVTrZ8pzDAucq1EdXsOuEJbol8krDGud75qPWy1pMNz7vT4PDXy+9jRo6wtRDKRk+WSU4kJyUkq7re1wO4s/hecyUyyGlFmvilhsJJNUJTzLpPoAAOAKmBQTMTXihw51dM2qB/sU7u9Pn2W/btXYVTuxr+9w3aoB7U3Z8ocat0TK2zcntFECDnFIzKwGMzMUys9LzHat6hpBjtV2AxpuhAaeWjXtm1ebvq+cXrs2euWTT6JXHrp7ReXk5asmTlrZuff6IUt27nlg8XNMUtbGqVvPnts68bGsNsdWHo0hjJvWvYaHTl62dMy42mXXYv3XD3y25oHdOxPWoluzOLLRn9W8zp5+DjUwyjEiMJGZ5pjumRRY4FmUssazOuVxxy7PUc/3jm98V3zW2x2bHXscTOes8TzJoOorALx1+X28LzNloHEM1VVekCkOf1ASF7MGqqFSj+AI0oOUmW/RTm2o7DVQ0TNfZ61ZNRPz+vAbt06QzTcqo1YRQ+XUZtd0zx0kPy+DCheECHhrMWtGZAhrHLRrrK3a41g4dsiikgJc8PL0Q9ew7vi65vsW/GP7C2fJO8/Mnrd/18JF2/AQZcE9/RZ/XCW7hldi4eNzWHk8+jV4Td9GD7z4KpP3xKFjT66hEkbQYRCzFWxI81Y7qT6WQ7xOJHwRyxRhngVfoT0qRoTakNuEhDdTTaUFLHoNGBo2rOAyMECHwW1gyk6cuPYcuA8EzYgO0p3mTqOeaAT6tzqC9Ss+h98fzDfkGrsbe7u6+Xuk9+jdc/hQ44IsoyOYhUNidnIoK99TEOkaHO4qSx7lH541vHfZ8AmuCcGJWXM9C5Jnpi93LfOsSV7trw25jUqJETFDqCKQTBk5+hI90escL5NeqCvqS15u6NqZkVLpdNMZ+8JVYRI+gvujDPLyofa90k06DF75UtWklNyB0i1bTek5ShUozCN4F0oimxuKO2WnQ3kRBchmVfTl43x36Yg1CS+yuYVOMeXNl1vOg43TjNo3N5eDwJwHnhSXn4fBTWgRaiQG6XBSWdHcAmdhLhMfx8ICS34eSQ+kscRus7C5vnRwvnk2kJaengGlCy3I35GlrrA292SEsC2BD0CCkbCrumwbVLZzytP/nDlicyTtwPqUrOT84TOXPx/dc+L76KLTp/Ejv2Ae3116MPfX6O5/fB5dFf2169DxC/BrWP0Vr5459t1DH3cfZjNEHQ8M7bSwulftWLV6qvp031GTP16yBRdvHVX+RMvYNaakjNtLsGHdczjtxU+jk77/Jbp5V/39U84unnnh0Vc+vfwZNmHfO2/teSf6+ZdvZ2e4cb9VG7sue2fiyg1d1r8H2Iq1IMSVgc2lQ0acoo5rr+Qok4TJYoWyklmvvMUd55uUS4pe4MrwcFKiTNbXK/+S/2X4l1FkZdbAGhm9JHIsCyatwOt0MsQFXtaBU+rTyTbIIAzjY2UblBBTOE5I4Rm+kVSpIhLk71SCCTmC9aA+9KpF9qEJOmZwCXuSPccy61nMNmKs6kvkJt05mVkvY5mmFZPupI4s1tXoiO4R05mP4mB3A8GfCwbX41ZAhF3FRR4Y7CLq0jdThxYMidp2rnDCS4A5JFKrHDtmPHaslouHIOh96/VD+tanDBpZ2sCaGEF3BDwcFPuVyn8ZnlldHgBXOMD4GaufCWXwOobk/o2UfvZ8yxPbPsH/2NQjzZvLHfm9Bz4a7UZG4g2H7127GiR2A8yO3wF/zZplsUQtYdkegeGBiYFZ4jKRn+KZw1WJs/RLuaV6PsMhMq6M7BRHsihaLSnZ2VlZyJucAlxKTUkxI8EV4ocGQ7KnTXKKT/MYy8O3jda0l7YqdaV/c6t7BATaqwgs1kh7c4Ta93HzHgyFXLP/BvvdSALY3zHuHIUCYH52LNTwC/ENJLTznVkTJy1fN6LmtTXRR/DtSzr16dvjgc3RT/H0u0JdR3Ye+uia6B7uSNnhCXc9m5txtGbSvooOzGCzY2L/3jOyrm7VyZ0qewye34HaCBNj33JzuQ+AB41qxTgyNRm0VUfDOFSFZifXoGXJ69Hj3PPMM4bDTIPhDcMpdD75X8lmoyXZnJzMZPOZ5myvL7WnYbhthH24ezJXmXyfZbXlcWaT8XHvTryD7DSfNlqRDXkUm+Jh6eLH/swIppo+IzOimBBmk6wpMpOUwopKyNQHhXwYY0+qM+QTsOBOGTe6VWcAE8v7t5pZZo1l4XA5XTzAM7FTk3zgjiU9F8ReF6L6nioGqvHZhtdvj/7lQnP0oyf24q6v/x23ue3V3Ncf2fX16OnfrHj6K0I6/Hz1NXzP+xfwsH1fvNN268Pboz8/9HL0u7qjoIM3gwyOBIyYgD/L1JAvFXcV4gNvVlJMSICGgu3gSU1WEuOe8se4U/Pw+qB3yOk6Xy1gknQCL3ACK7C82+VxEV4vyZJBAq3msDmsDoZPYpx+bDHCxSV4/dghmf0oHIa+ZsNnCdZA4nQ4HWBGEoBI0N8x4UCDjenfjH97fuT9ZbNnDVjw0Inl0X048tAzHbr3f2zagD3Rd7kj9uR+d0dPHnsuGt01tuOegg7dv3v2m39np1AUbAdZoKcn9GiUaue5FEHQ6RDD0o5KYooeCTo6Zl7FkqcbyvTxST4DkTwGVkz0Wr5tVHyg6NKHNlSXz4dvBXyHnPhCYZy2s+nXNjPha6eZZdyRPdHiF6KGPbQlO6Ely6ElIuqrZmstWQeTTWtjoCFPgretJ8Sjv167dNvoW2o/HzdIqZ1wa807mc+uXSD1LSW01s57WibCG6aDDBwGGQiij9XuSbYkO6nIwHcJVmxh0tOR3+IkQQS1Y96ZYmTAbBQxDmUE032gQYkvowJs5pk1GTgjOeSTsOQOjRvVitr+SjlAoT80gboFCbOwfZGWjPv2ETrTATS6sYEkr8fr9jK8HFKC9lBqSAiyoUDQZUj2I4fJ6ofCNqtPB6k0LujHXj1gxGaGS4ro96N0Bi6IuqqAFaVI0fxa7UNRA1KSHzTfJCUOp64dATGhC9Z0BgUcmZl+ZPq66KmtH0e3NBzAJZ9uwfjh0F7/3YdmLH/9Xn+nWkweuv/SHaT4BdzyxcxZh/FdH5/BsxomNf4pp6qm/6BlA1duORb9tWZsITbTkXwVLktgJBn0p4N05AhdPzvQ6XZtHe1Abl48bJsTDzOz4mEgGA+TU+KhyxNfd2tvUPJ83HpuLwdch1lsHdqK6hHbHhzYEnQOXUKcxQeZ66G67eyZMk0IwaffXwNzWHlZ9cyilvJWjtDFAgqJXPOrr9N5AdpK54JsaCuHclUZE5ZJ4ZDgozMdeU416giTgBp/g3h/Ux5HePxVfvuG18n78Lp/7YGCGxHiTfA+Bc9RFyNiEmwkSWDnyivkN2VGlHvLvU1MFhs0tDGWMqPYuYZ5xlqDoCecEDEUGAeSvgy4uEJ/w51GaSPZxGzQbRB2Ms/peAsxGY05HLFxHBFkgyGHEyAqyINNg7EKU7YgiJJebzAYjQoSRFJhqbEQyxGyE1z/Dvs5n9CIO6iSLEo+VV6sx/ojZDjYFnq4QxphohfB7fGZqhSsNJLhL/m4Cq6GY7hGsvOA+TZgqJsuxZcXuaDr2lwOcc/1xPlymNmLi+gC/fWvB+Z7OsPXLtJmeAjAqPtjKn8FybGrSIidAVvnjDaT962X4V4m3KPLNb/uM0o0N7F68+Ehf8TYxq+t4BwqjBg7FmrRg20hN7FKEy4DWwBVl2tqEjucBYXYbw6YcQCbN+J0PCrH4c4Hd5Z7OTp8b7SUO3L1nw/1KnmCufZ7D/adq/nsF1d9FLfgjnKpmgb6fp9FT7GXb7XnCdRe0glgOQlExzCCyBIi6gSW8fE8V+7TYx9Y0BX6Kn2NntMLoJq0RWQZnkzoqDgkwxQ5YBIVaWtWoB41cxdsHrZdnEE4DP1tENQeEQZecKhHRFA7xqMdI7o0t7aBcMgN0Y7xKM0NxLcV9IGIzmgDstL05UNWiCbHo8kQtdPor/vsCV5RRUE/mtIoAwhjQDFw6sk3GHLkjWtRYM8SdjGwpuZqDcyE40BHfsZ9iIwoCS1WKzwmbFNstiRnUhLLKqxN79Qnsbuch4zHjYzT6UoivmTVPNA60Kl6SrlScYQyzDzGOtI5xjXcMyJptXMTUdwpDGNJ0Yv2kA8UvKcmGSebQpRXbu+N0345nfdv3CyASd+qUPOeToGaRitUwNtD5jwC0z4ah1figndwj+cboodePRk9svNNnPzRpzhp/ncPvRf9iLyNp+OnXo8+8/dz0a0H38Qj/xz9d/QkzsNJB7D+kegFFJ/zWXrS0IBcaISaP8FcaSN9lb62UcooG6uXU0AEkdMVn40sIcHj82D487gMCR3hvtH0qy6/0r/5+mykGQKttp4zBUwU4vebIX59BidZD/ef9nDZT9G3oivxfUc3l/frsCy6ijtitEw4NP3laEvLCwxes3j0UrsBWroNkApmHrQzDfdTTRa9EVsKvCNTJwrTU1lLY+yrAxZPHoSXDqRl5JlpOjkjT0mEpkQI9z8+kByK34fySiKk99VZEAka+3j7+IboR3une2eK84zzTcullabHDLtMjaaLxm9NilGWfWaTzWw2mU2yaEkifo9D4sH7NsicSxQdTo87xelE/jSNZy6XyWQUUkLGJ/lyX3pVek06k57mSvAucNvOP6ZyGHv3eRe1oag2SbAQssF01jYY4vsL3PUNscQnvpYrCaopYlI6my2dKb5xtaZGjCAmHnfEDIJkATKq3oiSZgNKBbouGWU3mOJgbFkDTDsCoxPQRkpbkPFvI3XH3l3w9gf9M4f1i11+fdg9I9r6+36Jty3fMOCxp6M53JGBb85/8kxyMH3AnGg17rBsTSe9rmUOk1s4v+dkbT9qdOxb9gewOXJQVH1yHDOOncXMZtlgRj4T8XZleuv6JXdP7ZbeI2MIU6YbnTwic5XVmGkIpZN0JiNYYMoLdAt2bz/SNzwwLDhNP9VQaZxom+Car19gWGBapMxJnxVcwdTpVxnqTGuV5elLgw8bNpg22FOC6UaDnvODBZsk6HiWITwOpqdBHhhaSW3XAYqbHaitgn24BFfgKrwevOBGXK8G26akOBgupa2YFPL0EUMoC2d5OvpDFhyyDNVktsN1o4f68TdZ63QjA+gy3ciAMaNOXtzp0ZZuqq2FKSS3Y8KGBe+dLoxpOxkJO95uczrAlXfE103TQ6NfMox5c9GM3UNKRt8WnTZoyqT7//mnp39bwR0x7dlVvy3SCX9SWrNgxdWn3oj+axP+SLln7Yg7Z3XrPingHBsufHrCjNfGT3l3iXH1g0tGDczNrcy87eDcOSdnzf4O+pADcn9E87cHqgaOpAB7kHYMTGwksw744m7vS7wPk/Z0PRDjg1gDLb0rHNoUl3kKXaXlfPk3irZjXdx6CCFf2yK3RpPZumgSZ9iz5/d/URRsA62aBnXaULUqhUylbKnwlsA66NThgKkjj71N6MH2EeaanuUumnQyIma6XuPlRVuIlPsc2OcocZAKR5WjxsE4DJr9SZ8V4Vmp3E7nHBiTcDk1RMurr8QVqaaCQEpwrjmhQPNB88cXG81sxevjo1c/fC/6e9XrPfcsOnOIO3Jt32fRa08/iA3fMQOv7X/14N2va/vVMD8iroe2J/SL2qs9h7NRJhOU2ss5coW8Slglrpeb5Euy3ieXyIQlegE8CdEncDZB4MAm8xHORggnYsJ955PAWpkg4AlEoK3XZ0ZKBFwjrBcgjbFqIGpmZAzB68gWQgjNMfu4Eo7kgIWynmviLnEcWCkrD+grdsatlGq6F0vJpcS3zj3uZld8+/yG1Ya4JWIDa2M/MgHb/rFftGAagLHWGPspvlxLjZJMKFagGSWInnLR5iHQ4X6cG7cxcjHp0vLm+3hRu9S0tnjN8RawKq9+VFM1bx6bpVmXboR0c6mexmvUblkoZM6yhFwRVGCOWApcvVFPc29LT1cpGmEutYxwKRuFjSbCsGDi8TrglaSXZdFgNJlkm9VisTucLpe9MVZ0gEMuHw1li5mG6kg7WB2IIwRMDxvGyMUJQordZbPbXRZZFFPsFohazLLJ5FPMNkUxW0RZcNk5k1kBXHF2mWNciskkioJAQE+7LBazGQkep9OjdBHxIORDMlztQCri8KBDPuqqu92NePW+hM72uPu3gEXY4nG3uAZ0n9Dtm+uau9UipGqbrvG0Etg9/W+0D28OQBPXGpVjx+BSdKw1duMFxsYEY2OmQ2iRXI2xK/EBC0Jm9h8DlrA5jZBzQFY5tZM2hjPpAFrjA2i1QGDNBUORLh9hvDl63xvn0j2dJOz8/v2BAW/bb/4Svefl6DsZOqct+haIRPFjj/6Qznze4on++K/VDcyLYCaVr/FN6Hn16VbJ6A3jbSUj1CxQkm7s0JMsS5a1Ey5kOgmdxE6GzsZ8S6FVslh9Fn+ehV6MjbEvDkBoSIRiIhTo5sWbEGFpKYZe7sX36kmIzdJl6rONIUsB21norKdv7CUMZcuF0fqRxqGWSXgCO1Wo1E8xTrDMYRcIdHK413KvdQVbp6uTHmUbhZcsx9m3hI/Yj4VPjGcs37IXhYvGbyxtAHWiiOnxV0avKFaT0WDAimIwW6xWPfSNGPSMbJX0mFeIVZSsVh8SQR+IDDEYfDJjk2UGBJ1hCLEaDLKMhPZ2bAcU+WRVJnIjHvOST1ovNUmM1IgbD45JyHSjKvENqlKinFQYBQqpkg+5bfbX/VSmwwMuU2yVuy64m8ubyyGiwav8JnzVcjdBiW5OwsdkougpEo7dGMTRcyxuiuFWu0EzEPTUjnZHMDUOXEkRC12xSopY4wHbGLt4KCkipCVFYHSa9nupB9KkpnojVjAkGCCD0eEsslocztsFsHuKGHCzb9dT27wdePtploheTvbfjlGyv0gv0RihMdnqhDyrE/JojEAsfNMH3xAHSwbUTy7+Q/m0olYkhVH5WywNCXToijM+aGkh4UvRdan+DvboenKN/Dm6ck5xyQi8vKX/td+Ivm1+SUoUU7TyCT0u47xDgtiZYW+Dzn17wOKk0PtWNUKEdcOFoReRWosuDZUfqz0gwmbCxQJgFLKl9kZ2Mp7MT9Z/zrMUPrygE3le5BnRJ+ltkqTnGV5kfASD5se8rOcxTLFY30jcqihJgCCYcI2NxKWKsjhYlWokAhg5qBr0etmHmMEDyToNKwf3Yzrrug4ZjAl8XKHzLhga8eAbOu+CK3q5yBxXOLXtwgJofI5Cg0Zq6eqyApe+9U5QDF66rizIosweiV1GTOyytrFUFt+BoLakKAIcBCAAwOf73NRMLLs+IH7zH0NhJre1vPMj9pd0v/Mu7P2q5SUynekf7bFw4az1eO+1Ay2PUP+iT+wi62XvQJmoECerD4oGMdtt8GRnGbKzwfu3FyZ1zu6dXW4oz55qmJJdkVNnWJH1uOMJzy6D/Vn37sxD7pczj7lPZr5v/yxT6ObAqc5UV7hNdl6EjbTpzfZqM1woC08UpoTnyrXyW/Jvht/C5sI8I2aV9ul5zo5+m2tM1owskuVtbyw2rjNuMcaM3BbjXuPPRsZo9DLORrJbdbgetXm9OtQ9Q+roZfRZY5WxKOhPbySjVCVDpcdbfKGc0N4QF+oQ0fCfEsjLiTRFyNYIjjiDrrT26a/yJ3mSyhfzhO/Qie7e0U08mJCvlDdfLmq5cIHaIedbj7rA3er4gljraRd60AUc+fjuD7UHC7Vvfl5G/HDBHUQzEB12u83hDIQYXmck8f09KMQUjT88de/RnrN65VeenYRzu69cPD+53nXPqVUrd5coojPtqNd597EZoztOnzJ5eyh56bAezy8fsGSAzWjwpAele9reXlbtql7dVx3bp928S1eX394Jf5bpVTL7t+9VMWrg7ffCCK6AEaQrBPSc3Rn1BczJpnQun+vOccWp9akkNTXNm+u901uVuj6V72wtchR5+jn6ecqFckOpqdxxl2eqMM0w2XSP4x5PU+on8lnnWfdX1h+dP7q/Tv4iNZbq9nHtTe1tOVyxSeX6mUq4idzZ5F/Y3xVZsRtZnqAkL0i7ZPca9a70U3qs6FV9hb5Gz+pngwuPcpkgIU0YbPetuB5fwmwqLsYDMYPdKT0LE5v3M+ma5GVqlVdrJiH8aZsRcchXz0TV/gDYhGCGg4+qoEBaBgNW+B+b0W2fa5i57+691Wr0n68crSR5wx6a+8Izc+a+wB1p+WXdwHVvz4r+HD3zFN7w6rDVJ945dfwEaJmS2EWmGVDvQSfUnqKMU71drV2dQ6xDnBXWCucT5AnmccMOZYdHFgxuaSqZwkzl5shVhhrDs/JB8ZB0UJYd8gr5a8IY08aYZpgWmxgTpmDtnaOtAFagKrQebUVfoEsw/ZpMejDmLV69zuVl9V4TNqUb05KgFen6cCooD7Bcenvt6Sd1OFVXrCO6Dkl5xzT7vZpuNc9MHLI+jDA1H5pnXm6e2bp2a460V8CdKT/f6r5gZ/w4R2KHstVnocxiivYl//zi2ei/Z363as/fU/e6F49cuXvHsqkP4uXOl07iZCy9gMmSvduSKqf95YMzrz8AyOoBXDqX2JM6oz4vEdYQNOQZuhm4fFu+dwQZKg22DfFOIuO5CeI4W4W3KfVD7rT1M/cF6wXbz84f3Bc0BDlSU8MeCru+HopBXTuSbmjn6EzyDX1Jd0MPW2/vCGm4YZLhAv+t43d82ahgO2PUKyZAll5nRgAtRu/KxShoNgUV5ZQZK2bVXGGuMbPm2Zb0V3Unded0MR1LeTdQx+jcKXklCWD1bwZIaWd0i85rvgalP6BFhdqff9OWLsAM33joodOEY4tPz5n64dKKDe0PtPhemDP3mZ33zdu2YvOaq09vwUzdoC7E+HsPYnn37deOn333GPCsL0hjCiDLDjz7XB2firx2Mowp58rFYfoJTCU3Q5ygFxSkYIVkWD7hfrdd8eg6WDq7O3i7WPp7ungHWUa7B3vHWqZ7xnrn8fPsV8gVl4Ic2GRwOksc1LliHF7TemWrQhSFTfJKOkSBJ+JHrQAup6rZAGJGdl69ARs8qXTROhjKo6GaTDVjKk515CrpOjU9O+8GliVkMdy/5fwApRrmsOqw5p+1JA44FLVUFyXOCCQMleqZrWCLL3jZdH7NacN+7YQRz9x1pM1Ph7+L/oxtfz+NjfjaRWn/8nFrWs6SQXKn4asW7sLDnU834FTQBTLOjH4e/U3x7T0yGT+6ouvkZ6klYIXpqYb7ADnRATXFJmKTu707x626q9xPyE8adhkEjyHTUO9ucrNu2rtMT2pesmBgZJNXwnYStllZhkfSFhu2xawq6wyyiCEPY20l9ECHTnnaiqjkTc1bD3U97XIfxUeQH13BEqJTeHmYniIFQw5ctuby+BROz5NGzPEdEpti5kUdL8CUooiWJGTmTUkYTKPsJUtwGIA1M9ccyM/Nzyukqwcgh1QM7fS83v4tW6yepXP7jU7q1HFwt5MnmcfXVFfm9RhheUrqUXH3mmsTAUN3Rgcx3wOG6FmYS2qFXs/Z2uiDtn767jZeTHYnt9GHbG0CEX2BrY++h224rlQ/Wf+79Ivd2C7QJuOOwB0Z/TLWt9naRlfgL8gqbtND38PfPWuof2jWFN04/7isijY1bc5mXPT/FPg5w+x08PZGsq8h02vVaRpM8aEcTX/VoCZ0ClF0LVK7cF6vSeqe5pUlhz03mCsFXa5TTqw4VWeFs8bJOmebcBClpaa/ajppOmeKmdhUU7FpIGhFd7jNbD8VSLCaqUBepgsw1XRR4go9zXc+cYbmfHwdoBq0mJNuKGpzZ0b88AyVTGd+rllbcQndeCZp4l59x66zF610GfHc+k8v3fO3tUcXPDvh061//n7Ts4sW7tyzYN7OUs+gYMfxIwvrV+OizzZivGZjzbWpv56c9zyT/bemV9/9y/G/wOjXIsRc1NY99h1GDnrI0+7MC7L5THfmiIHVTuOmO915TsEsm20Mh5HJy+lsekkOimpuQV5MxE0iFgdoCyXOvIK8esclB6lybHXUO2IO1kFswfj6ux0KX6In8X3A2S8QiwbYe5a4Ese0tZX48OX4afSi+BxIzUUNbkbeqAsaeTkJGwQAGqJG+BIUBnchNz4zOhx2c8CscYW3m2sb7m+a+2LfhjmVJWuLYBr858PlO55sGUO21d435MFFLS8DxlaCiBVp+2E6tEgtHyiuF7eK9WKTeE68JOqQmCpWiTXilkTWF2JMlFJFmKt0LGHAhr4fLHWOZyVeF+QQu4XdytazTewXLN/EXmIJYn3sKUix7AChtYczi7RzsdAz3Opx0yGfWa0dOIJerGxoaGB/OHnyqp0NXT1LNQC0kfmV7gPjd1WPjh/OjxQZk+Ff3BWeGcbcKxEL77Nqxv+lA5YM6gxcaoDQwmkZms96SV0GOTwLDgBfKPZkuSDfViqV7mXmSGeZr3ndszwO8CFdUIjwncRiw0BDGVvGl+rKxEXsfG6TeJx/nz3Dn+e/0/2b/02wWySJYxiW8LxOFAVIiIIQ1PE2nY5nWDbISTaOA88BEgIGHnB0xUSvRxLbiE37uTQBAjXg0+Z4z3pQz/ogIkGwjRAuRgNhTNyy4Ut/z4kJYaFHGeniZXXr6mXCvYQpzBmhq0f0SDGELu1Qu04RioQiRrvGDxmrktgmOSIKyclFPPUYkyMQfLjfpwX7/ImjxNoOVTVKOJw8+JF+bYNnv4MGn+9XInw80FKyFuzTt+5w4binqlo+Y7Fgc0BtNluRdoGnrux30Yd/3JcUL47LyzSDjkI27inqYNDx7u+iU/Grn0e3LeaOXDuK66NzW8aT1AVR+quZpQCDQg2law4jDhR3Yaf4RmxefjzM6RAP0+IbtWoQJNfEpXJbuHMcOxAulzgmlaviargYx4LkSYSJCyN9kyaUHtDSWxBuAlON3CCZ7HXchsNx5GoKaqbWE9qDpQ2J3VrQHnwItHUAHT+MRHBEu+gNoD3Os+fFL50XfNxp7oqPOAVfQHQl+USGCaR4ebtXrwezmQ943Ip0KojXB7cGSdDp9BiD67UfQpQfdAXXJ+EkiKluRHIDQXwKYWpTklRE0cIgd3qwEc874O/Zql3Bjm45T33Oy+Ut2pIEmM503gKh06Bkdt64x2+UbdaQTTYnYYvB3qpStJ+H0IPl2qK3UzsTqukVbQK7UcNs6/js1LmPpd7/9ubdBwKj76j6U0Pp+H5LOrOhRweMubv0yN5DLRnkqWljOj+6o+Uxsn/evJLHH2r5JKFrvwFuOdC7qpVjeCvZqTQqXzPfWi8xV6w8S2W2AzBwvoI3KqdcX7hiLtYn2Iw2hwWULuYdBslglI3pek3z6jH86Qe4tIGkmtd1yUWqXFtd9a4mF+tiSK7dkVC+lv9Qvs5WxXu5KO4NgurV1smBY81/6F4HbxYlQdJJDK+EzLwxCZskS4Jh9DgDCI+GaXtBwg28gWG12+d8VrGtRJEasit7zXqODT22t3tV/46LWmaRFfdM7/Lwuy30XE83sBkzgCcG5EavqeUWneSWe/K9hOF8mTCJnyIIeUpnS2dHvqu70tfS19HdNZobLQ5Wyi3ljsGu6dx0cbwy3TLdMd51L7aLPGcYxQzlhkqj5GnMBG6CNE2WnF5WZwbI2dK1kzPW9GBejg4jnaLzgfnX4RwFGuS7qYEIcWM6UqEIBRpBHTzUOIz/Vqo6XH6lvPyPn0tRC1pbkhjCDRHv5u4WWZBxq3aWHCVOlt84X3fbseqvn2LHfT+sPhdtPry/dsX+A8tr9xMrznhwbvTLlhM/PIBTsOHdd97921/feRuqro1OYf3AFwtYQifVZ2SlrXK70ldhi331PpLqy5IDyR3tHZPvTK7yrfcJnZ2dk/o4+ySVCaPk0c7RSVOFSnmKMt1ZmdTk+8D2meszzwcp523nU77wxXyOABtWwvZ8trPSg+2jjFQu6H9Ijip6sxGsa+rQ8g5waJHRnX5KwoqkShVSjcRKs7E1l+Raggj9V5c2FVxa/N98Ws2pNUdudGmtrULmsNu0c8EZZuYGVtXu6Pzw5JWnps45d9/Ide3Mz86d9/xzs2fti07hXqkbNGhNbOPT0aur+3VuucrsOHHsndPvvP0RQHs5WMzHgV9mtFS9rb0VKywOsHlsV3YIO5GdzfKiWRAF0WA1iwbECFivdRRJYuZ6AQtpPiu2kjTz/6WtYul57LqtAj7n5Zn0XBPtVqT1Zy5IeavWqJ2rKJ9JTz3Eexi3fnUgDcu33zGleNRdd9x552132VLY0LbqXp2fy+hZXDGz5UM63xeDv7kP2p+DP1HvY9NsaZ3FPmK39OFpE9IWig+Ky9KftT7f5nXGIDo9LmdO3zZnnFwSGUaI0hFLrtHCaHG0NFo/Wh5tmCpMFadKU/VT5amGhlBDhonu5aVnFaSPlMr040PjM2cHZqfXpD8iPSk/nPlYm0dzdki75KczdmQeCP015EimS6SWlMhIISMoS6zHF7Kz+nbJHmoee1Pdxe6B7jHuve6Tbt7kTnXPcJ9zs6nudW7ifpkMA78PUStaoSdgFHwK7ACsYEIPGR6wOfK0w4YpRnMexu1GJ09LJsleu471ttOnerAn3a1aXXnuRjJqvy49G0q+5I2cysbZno70qRD4dBUdmzqS4o41HUlHBWOcjnzpprRz182HDq1uXHV/+ivNmQM0tUY9ucvhxKJBNThzYdBXMzVozjx//eiXM67s1Iy2KQFwN0JmxaJYFYZPM/iSkJipS8JcW7ik2CDpNwaSUFrAIAtZUhLOzBAlPswmoVQlmarF+IEv7aItFWeHlyyhtmo1Nfb++GFORiijHVj3BYX/sb8KX3oYQTP3i/ebVt23cF5+8JHjmwZ26ZT90JBFr4w018uzpiyc6nC0T1r26mPDpxxfdPITfLu3cuaEbrcHXMGOvZcM6Dk/MzXc675JrsGjBxcGvMlWKT23y8LRI7eMeIEiLT32T5LNbQLfsuYwkuhBrxA1F5vULhCpcYOdKxskzCCHIoZNEigDRm9S0lAaNliCMo7phO5i9wpdla5Gt17HItCiW3X1uibdKR2vO0KmIhcu2DcxLizaz4XBtj9/uUhbgmgponrAnJurvBU/JxB0xlcgqL9oLtR+06XtfhLF06/o7mltli07cPCgNZyZsm2LcseE7WTcGqybFl27puWR/m08tC9LQWq+0P7/1CuHkYd6/2ADEZ/VQQ9NXFKzLLa8sBWnC1aHjK0OPQi8GbqDch1Bl1ObRJ24yYmdAzya2NNJ1HPJQ6o8Wz31npiH9YCXc10h0N/U+sRT4A+w4gD3deeluXX+BM2greAVtR6bB0h5WMVoMBno/iE9bQqzKCsnIYNgTkJ0Ds3OXhLf9kgsxWSEtA1gpwaTAhpniheevuvpgYq+QW++Z9CgB29reLKh1/SB+bPIwy0H1nboOWjIupUkAi4Dpr8MZC4CLyR810v54KilmSMSlWaDOSKCAZEn0AtpjH1/AEKcCCW6eyCm+PNQJlwgdVEVwZ5EDrhA6qx6MLNdHvLBxSRnoUwxJEVQvtQL9ZSG4+GkTCgVJ+KJZIowRZyH7sX3kvnCPPFeqRbXkhXMKt1KoU58Cm0UH5JeQNulV9BLun3SW+iv0ll0WvoRfS1dRZelNhLiJBdySJkoJBVKAxHY7pxqceRxKphCErgRQVGyiaKEGAIeg7ajCp4GqG5te5TXSSKDMNdexnKaoKoqeG5EbMRJB1UwfAkHMVX0ERWn6b9/nw5Zs8fdUt5S7nE1ny9P/PDjunthjvzHKTkw3av/ONGiHWpp3a20gh3/YnTan88HU13hHw9H72FDLcsmzRg6l6yM+3AmGJF/wIgo+N6XTBZsSnNrDoV6yB0ZadrAbhA2GR83NXFNfJPuHZNoUh0RD2MV7QaPko8765fgB/VCe8sItkxXpi81PoY3Shv1L5FG+U3928Z3lbPMafFvhk+VC5LFwvNMfKuQF7XNQpNJoXuFJpNBub5RqEi8iZgk5Tg6LhIleH2r8Dg4Y8Ebdwt5RdstlAZasKW34X45TTKN5cX7VQlY+ZLKl/A12g8UuqpGH3M/SRsIHe1tXngs8RNkjbvAXOWCcrn5PzYG24XLE9wtT/xske4LapuBx+JXCHTaBmHCk2swupIj2sadPjkipzkjDBBNg6+maMtY9ghO80dE1dt6aAgcO2qXUqeL7s456VgVUp+LycAmvCy66cun23nbBA98FH0Ir/7sbOfodyQTR3/rmXNn7tWo3PIe7lMWLafj548OYn6C8fPg2gMmLzbRVuzwRjJtw017JUY1qMBQX2ZOnkIvOlm0OAwuS4Y+Q84wFMgFhnzjJrM+05Jp7eUos5RZy+xTLFOsU+zz+bmG+eYFtgX25YY68xrLGusq20Zpp/6o8rL5iO176VvbL4YW5TdbzJtiMZlkxWyxAPbdNqs1aJFskDDJJrMc1Es2vV6yWiyyrOcZr9uEvIqXtPe+6iXeRlJ80GRVLaqtkQxV9cUW1ULGWF61EEsjvvOQCaeh7kkSvWUx+fSq6pNz5IEyUyLHtH3hOw+0N0FnSXFDkm8hqHLw01roT0dgVOkBU5dy+byb/si+2eNSmrUYclH13jrEwo2+OR3jxI5v33rjkL71rkEjS19Gcuwi0scu4vgJAM3/tsU+P1QYkdIKI0ZQQwftEXPiHFgZ/T0/uOowntaM+PJYoXZWICGE9EfqgbTFttvaFPVymkOcPjr99c/Caanhrxui07qk5ywcnhedtEvJTE+qNCWzmS2b5ixZOJdUXn1z751lQ7R/ewVE/vbjfSvObxpjKvpFSBK0/4a1/euMbBoevH3/m7/vbZmkdBb6af+REWtPaM/p7ogOQF0V9PveaK7SOZF//cNl8oks+r/MElRPPkJ3sbOQHai3Lhndyw1HpbgWjSS70UJKTDJS2RfQTCi7G9JdIDxCn4Xyw4DOARUBDQfyJPL6A40FGkLTUPYwfRbeUUXfo4Wz0EghFc3ghsdaoL4N3BtoItBmiG9nv0Y7+QiaDukd8NyrLEKFtAw8s4HfjTZC/pNwfxzkbYawFNLbID4anstJxEXdWuSmIRAP+VnwntWJ/mYwr6ECdlbsS+hLGbyzD9AKqKMEwh5AfaGMFcI7gWrxG2glfiO2He5DiJZC/bU0H6hbIuwF71kO94vhuXRIL4W4B9rBQ2gC8gNlkhdQhNjQUQjbQ/9HxPsN9AaaTPt8vU/Q/kSb/pPibex7I0GdrwAFSCR2AULxhrbdSktvod5MLqqBsBIoCWgQOYGms/0QBn5t4i4ghhIgj/Lpc6Db2fFoAKQxtHMI14Aep2mg/hrNirWwT6KtzGXUCe4t4DdAP8YDvzsAXUHtyY+oLR9EiwFf3eD9S4A2wzsvangYj4ZC/e0gzGUvaBhaAbQG6vq5lU+UN5BeAuM6GOq6RiUCnh8C1BPGpQZoGm0P1N+e8pyOOx4ejUDZ81BmNCXId2oEfaeYpM/Q5+FdwQQOt/8Rou1QZi3w9QsIWSA7bUMraThLENw7Du9xA/FAyUDtgC4AbQeqBOoM9BJQJtSNoF5GwytghmJTwwdgg3sDeAht0zAb78NmbTzjMrMt8S5aj59/AVUmyE/fSeWFYhbasq/13VSmKGZaQw3flRT3+B+0nxRT10OQPfYH1JO2QZNBwFZrSOUO2kzlYQN4VSshfBxwvJRilravNaR8oVjTeAIykQiLbuhrjiYjEDIIBRJYX9oatvLiejgZ7YB3VvB3g07Zinqxs1Ev5iF0N3sJdWOyUDsuB/KgP1C2nvyABgtNKBfGciCkN90SbqSkO42nck3Qz+eBn6fRU8DTavY0SWNPY457PvYdh/Bb3PPkfi3+H+GthJvi92hI6cZ7/0/z/0+InOGeB535fOx77nQsBv15mMqE7gecA+RrDSF/P1ANULYQxhuFStyoG4YUHqHLQDNYFXXmVFTINsH42EHPgyxA/jDuS/QqsxatYk/HPsE1qIacRit0djSWbACdBnWRM2gpJfp+CKtuwNFNmLsVS61hK15vDanOT2AqFUIe5O+9BJ1P0BWgXwBHT+N4HYVUP2vzA+hooBVxvMZ+v47Pt9AzEK5uxectOK28BZ/yrbi8NdTmFtDvrXIK7VjV2n+qH6mOozqS6jmqZ1rL3xre8Hwd2Q04pnr4BBqZkOu0BPWBNn6VkH3QwzDeI2IxvkfsOb4htpOxxHbyHSH+MRAXew76Pe/6nFoaiybm06zWuTSej/St8yiXi6Yn9NkOTd/8E/1Jm0eHa+0T+b1oMXcVxh10oNberQkZBH5CuyvZCuD542gN9MPN1II8Qj7QaMoTbSwQctF5gc6JzKPAZzoXrUVLmU/BXqDP5iKzNl8UoxHQ9re0PJhTaUjzuBFoO/8D6sgOA13bhMbTsaL9oO2hYy/MAe/VDnriNOrA7oIydiRBua0aD1T0nIYL+mwlWD/AC904pAPMDoAy9H3btGdUZEnwY4fGC+15sEUovigv4J28HQ3W7Ikf0BZuGBoBMrRNV4O28cNA5uxoJ7zjGXhuGG0LPOfR5utH0SiQr5Wgm1aCzkEa/kfGrjLPQ3/mgV4HYmqAR88jF1cDPKzU+t6NjevYWio/zG4UohjhHwU9TO2JR1EdG0bd+Uq0FvLWcqAnod7VkLcM5DcHZHcVPJ+a0NsI6l4F+fTZYmrLUBuByotORVa+RrMDkNYGaqdA/cx3aBvTB60EHHcRHgU+LEdtYb7AgL0UoA5x0tL3J2hNnLQ8JR5iP6OgRVp+Lnqf7Gb0gFs6hx5ml6Ap7HDUkemA3KwZtWX/BrL6G3qCMaEx7NvoCbYRraFp1ooymXrofwPYljT/JCqh+eR9SG9EI9kieH4luocdg2Yx+wB7HyKJnQhjDc9xDwJO0uH5f8J7E4S/RiOZ4SBbKyD+W+wFWk6royE2ghLbC7XVnruBtLa20i1tJn2Bb31gTKG9NH5Te6Gt19vZ2sb/0j6tn/S98Bwtwz5B/9dX7O9AwXgYHUTWoueBtpKzqCvTH83HO0HBPIl64AtATyZoD+qlhfuABsEcn48XArVj89FLQEsg3gbCPwPtjafBdstHnwIth3e/BuEBXjvuiRG5ExXQEPI2A20Eeqf13o1E6/pv+TcSl4RuTh9ENZTw5VgLpVvLA58LoL4C9nbgJxBgcT0lfjEaqZsL45cB+SnwzlvSUE9H9iCa+j+1538ifBLlaDyMk3pjH1vHA0LH/wb9/YbQR8PE3PD/qn3/JwTjuxioXOPvT8iewJARn0FpEA6HcDgzB82jBOm2kC5r5Se+DFijtBM9ouVfH794PmAFgR13+635t6ZvHdf/KU0OoGdupFYcXMfDw2gZJbYYygPdmhbeQsso8X+Fe3/9zzT73P9AI1E287jWJqRh7JY0PxDmTCCSDm31aM+soXQ9fRJkGYiW1Z43oLWUNNkFIg1oCqXr9/NBfwPdwNcCyleoU7vfOj6t43Lr+ED7VPY9oJEwV7yHciAcAmGX1vA6vhP64ibMD4rj/Xqa6pILt5T5Qyb+kI2TdK757+/8/xOB7LwN9AbQ8f+v66JahuoIheqJv4MdUgx25GmwT0bRsxctoEuutQd6FvTQUAg/gjyYvaNZQAaImyFvEoRPIXT1F4jPhPzTcYoRNgltTdiVbsg7lHhWSLxvSPz5q28i9Dsg6ve98eev7gaaCvF/AC2C+GcQvva/2jv/2CaPM44/dzb2m4QQx0CSNW/8OjgxJU4xNVBDQuMfOA2tNyVAYDHLSCBkYhQNJAeQqg5epiIVtSWok1jHpIZV0zStavvGnjInIIUtHVszNqqOsYn+omya2m1tSv9Yyx/M+969b2igWcuk/Wk7n+f73N1z753vvde+17H9Qp9B/N9R7zHoL83yGz1I7wdnkP4H0rtBF/zj0IXQRjAfuFH/hECsRz5zHvp/19nPP+5UsWbpQz818Z4X9NHbzyHuWKf35xfo7eca0/v/i3TGewa3qTkOOGe6inWfMfPc5/POcaYV+/PfM7Fvyt/AmnKuWEeLtaxYP8v1o6Xy/E2uY9Eu0YJpFWtnsX4Va2exfoX+EPq4Y47szyZxni/6Zb1Ruft/4J94Ftxl8RqePbEq45hz/DyeOcVvp2IO2j/BEYVXKyfmnfIyUTHmYMmXAfJLUW/ei+ha8eyUAzdi5u/FwzhGtBBzHC/tVDFAVDlpUrV/BudwqvMdomocIyq0ptHE8y6R9xHwAXYLjqtFF4nq0JfFOM7uvvr5NPyIKPBnokbsg6UhouCjRMvQ1r1/IwpdJlp+0GSlTnQfXoXCG4hWfYNoNfKaXjRp/hZeUrCNFmwrYmB5HzdZy8X1eQoUKFCgQIECBQoUKFCgQIECBQoUKGDBxCcpYSLUR3OIk4uCFCeyzy2+QTbiMR+V2SppCuSBjTTYIGgHPWAQDAGHjBM5e8AhMA4+lCVRW2Xm6eXRHOQJKdldu0Myuc1Mdn9dJrNfTZn6lfWmJh40w5rMsHtXmNlL46YubjTVXR/ShRaXhs7GKmwV9CrgtBeW8ZepjDHS6JRtIRmA2xxWTtTmztb5Q0PjNjsxG7cx2kFa/qyNZUrLQ7FinudT5CaNf8DfN0v4+9l55aGh2EP8Kr0ExoGNX8X9Hf4OHeJXMJplsBEwBMbBBTAFHPwK7m/j/hZ/C1FvUhBEQA8YAuNgCjj5m7Au/obYN9IKPwI4fwPWxV/Hw3odtoxfhneZX0bX/pAJrw6NSicQtByt3nIqqy3HXRHK8dcy15doOf6XrDegnYot4xfJAByNXcTGL5IXdIBesBc44F2Cd4l0cBycAgZwoM4l1LmEOpPgPLhEy0AUdACFv5pBMzl+IeOPa7EK/nv+a6rEoP6O/0bqeX5O6m/5r6S+AvVAJ/m5jEejWAnKCXVcUBc0iPI5/BfZOreWj5XzcQyPBhsEEdAOesAgcPBxviizQ3NjI6dpUiFEZug9qT+m5xSK7tKi/rWYY15h/E33w4MZ8g75edR/4vtICuM/9jQ8YfyPPQlPGP8jh+EJ49+9H54w/h274Anj39IDTxh/eyc8mBx/9ud1i7Vw+8PMGyvjBzBKBzBKBzBKB8jOD4g7XbeLvv0g09CAETsZDSxp0PQxpp9h+gamP8f0fqYfZPphpq9h+lamB5iuMt3D9CjTT7NVGAqdRX92S3J1tIrpk0x/gelppvuZXs/0OqZ7WTia47WZB5dLaZWSjYnjCnp/S6gMfazFiNZiWtfisB+HvQDyMhVFkHeRGfwlj9BF2YaImV7aFNoTW8cnUHECu2GC3gZ27KAJTKMJbGQCGyiDjYAecBZMgTxwIHoROj4obRlsEERADzgEpoBDdmcKcNpjdfEl2bGg1el2keITuIsrk9fy2miNS3UFXOtsgyor87B2T97Dw1Qh/vnhLlfKc6x05OPSTz4upaJYET/GB6kGO+K4pYOZ6zVajj2T8Z/WYgvZ98hjx6xjq8nP6qGrKC3TK0lVhK4glT8PDWXUzZr4ir6/URtj80StEe26+lftPTXH4b6rntb+5M3ZWUb7I3KeH9Euqke1V4I5BTln/DkGGfPK0FF1lfbCpAw9jIKTGe2gkBHt22qb9rAqC/rNgq1ppKJl2gb/Fm0dtpdQt2vRNLY5okXUrdoaM2qlqDOiLUMXAqbbgM4uUWWjPo/c4KZwju2MNjpPOLuc7c77nCFno7PWqTlrnNXOBYpbcSnzlLlKsaIoDsWucIWUBeInLQLiw/sLHC75q2t2Ye3Sd3FhufnZfs4UTg+RMd+W5MmNcZY0zvZRcrvX+NdGX44Vr99izPHFmeFOUrIzbqwKJHPO/AYjHEgazo6vdQ0zdiyFXIM/nmPU2ZVjeZF1pFpcVnmUGCs/8lS10LuPPJVKUVXF/khVxN1SvvqBxCym17IzvohUdYtfY5xIbuwyflqTMkLCydekksZ3xXWXR9lH7MPWxCi7JiTVNWprYR+1bhD5tpZEKpXMsc0yjrzsGuIwY67JOMVDXhFHXsVjxp004+pRH3F1QhBXVET1Mq6+qEjG2ZmIG07XtSaG6+pkTKWX0jImXemdGTNZj5j6ehlTodOkjJms0EWM0SJDVBUhHlWGsLtIlSEqu0uGbP40JGiFHL0ZclS2ZGOfxqhmTOmV6ZjSK4gJ3OmtPx4IsGxzqq9bXLO619faD3qNJ/bvrDL07V7vcF/Kupi1v3d7306h2/qNlK8/YfT5Et7h5u5ZirtFcbMvMUzdrZ1dw93R/kSmOdrc6tuWSGXbOlaEb2nr6M22VnTMsrEOsbEVoq228CzFYVHcJtoKi7bCoq22aJtsi+Qc7+gaViieWtttapaXFGO+9lbXpuIVrr0tcvI211YdrB6ziw/XlARSxlxf3CgFouie2D0xUYRjShTNExcmt4qqDjbXVo+xn1hFLmSX++IUGNiX3kdVrd9MmH9p3JA1sE8MuGkD6f92Q1mrEd2WSA8QJY2GjUkjsn5L17DTidxe8ZCMpum8kpLWXP6smbkUmU0i02a7GSjy1oi8oiIr8LP7f5+l8vtKOj+dZVEPG6B0ymZ4kp0cTwWd1hWgx7BcEi8P6RQeYJoFWHp6G7LbZH2XUDzeaQb2WZ41DgOWmrVQJT09HDdvqIOnqv8AXSKXvAplbmRzdHJlYW0KZW5kb2JqCjE0IDAgb2JqCjw8L0Rlc2NlbnQgLTIxMC9DYXBIZWlnaHQgNjk5L1N0ZW1WIDgwL1R5cGUvRm9udERlc2NyaXB0b3IvRm9udEZpbGUyIDEzIDAgUi9GbGFncyAzMi9Gb250QkJveFstNjY0IC0zMjQgMjAyOCAxMDM3XS9Gb250TmFtZS9FWVdQT0QrQXJpYWxNVC9JdGFsaWNBbmdsZSAwL0FzY2VudCA3Mjg+PgplbmRvYmoKMTUgMCBvYmoKPDwvRFcgMTAwMC9TdWJ0eXBlL0NJREZvbnRUeXBlMi9DSURTeXN0ZW1JbmZvPDwvU3VwcGxlbWVudCAwL1JlZ2lzdHJ5KEFkb2JlKS9PcmRlcmluZyhJZGVudGl0eSk+Pi9UeXBlL0ZvbnQvQmFzZUZvbnQvRVlXUE9EK0FyaWFsTVQvRm9udERlc2NyaXB0b3IgMTQgMCBSL1cgWzNbMjc3XTExWzMzMyAzMzNdMTRbNTgzIDI3NyAzMzMgMjc3IDI3NyA1NTYgNTU2IDU1NiA1NTYgNTU2IDU1NiA1NTYgNTU2IDU1NiA1NTYgMjc3XTM1WzEwMTUgNjY2IDY2NiA3MjIgNzIyIDY2NiA2MTAgNzc3XTQ0WzI3N100N1s1NTYgODMzIDcyMiA3NzcgNjY2XTUzWzcyMiA2NjYgNjEwIDcyMiA2NjYgOTQzIDY2NiA2NjZdNjhbNTU2IDU1NiA1MDAgNTU2IDU1NiAyNzcgNTU2IDU1NiAyMjJdNzhbNTAwIDIyMiA4MzMgNTU2IDU1NiA1NTZdODVbMzMzIDUwMCAyNzcgNTU2IDUwMF05MVs1MDAgNTAwXV0vQ0lEVG9HSURNYXAvSWRlbnRpdHk+PgplbmRvYmoKMTYgMCBvYmoKPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCA1NTE+PnN0cmVhbQp4nF2UzYrbQBCE73oKHRNysDTdM7LA1CUh4EN+iDchV1kaGUMsC9l72LeP3LXphQj8gUoaU9WFevNx/2k/ne/l5vty7Q/5Xo7naVjy7fq89Lk85tN5KupQDuf+/npn7C/dXGzWw4eX2z1f9tN4LXa7cvNjfXi7Ly/lu6en3x+q98Xm2zLk5TydVkXDz1+rcnie5z/5kqd7WRVAOeRx/asv3fy1u+RyYwffxKeXOZfB7ms66K9Dvs1dn5duOuViV60Xdp/XC0Wehv8eJ+Gp4/j2usAZKph0hDNsKfVwhpZShjMcKY1wht6kuoIzDJRqOEOmFOAMIyVzRAp91Qqn1JQinBIoJThFKDVwilLawimRUgunJEodnNJQsrGQwuHUNhZSOJx6gFM6kwJHbFQGChaFVAYKFoVUBgoWhVQGChaFVAYKWziVgUILpzJQsCikMlDo4VS6DyOcyh6lglPZo9RwKnuUAKeyRxE4I2NLhDMyoyQ4IzNKA2dkRtnCGZlRWjgjM0oHZ2RGYV3GyNKEdRkjY6vCmViHRjgTrWqCM9GqNnAmWlUWYUy0qi2ciVaVRRgTraqZJBOtag9nerWa4Uz8+HSEM7G0WMGZWFq0usjE0mKAM7G0KHA2LC1GOBtOIiY4G04iNnA2nETcwtlwErGFs+EkIrsxNowd2Y2xaW2d/dtbj832WLq+KPvnZVl3qG1m25SPHXmesi/v+To/TpXrr/gLTntjMAplbmRzdHJlYW0KZW5kb2JqCjQgMCBvYmoKPDwvU3VidHlwZS9UeXBlMC9UeXBlL0ZvbnQvQmFzZUZvbnQvRVlXUE9EK0FyaWFsTVQvRW5jb2RpbmcvSWRlbnRpdHktSC9EZXNjZW5kYW50Rm9udHNbMTUgMCBSXS9Ub1VuaWNvZGUgMTYgMCBSPj4KZW5kb2JqCjUgMCBvYmoKPDwvU3VidHlwZS9UeXBlMS9UeXBlL0ZvbnQvQmFzZUZvbnQvSGVsdmV0aWNhL0VuY29kaW5nL1dpbkFuc2lFbmNvZGluZz4+CmVuZG9iagoxIDAgb2JqCjw8L1N1YnR5cGUvRm9ybS9GaWx0ZXIvRmxhdGVEZWNvZGUvVHlwZS9YT2JqZWN0L01hdHJpeCBbMSAwIDAgMSAwIDBdL0Zvcm1UeXBlIDEvUmVzb3VyY2VzPDwvUHJvY1NldCBbL1BERiAvVGV4dCAvSW1hZ2VCIC9JbWFnZUMgL0ltYWdlSV0vRm9udDw8L0YxIDIgMCBSL0YyIDMgMCBSL0YzIDQgMCBSL0Y0IDUgMCBSPj4+Pi9CQm94WzAgMCA1OTUuMjggODQxLjg5XS9MZW5ndGggMjM0MT4+c3RyZWFtCnicrVppbxzHEW1gv62BRLIOy7YCDGBZoRVpNH3N4eSTeS0ZRryWXApiPtDUSqZDkTJFJ/C/z+vqY3p2hvaMLSzEnaP3VXUdr7uq9dM4S0rF07JK3uEyS87HutKpKONLN+B8/MN4Nr4Y8+R/Y5FsYviPY54l/xq/+neWvB7/RL/Pkqu34++m4+drPOFlWspk+ga/MC94omVa6qSoVJrrZAqBacFzlSfT0/HS8uW7d/Or07OT82Tj4r+XZ6fzb6Y/AhLvVqcWUSRlKkQHYKGNfgSYZYprAmSP2LfsazZinO2xv7TAZAusEqkgrLzwWFxWFusuPg/YQ/w1/+6zO+xBD/W4ytOqXMCUmlvMp2yXvYRuW2yNTaDnX9kKm+Gqj64i42lRLGrLpUW+B/3u4XML3w/xF1p3apurBqbmaVYsWrMsu7Xta1WZZalctIFQzgaPgXvHfe516Fikxpsth2u4SgUVnb9z9jegSeh2/A3TTLCsU8EbIJVKMxEsqZzf/4FZHrED6LnHdtg+PLTLjo/7o0rY8qODiiLVAVXkwqJqYBrHrON7xJ4DdQvXI6SAkbWLOxNeo47AvVFQJtJKBvULJ0gAZga4TQKdsENE2HOa0ksSuov7bVzdQoxM6OmE/RmCTd58xr7sLT4v81QHJxfKSv+aHS9B3BO4umRZf7CiNFTm51I5tD9BqxH7hH2O7L7Djt8Y+9DHpI5J+rsDJAA5DxKEpTWyxwas8xVc7Py+1vA7u20f9BeUg45V7X+XoAWQV+DxCb5HuAPy/g1scgMuvvLgbl04MqlApMWvJPwNYEqnJsktGKAt2LEJHw3fDUCSeF7VSMpP9xH7O9R6Ct5sg7WZQ3JF7NagDp3zRe4ooGAf5TxegzcEr3wyTsgZcMIIPITMGwAZk8bHQWwyRuWc8YCCH2Q5cWGzgrwdwRYzXE/wGSKCV6ms1zjhzXqEBJgRL31mMmoAYIN8hCg8oska0vqALtcg4JAyyHDMQ4i5C0Ff9BeUYztShEgtReVXpkcDMGKq0n72JXLmqcnEJcSosWtBqXRsZjFCmGEOIzzJzBOTE8NENggtK90i+An7EtO/b6jsFi6/wOUtsNtd7AY+HwLe4DK3RaOocDF4Gwxm1pN7EPIVHr/CneG0bVz3JzQv7qMzmgf+OJTm0QZzmkgQF0UHp/EsSvEi97H9TwTzPk3SzNik/AZdTyjQu6ZuJIguCUifOuV5rmoWMUBbtCr3F9K98dalIkv5zXItwwTHAXuBzGygZdBVVgIFREYFhPnsrY9FhZ23VlnKUYXoHGWDvz0f7/tqorWx5ClWcY3fShepuSoKSxMb1/N3rVm0ITS8UzUgJDBs9q7MP5xenb2/Pru86EAquvfjWmYBqlDeIqa0uXw2AEXgy+mDTaPNhtdn178ky5ev22VRe15CCXJPmNgzGD4DOiGt708HYIDXnYdVVUmL8P7k7HUHRE4rYQRRSnIRPBWMImVp7ftifp08HwAiEGqOIjiv3P5t/eryw4cBIBz1mFushdKZzbvZ/OztD9c9TCKFTjPZjBcppGXe5e3tPhB5nnLRhODCLRh78zfzq/nF6TyZ/vJ+njwegOe9xGniJgNWevxaKTxpJoCuhLI5tDt92QeirEx5F0Ooihc2Zg8uzvrY1WPUoSZL6XLn8OT8566QL1K5QEgQ3ExAUbrKcP/n75Pp5fXJ+QAcpKCnT6m4y5yWMg3iohtVER8b3nIc6WmN7qLXCGKKJvfe3UYDhHnDa3h7Gw8oeSye7qLXUgjyrnvvbuMBuSS7+wH2NhqgsODpegruNh6AfQ+WljDA3sY2wM6zqCfpbuMBMdsH8o8nSSbmwuzR6sXB3ofVQd2wOoCybEgtceM273ZlVviulSDLfAQK7becWydXb+fJ8sn3ZxfzdjS3oTxxeqylF78u2nNTLVsrqWwWG4LJkv+87SPWwiD50ir/Azie5Lw6z5AIlmy/W+3xc08p4ecmD7myCDzZWd7vA+IIITJJWXHL1lLQfrc/Sm0R6FLmpbC5vHqw1wPD00GkSVWKMtKkP0g/RWy8Z9gayMAphTQ+iDjFv3akUr/3pOIHeFapR3hWCRIcrUQjLK2EAZZX6veeV/wATyzRCEcsYYRjlnqEZxY/wlNLNMJRSxjhuCWyheOWYAxHLtEIxxZhhGOXISNiD3S9v6FksD/D6qxDXSN54euaPVROtth+gus91DiHuK7o2VHPEkdkCjHZFCHcZhFVn+kB36a+6shUKCXTnZhZ2dZal7QoesgOrRebwY+w4Te9rV2MGKD7gqChundZXJt1JFQ9octs2m6+wqEOymOy+iBLx9Cq0KH4G6KeOUkJVZnMpDetqb92KAj2UI2NmIShoecOqdzTpiRAyrjNXuU3Cahr6/7YWBmUbrlLUrPXxoHFfkpm3qO+j5HaXwKnNAx9gEbr6xCIpvNjmqkHNjBeoVQ2N0MMlIEQgxulPzsIzQU/h9+LHtX10nfm48wxLXHThf+WivBNVPyDMibGF8p5wCTLfTqUGqHk3uyrLArxqEUgue5ipwPcHVLc/D6Nm0L+gMquB7PAd2XZVnqbeu7Glbb1uLPYhbhJQpWZ6ruWsGT7lb+tU5PHXA8Crj6C7FXXZ6nzepuaZLO+WnkZEftI2RFZR3Qc0xvWTdbD9p5rg8GEP0eRRAEv8HeTLJ+F/s+IfUrNzhkCx1riqTsRGTL7OO6xWNdCkbaxfZ9Ql9XKtF2+bim6cS4t8lTlCc9kmtX8lodj2k8p9kduAhO4lJaQNbo1J2Eb9oGZoj1js5MdhcOfA9ct3XIctkbfM3dcNqFzui16E/8yXmG3yJSmMW9P+Uzre90h2aVtjST7521dvEy7otQrwYy6jub+0PUf/aozIidaafb9zB1Rr9OzVSCYNuLtzuOXQrVNrJXZa4YjMuUTZYVkdh/jdOGgxArdac1DOBiq2gpm7obLOzyPYrEMy044n/fn8d5QuTvzNE+PyDB9FHYR3Jh5+C8KfSBlC5JzAkNVUWvNXTPrt87+K1igTLCSc1seAIO727CbbU9Cmf8aIJuTkH5z0TzGdgdIdBrQURuVdPoW1UYm6Ugh6Xs/GXcHVDsnKLx5cvkm4THSLj7/B/MdZfwKZW5kc3RyZWFtCmVuZG9iago3IDAgb2JqCjw8L0tpZHNbOCAwIFJdL1R5cGUvUGFnZXMvQ291bnQgMS9JVFhUKDIuMS43KT4+CmVuZG9iagoxNyAwIG9iago8PC9UeXBlL0NhdGFsb2cvUGFnZXMgNyAwIFI+PgplbmRvYmoKMTggMCBvYmoKPDwvTW9kRGF0ZShEOjIwMjIwOTE5MTIyODE1WikvQ3JlYXRpb25EYXRlKEQ6MjAyMjA5MTkxMjI4MTVaKS9Qcm9kdWNlcihpVGV4dCAyLjEuNyBieSAxVDNYVCk+PgplbmRvYmoKeHJlZgowIDE5CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAzOTUwNiAwMDAwMCBuIAowMDAwMDAwMjk5IDAwMDAwIG4gCjAwMDAwMTc0NjEgMDAwMDAgbiAKMDAwMDAzOTI4OSAwMDAwMCBuIAowMDAwMDM5NDE4IDAwMDAwIG4gCjAwMDAwMDAwMTUgMDAwMDAgbiAKMDAwMDA0MjEwMSAwMDAwMCBuIAowMDAwMDAwMTMyIDAwMDAwIG4gCjAwMDAwMDAzOTIgMDAwMDAgbiAKMDAwMDAxNjQwMiAwMDAwMCBuIAowMDAwMDE2NTkyIDAwMDAwIG4gCjAwMDAwMTY5NzkgMDAwMDAgbiAKMDAwMDAxNzU5NSAwMDAwMCBuIAowMDAwMDM4MDA1IDAwMDAwIG4gCjAwMDAwMzgxODcgMDAwMDAgbiAKMDAwMDAzODY3MCAwMDAwMCBuIAowMDAwMDQyMTY0IDAwMDAwIG4gCjAwMDAwNDIyMTAgMDAwMDAgbiAKdHJhaWxlcgo8PC9JbmZvIDE4IDAgUi9JRCBbPDIwZmQyYmJmODU2ZDVlNTI4ZmNkMzMxNTM0OTVhOGRjPjwxYjFkODU5N2VmZTUxMzM0NDE0NGMxYzZlNWM1MmFjOT5dL1Jvb3QgMTcgMCBSL1NpemUgMTk+PgpzdGFydHhyZWYKNDIzMjEKJSVFT0YK</DocImageVal></MultiLabel></MultiLabels></LabelImage><Label><LabelTemplate>8X4_A4_PDF</LabelTemplate></Label></res:ShipmentResponse>'
                return response

        # zeep.Client.transport is using post from requests.Session
        with patch('zeep.transports.requests.Session') as mocked_session:
            mocked_session.side_effect = MockedSession
            yield mocked_session


    def test_01_dhl_basic_be_domestic_flow(self):
        with self.patch_dhl_requests():
            super().test_01_dhl_basic_be_domestic_flow()

    def test_02_dhl_basic_international_flow(self):
        with self.patch_dhl_requests():
            super().test_02_dhl_basic_international_flow()

    def test_03_dhl_multipackage_international_flow(self):
        with self.patch_dhl_requests():
            super().test_03_dhl_multipackage_international_flow()

    def test_04_dhl_flow_from_delivery_order(self):
        with self.patch_dhl_requests():
            super().test_04_dhl_flow_from_delivery_order()
