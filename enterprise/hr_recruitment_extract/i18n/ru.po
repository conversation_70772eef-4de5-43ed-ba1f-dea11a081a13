# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_extract
# 
# Translators:
# Андрей <PERSON>ев <<EMAIL>>, 2023
# Сергей Ше<PERSON>нин <<EMAIL>>, 2023
# <PERSON> <yeli<PERSON><PERSON><EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "%s's Application"
msgstr "Применение% s"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Buy credits"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Купить кредиты"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Refresh"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Обновить"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Retry"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Повторная попытка"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"All fields will be automated by Artificial Intelligence, it might take 5 "
"seconds."
msgstr ""
"Все поля будут автоматизированы искусственным интеллектом, это может занять "
"5 секунд."

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_applicant
msgid "Applicant"
msgstr "Кандидат"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_ir_attachment
msgid "Attachment"
msgstr "Вложение"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Можно показать кнопку отправки ocr"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Оцифровка в автоматическом режиме"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_actions_server_digitize_cv
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "Digitize document"
msgstr "Оцифровать документ"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Оцифровка только по требованию"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Не оцифровывать"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_error_message
msgid "Error message"
msgstr "Сообщение об ошибке"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_state_processed
msgid "Extract State Processed"
msgstr "Состояние извлечения Обработано"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_state
msgid "Extract state"
msgstr "Состояние экстракта"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_status
msgid "Extract status"
msgstr "Состояние экстракта"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "Идентификатор запроса к IAP-OCR"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Находится в извлекаемом состоянии"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Recruitment OCR: Update All Status"
msgstr "Набор OCR: обновление всех статусов"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Recruitment OCR: Validate CV"
msgstr "Рекрутинг OCR: проверка резюме"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_config_settings__recruitment_extract_show_ocr_option_selection
msgid "Recruitment processing option"
msgstr "Возможность оформления набора"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_company__recruitment_extract_show_ocr_option_selection
msgid "Send mode on applicant attachments"
msgstr "Режим отправки вложений заявителя"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 10 seconds."
msgstr ""
"Извлечение данных еще не завершено. Обычно извлечение занимает от 5 до 10 "
"секунд."

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_applicant__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_applicant__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_applicant.py:0
#, python-format
msgid "You cannot send a CV for an applicant who's not in first stage!"
msgstr ""
"Вы не можете отправить резюме для соискателя, который не находится на первом"
" этапе!"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "You don't have enough credit to extract data from your Resume."
msgstr "У вас недостаточно кредитов, чтобы извлечь данные из резюме."
