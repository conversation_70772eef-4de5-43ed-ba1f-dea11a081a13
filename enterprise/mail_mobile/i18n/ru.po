# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_mobile
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid ""
"Check this if dynamic mobile-app detection links cause problems for your "
"installation. This will stop the automatic wrapping of links inside outbound"
" emails. The links will always open in a normal browser, even for users who "
"have the Android/iOS app installed."
msgstr ""
"Установите этот флажок, если динамические ссылки для обнаружения мобильных "
"приложений создают проблемы для вашей установки. Это остановит "
"автоматическую упаковку ссылок внутри исходящих писем. Ссылки всегда будут "
"открываться в обычном браузере, даже для пользователей, у которых "
"установлено приложение для Android/iOS."

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid "Disable link redirection to mobile app"
msgstr "Отключите перенаправление ссылок на мобильное приложение"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_discuss_channel
msgid "Discussion Channel"
msgstr "Дискуссионный канал"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_mail_thread
msgid "Email Thread"
msgstr "Цепочка Email"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизация HTTP"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid ""
"If disabled, you won't be able to open external URL's in the Android/iOS "
"mobile app (e.g. \"View Task\" button in email)."
msgstr ""
"Если отключить эту функцию, вы не сможете открывать внешние URL-адреса в "
"мобильном приложении для Android/iOS (например, кнопку \"Просмотр задачи\" в"
" электронной почте)."

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Mobile"
msgstr "Мобильный"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,field_description:mail_mobile.field_res_users__ocn_token
msgid "OCN Token"
msgstr "Токен OCN"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__enable_ocn
msgid "Push Notifications"
msgstr "Пуш-уведомления"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Receive notifications on Android and iOS application"
msgstr "Получайте уведомления в приложении для Android и iOS"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,help:mail_mobile.field_res_users__ocn_token
msgid "Used for sending notification to registered devices"
msgstr ""
"Используется для отправки уведомлений на зарегистрированные устройства"
