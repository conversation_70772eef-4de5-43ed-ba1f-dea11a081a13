# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_mobile
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid ""
"Check this if dynamic mobile-app detection links cause problems for your "
"installation. This will stop the automatic wrapping of links inside outbound"
" emails. The links will always open in a normal browser, even for users who "
"have the Android/iOS app installed."
msgstr ""

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_partner
msgid "Contact"
msgstr "Stik"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid "Disable link redirection to mobile app"
msgstr "Onemogočite preusmeritev povezave na mobilno aplikacijo"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_discuss_channel
msgid "Discussion Channel"
msgstr ""

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_mail_thread
msgid "Email Thread"
msgstr "Obravnava z elektronsko pošto"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid ""
"If disabled, you won't be able to open external URL's in the Android/iOS "
"mobile app (e.g. \"View Task\" button in email)."
msgstr ""

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Mobile"
msgstr "Mobilni telefon"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,field_description:mail_mobile.field_res_users__ocn_token
msgid "OCN Token"
msgstr "OCN žeton"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__enable_ocn
msgid "Push Notifications"
msgstr "Potisni obvestila"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Receive notifications on Android and iOS application"
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,help:mail_mobile.field_res_users__ocn_token
msgid "Used for sending notification to registered devices"
msgstr "Uporablja se za pošiljanje obvestil registriranim napravam"
