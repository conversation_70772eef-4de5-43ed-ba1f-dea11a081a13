# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_csv
# 
# Translators:
# Wil Odoo, 2023
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "Base Import"
msgstr "기본 가져오기"

#. module: account_bank_statement_import_csv
#. odoo-javascript
#: code:addons/account_bank_statement_import_csv/static/src/bank_statement_csv_import_action.js:0
#, python-format
msgid "Import Bank Statement"
msgstr "은행거래명세서 가져오기"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_account_journal
msgid "Journal"
msgstr "전표"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
#, python-format
msgid "Mixing CSV files with other file types is not allowed."
msgstr "CSV 파일은 다른 파일 형식과 혼합할 수 없습니다."

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
#, python-format
msgid "Only one CSV file can be selected."
msgstr "하나의 CSV 파일만 선택할 수 있습니다."

#. module: account_bank_statement_import_csv
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
#, python-format
msgid "Rows must be sorted by date."
msgstr "행은 날짜별로 정렬되어야 합니다."
