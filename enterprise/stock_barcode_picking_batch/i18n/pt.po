# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_picking_batch
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_move_line_product_selector_inherit
msgid "<i class=\"fa fa-fw fa-lg fa-truck me-3\" title=\"Transfer\"/>"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.picking_view_kanban_inherit_barcode_picking_batch
msgid "<span class=\"fa fa-external-link-square ms-1\" title=\"Open Batch Picking\"/>"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Allocation"
msgstr "Alocação"

#. module: stock_barcode_picking_batch
#: model:ir.actions.client,name:stock_barcode_picking_batch.stock_barcode_picking_batch_client_action
msgid "Barcode Batch Picking Client Action"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_barcode_cancel_operation__batch_id
msgid "Batch Transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_barcode_cancel_operation__batch_name
msgid "Batch Transfer Name"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
#, python-format
msgid "Batch Transfers"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "Cancel Batch Transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Cancel this batch transfer?"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Confirm"
msgstr "Confirmar"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Description"
msgstr "Descrição"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Discard"
msgstr "Descartar"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_picking__display_batch_button
msgid "Display Batch Button"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_kanban
msgid "Lines"
msgstr "Linhas"

#. module: stock_barcode_picking_batch
#: model_terms:ir.actions.act_window,help:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
msgid "No batch transfer found"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "No ready transfers found"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Open picking batch form"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "Print Batch Transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimentos do artigo (movimentos de stock)"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "Scan the package %s"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "Select an operation type for batch transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "Select transfers for batch transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "The Batch Transfer has been validated"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "The batch picking has been cancelled"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.actions.act_window,help:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,help:stock_barcode_picking_batch.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "A operação de stock onde a embalagem foi criada"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "This batch transfer is already done"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid "This batch transfer is cancelled"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
#, python-format
msgid ""
"This batch transfer is still draft, it must be confirmed before being "
"processed"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_move_line_product_selector_inherit
msgid "Transfer"
msgstr "Transferência"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "Tipo de Operação"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Unbatch Transfer"
msgstr ""

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. You "
"can use the info button on the top right corner of your screen to remove the"
" transfer in question from the batch."
msgstr ""
