# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_graphene
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:22+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_comparisons
msgid "24/7 support"
msgstr "24/7 kasutajatugi"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_call_to_action
msgid "<b>50,000+ companies</b> run our software."
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_cover
msgid "<b>Making the difference.</b>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid ""
"<small>Aline is one of the iconic people in life who can say they love what "
"they do. She mentors 100+ in-house developers and looks after the community "
"of thousands of developers.</small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid ""
"<small>Founder and chief visionary, Tony is the driving force behind the "
"company. He loves to keep his hands full by participating in the development"
" of the software, marketing, and customer experience strategies.</small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid ""
"<small>Iris, with her international experience, helps us easily understand "
"the numbers and improves them. She is determined to drive success and "
"delivers her professional acumen to bring the company to the next "
"level.</small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid ""
"<small>Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds.</small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_comparisons
msgid "Access to all modules"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_comparisons
msgid "Access to all modules and features"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_process_steps
msgid "Add to Cart"
msgstr "Lisa ostukorvi"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid "Aline Turner, <small><b>CTO</b></small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_cover
msgid "Contact us"
msgstr "Võta meiega ühendust"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_process_steps
msgid "Get Delivered"
msgstr "Saa tarnitud"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_picture
msgid "Grow with Us"
msgstr "Kasva meiega"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid "Iris Joe, <small><b>CFO</b></small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid "Mich Stark, <small><b>COO</b></small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid "Mobile Experience"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_cover
msgid ""
"Our mission is to give customers the best experience.<br/>Extensive "
"documentation &amp; guides, an active community,<br/>24/7 support make it a "
"pleasure to work with us."
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_process_steps
msgid "Pay"
msgstr "Maksa"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid "Powerful API"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid ""
"Probably the only CRM with a full mobile experience. Never wondered how it "
"works? That's because it just works"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_picture
msgid ""
"Put your people at the heart of your marketing with tools that help you get "
"to know your audience <br/>and see who you should be talking to."
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_process_steps
msgid "Sign In"
msgstr "Logi sisse"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_comparisons
msgid "Start Now"
msgstr "Alusta nüüd"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid "Support Team"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid ""
"Thanks to our API, Developers report building their solutions 4x faster. "
"Discover why and how."
msgstr ""

#. module: theme_graphene
#: model:ir.model,name:theme_graphene.model_theme_utils
msgid "Theme Utils"
msgstr "Theme Utils"

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_company_team
msgid "Tony Fred, <small><b>CEO</b></small>"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_text_image
msgid "Unique experiences to drive engagement"
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_text_image
msgid ""
"Users are looking to consume engaging content.<br/>We empowers our teams to "
"create the most relevant content."
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_three_columns
msgid ""
"We have a support team that will do everything to answer you as quickly as "
"the voice assistants."
msgstr ""

#. module: theme_graphene
#: model_terms:theme.ir.ui.view,arch:theme_graphene.s_text_image
msgid "We have one goal in mind, the user satisfaction."
msgstr ""
