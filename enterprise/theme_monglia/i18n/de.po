# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_monglia
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:23+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_cover
msgid "21/22 December"
msgstr "21./22. Dezember"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_call_to_action
msgid "<b>APPLY FOR OUR V.I.P. CARD</b>"
msgstr "<b>UNSERE VIP-KARTE BEANTRAGEN</b>"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_image_text
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) "
"25%, var(--o-color-2) 100%);\" class=\"text-gradient\">Mixology</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) "
"25%, var(--o-color-2) 100%);\" class=\"text-gradient\">Mixologie</font>"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_text_image
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) "
"25%, var(--o-color-2) 100%);\" class=\"text-gradient\">Music is magic</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) "
"25%, var(--o-color-2) 100%);\" class=\"text-gradient\">Musik ist "
"Magie</font>"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_title
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-4) "
"25%, var(--o-color-1) 100%);\" class=\"text-gradient\">MAIN LINE UP</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-4) "
"25%, var(--o-color-1) 100%);\" class=\"text-gradient\">HAUPT-LINE-UP</font>"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_cover
msgid "After a year's absence, the free and independent festival is back."
msgstr ""
"Nach einem Jahr der Abwesenheit ist das kostenlose und unabhängige Festival "
"wieder da."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_three_columns
msgid "Bruce Porter"
msgstr "Bruce Porter"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_carousel
msgid "Discover"
msgstr "Entdecken"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid ""
"Dj's and electro artists meet every year for the electro dance festival in "
"the largest concert venue in Europe. Book your tickets now, there won't be "
"room for everybody."
msgstr ""
"DJs und Electro-Künstler treffen sich jedes Jahr zum Electro Dance Festival "
"in der größten Konzerthalle Europas. Buche jetzt deine Tickets, denn es gibt"
" nicht genug Platz für alle."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_image_text
msgid ""
"Every evening, renowned mixologists take turns preparing the best cocktails "
"for you. Drink responsibly."
msgstr ""
"Jeden Abend wechseln sich renommierte Mixologen ab und bereiten die besten "
"Cocktails für dich zu. Trinke verantwortungsvoll."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid ""
"Everyone knows The States is the home of country music, and they have the "
"festivals to prove it. Grab your cowboy hat and boots because we've put "
"together the best country bands in the USA."
msgstr ""
"Jeder weiß, dass die USA die Heimat der Country-Musik sind, und sie haben "
"die Festivals, die das beweisen. Schnapp dir deinen Cowboyhut und deine "
"Stiefel, denn wir haben die besten Country-Bands der USA zusammengestellt."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_text_block
msgid ""
"Kori Thill ■ Chana Smithwick ■ Cira Sherard ■ Elfriede Ogan ■ Mariko Yonker "
"■ Bella Maday ■ Shaneka Wilkerson ■ Raina Planck ■ Micaela Labonte ■ "
"Chadwick Cottman ■ Emile Hornung ■ Genevie Mehaffey ■ Dottie Hinkson ■ "
"Mariah Nurse ■ Velda Caster ■ Rossie Cranfield ■ Iva Madrid ■ Flossie "
"Freitas ■ Tina Gregerson ■ Quiana Tassin"
msgstr ""
"Kori Thill ■ Chana Smithwick ■ Cira Sherard ■ Elfriede Ogan ■ Mariko Yonker "
"■ Bella Maday ■ Shaneka Wilkerson ■ Raina Planck ■ Micaela Labonte ■ "
"Chadwick Cottman ■ Emile Hornung ■ Genevie Mehaffey ■ Dottie Hinkson ■ "
"Mariah Nurse ■ Velda Caster ■ Rossie Cranfield ■ Iva Madrid ■ Flossie "
"Freitas ■ Tina Gregerson ■ Quiana Tassin"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_text_image
msgid "Learn more"
msgstr "Mehr erfahren"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_carousel
msgid "Martina Clarck"
msgstr "Martina Clarck"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_cover
msgid ""
"More than ever young talents are honoured on the big stage with more famous "
"artists."
msgstr ""
"Mehr denn je werden junge Talente auf der großen Bühne mit bekannteren "
"Künstlern geehrt."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid "Old Town Road"
msgstr "Old Town Road"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_text_image
msgid ""
"Open 24/7, the club welcomes you and your friends in a relaxed and friendly "
"atmosphere."
msgstr ""
"Der Club ist rund um die Uhr geöffnet und empfängt dich und deine Freunde in"
" einer entspannten und freundlichen Atmosphäre."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid "Smack My Band Up"
msgstr "Smack My Band Up"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid "Sodade Club"
msgstr "Sodade Club"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_media_list
msgid ""
"Sodade is a beautiful concept that is difficult to put into words, but can "
"be experienced by enjoying the diverse range of music on offer."
msgstr ""
"Sodade ist ein wunderschönes Konzept, das sich nur schwer in Worte fassen "
"lässt, aber man kann es erleben, wenn man die vielfältige Musikauswahl "
"genießt, die angeboten wird."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_call_to_action
msgid "The vip card allows you to benefit from tickets before everyone else."
msgstr "Mit der VIP-Karte bekommst du die Tickets vor allen anderen."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_three_columns
msgid "Theia Hayward"
msgstr "Theia Hayward"

#. module: theme_monglia
#: model:ir.model,name:theme_monglia.model_theme_utils
msgid "Theme Utils"
msgstr "Designwerkzeuge"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_carousel
msgid ""
"This is the first full-length studio release in seven years for the British "
"singer-songwriter."
msgstr ""
"Dies ist die erste Studioveröffentlichung in voller Länge seit sieben Jahren"
" für den britischen Singer-Songwriter."

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_three_columns
msgid "Wilson Holt"
msgstr "Wilson Holt"

#. module: theme_monglia
#: model_terms:theme.ir.ui.view,arch:theme_monglia.s_cover
msgid "X²O Festival"
msgstr "X²O Festival"
