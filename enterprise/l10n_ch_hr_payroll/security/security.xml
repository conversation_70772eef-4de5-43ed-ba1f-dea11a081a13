<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="ir_rule_l10n_ch_monthly_summary" model="ir.rule">
        <field name="name">Swiss Payroll: Monthly Summary Multi Company</field>
        <field name="model_id" ref="model_l10n_ch_monthly_summary"/>
        <field name="domain_force">[('company_ids', 'in', company_ids)]</field>
    </record>

    <record id="ir_rule_l10n_ch_individual_account" model="ir.rule">
        <field name="name">Swiss Payroll: Individual Account Multi Company</field>
        <field name="model_id" ref="model_l10n_ch_individual_account"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>
</odoo>
