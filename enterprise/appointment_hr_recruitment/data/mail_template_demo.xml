<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="email_template_data_applicant_schedule_interview" model="mail.template">
        <field name="name">Recruitment: Schedule interview</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="subject">Can we plan an interview together for your {{ (object.job_id.name + ' ') or '' }}application?</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from or '') }}</field>
        <field name="partner_to">{{ object.partner_id.id or '' }}</field>
        <field name="description">Send this email when you want to schedule an interview with a candidate</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
    <tr>
        <td valign="top">
            <h2>Congratulations!</h2>
            <div style="font-size: 13px;">
                Your application is really interesting and we'd like to plan an interview with you.<br/>
                Can you please use the button below to schedule it with one of our recruiters?
                <br/><br/>
            </div>
            <a t-att-href="env['ir.config_parameter'].sudo().get_param('web.base.url') + '/book/interview'"
                style="background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;">Plan my interview</a>
        </td>
    </tr>
</table>
        </field>
        <field name="auto_delete" eval="True"/>
        <field name="lang">{{ object.partner_id.lang or '' }}</field>
    </record>
</odoo>
