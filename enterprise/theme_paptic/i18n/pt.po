# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_paptic
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-28 12:23+0000\n"
"PO-Revision-Date: 2023-10-27 15:39+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_call_to_action
msgid "<b>50,000+ companies</b> run Paptic to grow their businesses."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_text_image
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-3) 100%);\" class=\"text-gradient\">A Section Subtitle</font>"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-3) 100%);\" class=\"text-gradient\">Help you grow</font>"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-3) 100%);\" class=\"text-gradient\">SUCCESS STORIES</font>"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid "Always Improving"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid "DISCOVER MORE  <i class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid "Fallanzy Integration Explained"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid ""
"Fallanzy has been active in the workspace design and ergonomics consulting "
"business for over 20 years. Discover how we answered to the challenges of "
"this company."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_cover
msgid "Grow your Business.<br/>Optimize your time."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid "HeyNosf Inc. Digital Transformation"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid ""
"HeyNosf Inc. is a communication agency that was founded in 2006, by Jean-"
"Louis Dewinter. He always had a strong interest in languages and decided to "
"create a company reflecting his own interests."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid "Hotels Improvement Study"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_cover
msgid "Man waiting at the airport"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_three_columns
msgid ""
"Our solution is suitable for Hotels as well. We help management to optimize "
"their time and employee to be more efficient and more productive. Discover "
"our study."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_cover
msgid "SCHEDULE A DEMO"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_cover
msgid "START NOW"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid "Scalable and Modular"
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid ""
"Thanks to our running release system, benefit from all updates (security and"
" new features) in real time."
msgstr ""

#. module: theme_paptic
#: model:ir.model,name:theme_paptic.model_theme_utils
msgid "Theme Utils"
msgstr "Utilidade de temas"

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_title
msgid "Unique experiences."
msgstr ""

#. module: theme_paptic
#: model_terms:theme.ir.ui.view,arch:theme_paptic.s_image_text
msgid ""
"Whether you start small or are already a large company, our solution adapts "
"to your needs and grows with you."
msgstr ""
