<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_invoice_filter" model="ir.ui.view">
        <field name="name">account.invoice.select</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search/field[@name='journal_id']" position="after">
                <field name="l10n_it_edi_doi_id"/>
            </xpath>
            <xpath expr="//filter[@name='to_check']" position="after">
                <filter string="Exceeded Declaration of Intent"
                        name="l10n_it_edi_doi_declaration_of_intent_exceeded"
                        domain="[('l10n_it_edi_doi_id.remaining','&lt;', 0)]"/>
            </xpath>
            <xpath expr="//group" position="inside">
                <filter string="Declaration of Intent"
                        name="l10n_it_edi_doi_declaration_of_intent"
                        context="{'group_by':'l10n_it_edi_doi_id'}"/>
            </xpath>
        </field>
    </record>

    <record id="view_move_tree" model="ir.ui.view">
        <field name="name">account.move.tree</field>
        <field name="model">account.move</field>
        <field name="arch" type="xml">
            <tree string="Invoices" sample="1" decoration-info="state == 'draft'" expand="context.get('expand', False)">
                <field name="made_sequence_hole" column_invisible="True"/>
                <field name="name" decoration-bf="1" decoration-danger="made_sequence_hole"/>
                <field name="invoice_partner_display_name" string="Customer"/>
                <field name="invoice_date" string="Invoice Date"/>
                <field name="date" string="Accounting Date" optional="hidden"/>
                <field name="currency_id" column_invisible="True"/>
                <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-success="state == 'posted'"/>
                <field name="l10n_it_edi_doi_amount" decoration-bf="1" sum="Total" string="Tax excluded"/>
            </tree>
        </field>
    </record>

    <record id="view_move_form" model="ir.ui.view">
        <field name="name">account.move.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button groups="account.group_account_invoice"
                        type="object"
                        class="oe_stat_button"
                        name="action_open_declaration_of_intent"
                        icon="fa-list"
                        invisible="not l10n_it_edi_doi_id">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Declaration of Intent</span>
                    </div>
                </button>
            </div>
            <xpath expr="//header" position="after">
                <div class="alert alert-warning mb-0" role="alert"
                     invisible="not l10n_it_edi_doi_warning">
                    <field name="l10n_it_edi_doi_warning"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='fiscal_position_id']" position="before">
                <field name="l10n_it_edi_doi_use" invisible="True"/>
                <field name="l10n_it_edi_doi_id"
                       invisible="not l10n_it_edi_doi_use"
                       readonly="state != 'draft'"
                       options='{"no_quick_create": True}'
                       domain="[
                           ('state', '!=', 'draft'),
                           ('company_id', '=', company_id),
                           ('currency_id', '=', currency_id),
                           ('partner_id', '=', commercial_partner_id)]"/>
            </xpath>
        </field>
    </record>

</odoo>
