# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_map
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# jabiri7, 2023
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:47+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Santiago <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"
msgstr ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                Sign up to MapBox to get a free token"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vista de la finestra d'acció"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#, python-format
msgid "Address"
msgstr "Adreça"

#. module: web_map
#: model:ir.model,name:web_map.model_base
msgid "Base"
msgstr "Base"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Close"
msgstr "Tancar"

#. module: web_map
#: model:ir.model,name:web_map.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: web_map
#: model:ir.model,name:web_map.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_partner__contact_address_complete
#: model:ir.model.fields,field_description:web_map.field_res_users__contact_address_complete
msgid "Contact Address Complete"
msgstr "Adreça de contacte completa"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
#, python-format
msgid "Items"
msgstr "Articles"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Locating new addresses..."
msgstr "Localitzant adreces noves..."

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
#: model:ir.model.fields.selection,name:web_map.selection__ir_actions_act_window_view__view_mode__map
#: model:ir.model.fields.selection,name:web_map.selection__ir_ui_view__type__map
#, python-format
msgid "Map"
msgstr "Mapa"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Map Routes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "MapBox servers unreachable"
msgstr "No es pot accedir als servidors MapBox"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#, python-format
msgid "Name"
msgstr "Nom"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Navigate to"
msgstr ""

#. module: web_map
#: model:ir.model.fields,help:web_map.field_res_config_settings__map_box_token
msgid "Necessary for some functionalities in the map view"
msgstr "Necessari d'algunes funcionalitats a la vista de mapa"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "None"
msgstr "Cap"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Open"
msgstr "Oberts"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "OpenStreetMap's request limit exceeded, try again later."
msgstr ""
"S'han excedit el límit de sol·licituds d'OpenStreetMap, intenti de nou més "
"tard."

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Set a MapBox account to activate routes and style"
msgstr "Configurar una cuenta de MapBox para activar las rutas y el estilo"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Set up token"
msgstr "Configura el token"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Some routing points are too far apart"
msgstr "Alguns punts d'encaminament estan massa separats"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The MapBox server is unreachable"
msgstr "El servidor MapBox no és accessible"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The token input is not valid"
msgstr "L'entrada del token no és vàlida"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#: code:addons/web_map/static/src/map_view/map_model.js:0
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid ""
"The view has switched to another provider but functionalities will be "
"limited"
msgstr ""
"La vista ha canviat a un altre proveïdor però les funcionalitats seran "
"limitades"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "This referer is not authorized"
msgstr "Aquesta referència no està autoritzada"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid ""
"To get routing on your map, you first need to set up your MapBox token. It's"
" free."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Token"
msgstr "Token"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_config_settings__map_box_token
msgid "Token Map Box"
msgstr "Caixa de mapa de Token"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Token invalid"
msgstr "Token no vàlid"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Too many requests, try again in a few minutes"
msgstr "Massa peticions, torneu-ho a provar d'aquí a uns minuts"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Too many routing points (maximum 25)"
msgstr "Massa punts d'encaminament (màxim 25)"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Unauthorized connection"
msgstr "Connexió no autoritzada"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Undefined"
msgstr "Indefinit"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Unsuccessful routing request:"
msgstr "Sol·licitud d'encaminament no satisfactòria:"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_controller.js:0
#, python-format
msgid "Untitled"
msgstr "Sense títol"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_ui_view
msgid "View"
msgstr "Vista"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_map.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipus de vista"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "View in Google Maps"
msgstr "Visualitza a Google Maps"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/models.py:0
#, python-format
msgid "You need to set a Contact field on this model to use the Map View"
msgstr ""
"Heu d'establir un camp de contacte en aquest model per utilitzar la vista de"
" mapa"
