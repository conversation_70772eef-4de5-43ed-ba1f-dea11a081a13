# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# JanaAvalah, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Andre <PERSON>et <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Anna, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-04 14:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and %s others"
msgstr "ja %s teised"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and one other"
msgstr "ja üks teine"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid " is not supported by the Journal Report"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "\" account balance is affected by"
msgstr "\" konto saldot mõjutab"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "%s is not a numeric value"
msgstr "%s ei ole numbriline väärtus"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "%s selected"
msgstr "%s valitud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"'Open General Ledger' caret option is only available form report lines "
"targetting accounts."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"'View Bank Statement' caret option is only available for report lines "
"targeting bank statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "'external' engine does not support groupby, limit nor offset."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(%s lines)"
msgstr "(%s read)"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_receipts
msgid "(+) Outstanding Receipts"
msgstr "(+) Laekumata arved"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_payments
msgid "(-) Outstanding Payments"
msgstr "(-) Sidumata maksed"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(1 line)"
msgstr "(1 rida)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(No Group)"
msgstr "(Gruppi pole)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid ", Including Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid ", Only Show Unreconciled Entries"
msgstr ", näita ainult sobitamata kandeid"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid ", leading to an unexplained difference of"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Värskenda"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "1-30"
msgstr "1-30"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "31-60"
msgstr "31-60"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "61-90"
msgstr "61-90"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "91-120"
msgstr "91-120"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid ""
"<i>Errors marked with <i class=\"fa fa-warning\"/> are critical and prevent "
"the file generation.</i>"
msgstr ""
"<i>Vead, mida tähistatakse <i class=\"fa fa-warning\"/> märgiga, on "
"kriitilised ja takistavad faili loomist. </i>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
msgid "<span class=\"o_stat_text\">Partner Ledger</span>"
msgstr "<span class=\"o_stat_text\">Partneri pearaamat</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">Kinnitus</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "<span>One or more error(s) occurred during file generation:</span>"
msgstr "<span>Üks või rohkem viga tekkis faili loomisel:</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr "Sama nimetusega grupp on juba loodud."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
#, python-format
msgid "A line with a 'Group By' value cannot have children."
msgstr "\"Grupeeri\" real ei saa olla alamridu. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit must contain a minimum of two companies. You might want to delete"
" the unit."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "VARAD"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.journal_report_communication
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Konto tabeli mall"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Account Code / Tag"
msgstr "Konto kood / silt"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr "Konto aruande töötleja"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr "Konto aruande joonealune märkus"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
#, python-format
msgid "Account:"
msgstr "Konto:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Raamatupidamisbüroo"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "Raamatupidamisaruanded"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "Raamatupidamisaruande rida"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "Raamatupidamisaruanded"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
#, python-format
msgid "Accounts"
msgstr "Kontod"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr "Kontod korrigeerimiseks"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Accounts coverage"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Tegevus"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Toimingud võivad käivitada teatud tegevuse, näiteks kalendrivaate avamine "
"või dokumendi üleslaadimisel automaatselt tehtuks märkimine"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Tegevus"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Tegevuse tüüp"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
#, python-format
msgid "Add a line"
msgstr "Lisa rida"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Lisa kokku summad sektsioonide alla"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "Korrigeerimine"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Korrigeerimiskanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance Payments received from customers"
msgstr "Klientidelt saadud ettemaksed"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance payments made to suppliers"
msgstr "Tarnijatele tehtud ettemaksed"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "Edasijõudnu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr "Partnerite aegunud saldo töötleja"

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "Ostureskontro"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Ostureskontro"

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "Müügireskontro"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Müügireskontro"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "All"
msgstr "Kõik"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Journals"
msgstr "Kõik andmikud"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "All Payable"
msgstr "Kõik võlad"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "All Receivable"
msgstr "Kõik nõuded"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Report Variants"
msgstr "Kõik aruande variandid"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
msgid "Amount"
msgstr "Summa"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "Summa vääring"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Amount In Currency"
msgstr "Summa valuuta"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Amount in currency: %s"
msgstr "Summa valuuta: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Analytic Accounts Groupby"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#, python-format
msgid "Analytic Filter"
msgstr "Analüütiline Filter"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
#, python-format
msgid "Analytic Group By"
msgstr "Analüütiline grupp"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Analytic Plans Groupby"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.js:0
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
#, python-format
msgid "Annotate"
msgstr "Lisa märkus"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
#, python-format
msgid "Apply"
msgstr "Kinnita"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "As of"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "As of %s"
msgstr "Alates %s"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "Kuupäeval"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#, python-format
msgid "Audit"
msgstr "Auditeerimine"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Auditeerimise aruanded"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Keskmine kreeditorite päevade arv"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Keskmine deebitoride päevade arv"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "B: %s"
msgstr "B: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#, python-format
msgid "Balance"
msgstr "Bilanss"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "Bilanss"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr "Saldo praeguse kursiga"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr "Saldo tehingu kuupäeva kursiga"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr "Saldo välisvaluutas"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#, python-format
msgid "Balance of '%s'"
msgstr "'%s' saldo"

#. module: account_reports
#: model:account.report.line,name:account_reports.balance_bank
msgid "Balance of Bank"
msgstr "Panga saldo"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax advance payment account"
msgstr "Ettemaksukonto saldo"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (payable)"
msgstr "Tasumisele kuuluva maksu saldo "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (receivable)"
msgstr "Enammakstud maksu saldo"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "Panga sobitamine"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Ühendamise aruanne"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Panga- ja sularahakontod"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Base Amount"
msgstr "Baassumma"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Before"
msgstr "Varasem"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/dialog/expected_date_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Cancel"
msgstr "Tühista"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Cannot audit tax from another model than account.tax."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "Carryover"
msgstr "Ülekanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Carryover adjustment for tax unit"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Carryover can only be generated for a single column group."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Carryover from %s to %s"
msgstr "Ülekanne alates %s kuni %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Kassa"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr "Rahavoogude aruande töötleja"

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "Rahavoogude aruanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, beginning of period"
msgstr "Sularaha ja sularaha ekvivalendid, perioodi alguses"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, closing balance"
msgstr "Sularaha ja sularaha ekvivalendid, lõppsaldo"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from financing activities"
msgstr "Rahavoog finantseerimistegevustest"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from investing & extraordinary activities"
msgstr "Rahavoog investeerimisest ja plaanivälistest tegevustest"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from operating activities"
msgstr "Rahavood põhitegevusest"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr "Rahavood muudest tegevustest"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr "Sularaha sisse"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr "Sularaha välja"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash paid for operating activities"
msgstr "Põhitegevuse eest makstud sularaha"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Sularaha saadud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash received from operating activities"
msgstr "Põhitegevuse eest saadud sularaha"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Sularaha kulutatud"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Sularaha ülejääk"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Muuda lukustamise kuupäeva"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.js:0
#, python-format
msgid "Change expected date"
msgstr "Muuda eeldatavat kuupäeva"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#, python-format
msgid "Change expected payment date"
msgstr "Muuda eeldatavat maksekuupäeva"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Close"
msgstr "Sulge"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr "Sulgemise kanne"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Panga lõppsaldo"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
#, python-format
msgid "Codes:"
msgstr "Koodid:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "Veerud"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "Kommunikatsioon"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
#, python-format
msgid "Companies"
msgstr "Ettevõtted"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#, python-format
msgid "Company"
msgstr "Ettevõte"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"Company %s already belongs to a tax unit in %s. A company can at most be "
"part of one tax unit per country."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Currency"
msgstr "Ettevõtte valuuta"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#, python-format
msgid "Company Only"
msgstr "Ainult ettevõtte"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Settings"
msgstr "Ettevõtte seaded"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#, python-format
msgid "Comparison"
msgstr "Võrdlus"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Configure your TAX accounts - %s"
msgstr "Seadista oma maksude kontod - %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Seadista oma maksude kontod"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Müügikulud (kaubad, toore, materjal ja teenused)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Could not expand term %s while evaluating formula %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Could not parse account_code formula from token '%s'"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
#, python-format
msgid "Country"
msgstr "Riik"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Riigikood"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Loo kanne"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr "Loo menüükirje"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Loodud (kelle poolt?)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Loodud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
#, python-format
msgid "Credit"
msgstr "Kreedit"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "Valuuta"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Currency Rates (%s)"
msgstr "Valuutavahetuskursid (%s)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Currency:"
msgstr "Valuuta:"

#. module: account_reports
#: model:account.report.column,name:account_reports.deferred_expense_current
#: model:account.report.column,name:account_reports.deferred_revenue_current
msgid "Current"
msgstr "Praegune"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Käibevara"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Lühiajalised kohustused"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Jooksva aasta jaotatud kasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "Käesoleva aasta kasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Jooksva aasta jaotamata kasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Jooksev varade suhe kohustustesse"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr "Töötleja mudel"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr "Töötleja mudeli nimi"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#, python-format
msgid ""
"Custom engine _report_custom_engine_last_statement_balance_amount does not "
"support groupby"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#, python-format
msgid "Date"
msgstr "Kuupäev"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "Kuupäev ei saa olla tühi"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
#, python-format
msgid "Debit"
msgstr "Deebet"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Deductible"
msgstr "Kinnipeetav"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
#, python-format
msgid "Deferrals have already been generated."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Deferred Entries"
msgstr "Edasilükatud kirjed"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_expense
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_expense
msgid "Deferred Expense"
msgstr "Viitkulu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_expense_report_handler
msgid "Deferred Expense Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.deferred_expense_report
msgid "Deferred Expense Report"
msgstr "Edasilükatud kuluaruanne"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_revenue
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_revenue
msgid "Deferred Revenue"
msgstr "Viittulu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_revenue_report_handler
msgid "Deferred Revenue Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.deferred_revenue_report
msgid "Deferred Revenue Report"
msgstr "Edasilükatud tuluaruanne"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "Määratlus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Viivituse ühikud"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "Amortisatsioon"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Difference from rounding taxes"
msgstr "Maksude ümardusvahe"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_line__display_custom_groupby_warning
msgid "Display Custom Groupby Warning"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Dokumendi nimi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "Domeen"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#, python-format
msgid "Domestic"
msgstr "Riigisisene"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Download Anyway"
msgstr "Lae alla"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Laadige alla andmete muutmatuse kontrolliaruanne"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Due"
msgstr "Tähtaeg"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "Tähtaeg"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "EC müügilist"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr "EC müügiaruande töötleja"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "EC tax on non EC countries"
msgstr "EC maks mitte-EC riikidele"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "EC tax on same country"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "OMAKAPITAL"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Editing a manual report line is not allowed in multivat setup when "
"displaying data from all fiscal positions."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Editing a manual report line is not allowed when multiple companies are "
"selected."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Enable Sections"
msgstr "Luba jaotised"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
#, python-format
msgid "Enable more ..."
msgstr "Luba rohkem ..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#, python-format
msgid "End Balance"
msgstr "Lõppsaldo"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "End of Last Financial Year"
msgstr "Viimase majandusaasta lõpp"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "End of Last Month"
msgstr "Viimase kuu lõpp"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "End of Last Quarter"
msgstr "Viimase kvartali lõpp"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Ending Balance:"
msgstr "Lõppsaldo:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#, python-format
msgid "Engine"
msgstr "Mootor"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Entries with partners with no VAT"
msgstr "Partnerid, kelle kanded ei sisalda käibemaksu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Error message"
msgstr "Veateade"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
#, python-format
msgid "Exchange Rates"
msgstr "Vahetuskursid"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "Välistatud kontod"

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "Kommenteeritud kokkuvõte"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_expected_date
#: model:account.report.column,name:account_reports.aged_receivable_report_expected_date
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
msgid "Expected Date"
msgstr "Eeldatav kuupäev"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement(e.g: if"
" you had the customer on the phone and want to remember the date he promised"
" he would pay)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid ""
"Expected payment date for journal item %r has been changed from %s to %s on "
"journal entry %r"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "Kulukonto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Kulukonto"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Expense Provision for %s"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.report.line,name:account_reports.account_financial_report_expense0
#: model:account.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Kulud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Export"
msgstr "Eksport"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Ekspordi format raamatupidamisaruannete jaoks"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Eskspordi"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Ekspordi viisard raamatupidamisaruannete jaoks"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr "Väljend"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Expression labelled '%s' of line '%s' is being overwritten when computing "
"the current report. Make sure the cross-report aggregations of this report "
"only reference terms belonging to other reports."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "Väli"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Field %s does not exist on account.move.line."
msgstr "Väli %s ei eksisteeri account.move.line."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Field %s of account.move.line is not stored, and hence cannot be used in a "
"groupby expression"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Field 'Custom Handler Model' can only reference records inheriting from "
"[%s]."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_content
msgid "File Content"
msgstr "Faili sisu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "File Download Errors"
msgstr "Faili allalaadimisega seotud vead"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_generation_errors
msgid "File Generation Errors"
msgstr "Faili genereerimisega seotud vead"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_name
msgid "File Name"
msgstr "Faili nimi"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
#, python-format
msgid "Filters"
msgstr "Filtrid"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/aml_ir_filters.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
#, python-format
msgid "Filters:"
msgstr "Filtrid:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#, python-format
msgid "Fiscal Position:"
msgstr "Finantspositsioon"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr "FInantspositsioonid sünkroniseeritud"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid ""
"Fiscal Positions should apply to all companies of the tax unit. You may want"
" to"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__footnotes_ids
msgid "Footnotes"
msgstr "Joonealune märkus"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Valuutakursi korrigeerimiskanne %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#, python-format
msgid "Formula"
msgstr "Valem"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "From"
msgstr "Kellelt?"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"Alates %s\n"
"kuni  %s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr "Funktsiooni parameeter"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Funktsioon, mida kutsuda"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#, python-format
msgid "General Ledger"
msgstr "Pearaamat"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Generate entry"
msgstr "Genereeri kanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Loodud dokumendid"

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr "Üldine EC müügilist"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr "Üldise maksuaruande töötleja (Konto -> Maks)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr "Üldise maksuaruande töötleja (Maks -> Konto)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Global Tax Summary"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Go to report configuration"
msgstr "Mine aruande seadistusse"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Goods"
msgstr "Kaubad"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Grid"
msgstr "Võrgustik"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Brutokasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Brutokasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Brutokasumi marginaal (brutokasum/põhitegevuse tulud)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "Rühmitamine"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
#, python-format
msgid "Group By Months"
msgstr "Rühmita kuude järgi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "Grupi nimi"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Grouped Deferral Entry of %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Hide lines at 0"
msgstr "Peida read 0 juures"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Hierarchy and Subtotals"
msgstr "Hierarhia ja vahesummad"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
#, python-format
msgid "Horizontal Group"
msgstr "Horisontaalne grupp"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#, python-format
msgid "Horizontal Group:"
msgstr "Horisontaalne grupp:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Horizontal Grouping"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr "Horisontaalsed grupid"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr "Horisontaalne grupp aruannete jaoks"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Horizontal:"
msgstr "Horisontaalne:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Kui tihti maksudeklaratsioone tehakse"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Impact On Grid"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Impacted Tax Grids"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "In %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Inactive"
msgstr "Passiivne"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Include Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Include Draft Entries"
msgstr "Kaasa mustandkanded"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
#, python-format
msgid "Include Payments"
msgstr "Sisaldab makseid"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Including Analytic Simulations"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_payments
#: model:account.report.line,name:account_reports.unreconciled_last_statement_payments
msgid "Including Unreconciled Payments"
msgstr "Sisaldab sidumata tehinguid"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_receipt
#: model:account.report.line,name:account_reports.unreconciled_last_statement_receipts
msgid "Including Unreconciled Receipts"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Tulu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Tulukonto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Income Provision for %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#, python-format
msgid "Inconsistent Statements"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Inconsistent report_id in options dictionary. Options says %s; report is %s."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#, python-format
msgid "Initial Balance"
msgstr "Algsaldo"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
#, python-format
msgid "Insert foot note here"
msgstr "Kirjuta joonealune märkus siia"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Integer Rounding"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "Intra-community taxes are applied on"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_file_download_error_wizard.py:0
#, python-format
msgid "Invalid Partners"
msgstr "Kehtetud partnerid"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_file_download_error_wizard.py:0
#, python-format
msgid "Invalid Products"
msgstr "Vigased tooted"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_file_download_error_wizard.py:0
#, python-format
msgid "Invalid Taxes"
msgstr "Vigased maksud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Invalid domain formula in expression %r of line %r: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Invalid method %r"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Invalid subformula in expression %r of line %r: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Invalid token '%s' in account_codes formula '%s'"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:account.report.column,name:account_reports.journal_report_date
#: model:account.report.column,name:account_reports.partner_ledger_report_invoicing_date
msgid "Invoice Date"
msgstr "Arve kuupäev"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Andmik"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Groups"
msgstr "Andmike grupid"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#, python-format
msgid "Journal Items"
msgstr "Andmike kanderead"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Journal Items for Tax Audit"
msgstr "Andmiku kanderead maksuauditi jaoks"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Report"
msgstr "Andmiku aruanne"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Journal items with archived tax tags"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journals"
msgstr "Andmikud"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_journal.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
#, python-format
msgid "Journals:"
msgstr "Andmikud:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "KOHUSTUSED"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "KOHUSTUSED + OMAKAPITAL"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
#: model:account.report.column,name:account_reports.journal_report_partner_name
#, python-format
msgid "Label"
msgstr "Silt"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "Last Financial Year"
msgstr "Eelmine majandusaasta"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "Last Month"
msgstr "Eelmine kuu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "Last Quarter"
msgstr "Eelmine kvartal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement"
msgstr "Viimane väljavõte"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: account_reports
#: model:account.report.line,name:account_reports.last_statement_balance
msgid "Last statement balance"
msgstr "Viimase väljavõtte saldo"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Later"
msgstr "See aasta"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Latest Statement"
msgstr "Viimane väljavõte"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line_id
msgid "Line"
msgstr "Rida"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#, python-format
msgid "Line without formula"
msgstr "Rida ilma valemita"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "Read"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Load more..."
msgstr "Laadi rohkem... "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Peamine ettevõte"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#, python-format
msgid "Make Adjustment Entry"
msgstr "Tee korrigeerimise kanne"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Manual value"
msgstr "Manuaalne väärtus"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Manual values"
msgstr "Manuaalsed väärtused"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching"
msgstr "Sobitamine"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Method '%s' must start with the '%s' prefix."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.misc_operations
msgid "Misc. operations"
msgstr "Mitmesugused tegevused"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_file_download_error_wizard.py:0
#, python-format
msgid "Missing Company Data"
msgstr "Puuduvad ettevõtte andmed"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "Mudel"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
#, python-format
msgid "Name"
msgstr "Nimi"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Netokasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Netovarad"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Net increase in cash and cash equivalents"
msgstr "Raha ja raha ekvivalentide netokasv"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Netokasumi marginaal (netokasum/tulud)"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
#, python-format
msgid "No"
msgstr "Ei"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#, python-format
msgid "No Comparison"
msgstr "Võrdlus puudub"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "No VAT number associated with your company. Please define one."
msgstr "Ettevõttega seotud käibemaksunumber on puudu. Palun määrake."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No adjustment needed"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
#, python-format
msgid "No data to display !"
msgstr "Pole andmeid, mida näidata"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/chart_template.py:0
#, python-format
msgid "No default miscellaneous journal could be found for the active company"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "No entry to generate."
msgstr "Kanne mida luua puudub."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No provision needed was found."
msgstr "Ühtegi vajalikku sätet ei leitud."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "Non Trade Partners"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Non Trade Payable"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Non Trade Receivable"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Non-Deductible"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "None"
msgstr "Pole"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Not Started"
msgstr "Pole alustatud"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "Number of periods cannot be smaller than 1"
msgstr "Perioodide arv ei saa olla väiksem kui 1"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "BILANSIVÄLISED KONTOD"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo hoiatus"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "Vanemad"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "One of the formats chosen can not be exported in the DMS"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Ainult arvelduse administraatoritele on lubatud lukustamise kuupäevi muuta!"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Only Show Unreconciled Entries"
msgstr "Näita ainult sobitamata kandeid"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#, python-format
msgid "Open"
msgstr "Avatud"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Majandusaasta Algsaldo"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr "Tegevustulu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
#, python-format
msgid "Options"
msgstr "Valikud"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
#, python-format
msgid "Options:"
msgstr "Valikud:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "Muud tulud"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding
msgid "Outstanding Receipts/Payments"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "Peamise aruande ID"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Ülemviisard"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
msgid "Partner"
msgstr "Kontakti kaart"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Partner Categories"
msgstr "Partnerite kategooriad"

#. module: account_reports
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
msgid "Partner Ledger"
msgstr "Ostu- ja müügireskontro"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr "Partnerite aruanded"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#, python-format
msgid "Partner is bad"
msgstr "Partner on halb"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#, python-format
msgid "Partner is good"
msgstr "Partner on hea"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
#, python-format
msgid "Partners"
msgstr "Partnerid"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners Categories:"
msgstr "Partnerite kategooriad"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "Partnerid:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Payable"
msgstr "Võlad"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Payable tax amount"
msgstr "Tasumisele kuuluv maks"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Võlad ja ettemaksed"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Payments"
msgstr "Maksed"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Lao analüüs"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Period comparison"
msgstr "Perioodi võrdlus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Perioodilisus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Perioodilisus kuus"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
#, python-format
msgid "Plans"
msgstr "Plaanid"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Please set the deferred accounts in the accounting settings."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Please set the deferred journal in the accounting settings."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Lisaks põhivarad"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Lisaks põhivarad"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Lisaks pikaajalised kohustused"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Amet"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
#, python-format
msgid "Post"
msgstr "Postita"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Posted Entries Only"
msgstr "Ainult postitatud kanded"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"Posting this entry will also update and post the unposted closings of the "
"depending companies."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Ettemaksud"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Andmete eelvaade"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#, python-format
msgid "Previous Period"
msgstr "Eelmine periood"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Eelmise aasta jaotamata kasum"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"Jätka hoiatusega, selles perioodis võib korrigeerimiskanne juba olemas olla"

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "Kasumiaruanne"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Kasumlikkus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr "Periodiseerimine %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#, python-format
msgid "Rates"
msgstr "Kursid"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Receivable"
msgstr "Nõuded"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Receivable tax amount"
msgstr "Enammakstud maksu summa"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Nõuded"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Ühendamise aruanne"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Meeldetuletus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__report_id
msgid "Report"
msgstr "Statistika"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "Aruande rida"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report Name"
msgstr "Raporti nimi"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Report lines mentioning the account code"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
#, python-format
msgid "Report:"
msgstr "Aruanne:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Aruandlus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "Aruanded"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Reset to Standard"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Jaotamata kasum"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Investeeringutasuvus (puhaskasum/varad)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Tagasipööramise kuupäev"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "Reversal of Grouped Deferral Entry of %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Tühistamine: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr "Juuraruanne"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "Reeglid"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#, python-format
msgid "Same Period Last Year"
msgstr "Samal perioodil eelmine aasta"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/dialog/expected_date_dialog.xml:0
#, python-format
msgid "Save"
msgstr "Salvesta"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
#, python-format
msgid "Search..."
msgstr "Otsing..."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Sections"
msgstr "Jaotised"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Services"
msgstr "Teenused"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_file_download_error_wizard.py:0
#, python-format
msgid "Settings"
msgstr "Seaded"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Lühiajaline raha prognoos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Kuva hoiatusi"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "Some"
msgstr "Mõni"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
#, python-format
msgid "Sort By Date"
msgstr "Sorteeri kuupäeva järgi"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Alustage"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Starting Balance:"
msgstr "Algsaldo"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#, python-format
msgid "Subformula"
msgstr "Alamvalem"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "T: %s"
msgstr "T: %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
#, python-format
msgid "Tags"
msgstr "Sildid"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Tax Amount"
msgstr "Maksu summa"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_alert
msgid "Tax Closing Alert"
msgstr "Maksu sulgemise teavitus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr "Maksu sulgemise lõppkuupäev"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_show_multi_closing_warning
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_show_multi_closing_warning
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_show_multi_closing_warning
msgid "Tax Closing Show Multi Closing Warning"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Declaration"
msgstr "Maksudeklaratsioon"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Tax Grids"
msgstr "Maksude võrgustik"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "KMKR nr"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Paid Adjustment"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Received Adjustment"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "Maksuaruanne"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr "Maksuaruande kontroll viga"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr "Maksudeklaratsioon"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr "Maksuperiood"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Maksuühik"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#, python-format
msgid "Tax Unit:"
msgstr "Maksuühik:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Maksuühikud"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Maksuaruanne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return for %s%s"
msgstr "Maksudeklaratsioon %s%s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return from %s to %s%s"
msgstr "Makudeklaratsioon alates %s kuni %s%s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid "Taxes"
msgstr "Maksud"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main_table_body
#, python-format
msgid "Taxes Applied"
msgstr "Maksud rakendatud"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid ""
"Technical field indicating whether Fiscal Positions exist for all companies "
"in the unit"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Tekst"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/ellipsis/ellipsis.js:0
#, python-format
msgid "Text copied"
msgstr "Tekst kopeeritud"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid ""
"The 'load more limit' setting of this report is too low to display all the "
"lines of the entry you're trying to show."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "The Accounts Coverage Report is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid ""
"The attachments of the tax report can be found on the <a href='#' data-oe-"
"model='account.move' data-oe-id='%s'>closing entry</a> of the representative"
" company."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"The country detected for this VAT number does not match the one set on this "
"Tax Unit."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "The currency rate cannot be equal to zero"
msgstr "Valuutakurss ei saa olla võrdne nulliga"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "The current balance in the"
msgstr "Jooksev saldo"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
#, python-format
msgid "The entry that will be generated will take them into account."
msgstr "Kanne mis luuakse võtab need arvesse. "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "The main company of a tax unit has to be part of it."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid ""
"The selected report line does not target a Journal Entry or a Journal Item."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "The used operator is not supported for this expression."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
#, python-format
msgid "There are"
msgstr "Seal on"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
#, python-format
msgid "There is no data to display for the given filters."
msgstr "Andmed, mida valitud filtritega kuvada puuduvad. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "This Financial Year"
msgstr "See majandusaasta"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "This Month"
msgstr "See kuu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "This Quarter"
msgstr "See kvartal"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"This account exists in the Chart of Accounts but is not mentioned in any "
"line of the report"
msgstr ""
"Valitud konto on kontoplaanis olemas, kuid ei ole määratud ühelegi aruande "
"reale. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"This account is reported in a line of the report but does not exist in the "
"Chart of Accounts"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "This account is reported in multiple lines of the report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "This account is reported multiple times on the same line of the report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
#, python-format
msgid ""
"This company is part of a tax unit. You're currently not viewing the whole "
"unit."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
#, python-format
msgid ""
"This line and all its children will be deleted. Are you sure you want to "
"proceed?"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "This line uses a custom user-defined 'Group By' value."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "This option hides lines with a value of 0"
msgstr "See valik peidab 0 väärtused"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "This report already has a menuitem."
msgstr "Sellel aruandel on juba menüüväli."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid ""
"This report is linked to a custom handler.\n"
"                        You can customize it manually, but make sure your changes don't clash with what the handler does."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
#, python-format
msgid "This report only displays the data of the active company."
msgstr "See aruanne näitab ainult aktiivse ettevõtte andmeid."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "This subformula references an unknown expression: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"This tag is reported in a line of the report but is not linked to any "
"account of the Chart of Accounts"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"This tax closing entry is posted, but the tax lock date is earlier than the "
"covered period's last day. You might need to reset it to draft and refresh "
"its content, in case other entries using taxes have been posted in the "
"meantime."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "Today"
msgstr "Täna"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
#, python-format
msgid "Total"
msgstr "Kokku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Total %s"
msgstr "Kokku %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#, python-format
msgid "Trade Partners"
msgstr "Kaubanduspartnerid"

#. module: account_reports
#: model:account.report.line,name:account_reports.transaction_without_statement
msgid "Transactions without statement"
msgstr "Tehingud ilma väljavõtteta"

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "Käibeandmik"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr "Käibeandmiku töötleja"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Triangular"
msgstr "Kolmnurkne"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Trying to dispatch an action on a report not compatible with the provided "
"options."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Trying to expand a group for a line which was not generated by a report "
"line: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Trying to expand a line without an expansion function."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#, python-format
msgid ""
"Trying to use the journal line expand function on a line that is not linked "
"to a journal."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Jaotamata kasum"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "Unfold All"
msgstr "Ava kõik"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Unknown"
msgstr "Tundmatu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Tundmatu partner"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Unknown bound criterium: %s"
msgstr "Tundmatu seotud kriteerium: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Unknown date scope: %s"
msgstr "Tundmatu kuupäeva kohaldamisala: %s"

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "Realiseerimata valuuta Kasum/Kahjum"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "KMKR nr"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.company_information
msgid "VAT:"
msgstr "KM:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#, python-format
msgid "Value"
msgstr "Väärtus"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Vat closing from %s to %s"
msgstr "Käibemaksu sulgemine alates %s kuni %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "View Bank Statement"
msgstr "Vaata pangaväljavõtet"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "View Carryover Lines"
msgstr "Vaata ülekande ridu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "View Journal Entry"
msgstr "Vaata andmiku kannet"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "View Partner"
msgstr "Vaata partnerit"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "View Payment"
msgstr "Vaata makset"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""
"Kui see on märgitud, kuvatakse aruande sektsioonide all summad ja "
"vahesummad."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""
"Kui see on märgitud, kuvatakse aruande sektsioonide all summad ja "
"vahesummad."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#, python-format
msgid "With Draft Entries"
msgstr "Koos mustandkannetega"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Wrong ID for general ledger line to expand: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
#, python-format
msgid "Yes"
msgstr "Jah"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
#, python-format
msgid "You are using custom exchange rates."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "You can't open a tax report from a move without a VAT closing date."
msgstr "Maksuaruannet ei saa avada ilma käibemaksu sulgemise kuupäevata."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move_line.py:0
#, python-format
msgid "You cannot add taxes on a tax closing move line."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid ""
"You cannot generate entries for a period that does not end at the end of the"
" month."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#, python-format
msgid "You cannot generate entries for a period that is locked."
msgstr "Kandeid ei saa luua lukustatud perioodi."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset this closing entry to draft, as another closing entry has "
"been posted at a later date."
msgstr ""
"Sulgemise kannet ei saa mustandisse lähtestada, kuna järgmine sulgemise "
"kanne on postitatud hilisema kuupäevaga. "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset this closing entry to draft, as it would delete carryover "
"values impacting the tax report of a locked period. To do this, you first "
"need to modify you tax return lock date."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "You need to activate more than one currency to access this report."
msgstr "Selle aruande vaatamiseks tuleb aktiveerida rohkem kui üks valuuta. "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr "[MUSTAND]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "addressed to"
msgstr "adresseeritud"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "kord aastas"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid "any"
msgstr "vähemalt üks"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "päeva pärast perioodi"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "doesn't match the balance of your"
msgstr "ei sobitu saldoga"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "iga 2 kuu tagant"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "iga 4 kuu tagant"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "have a starting balance different from the previous ending balance."
msgstr "on algsaldo erinev eelmise lõppsaldost. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "in the next period."
msgstr "järgmisel perioodil."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "invoices"
msgstr "arved"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "journal items"
msgstr "andmike kanderead"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "last bank statement"
msgstr "viimane pangaväljavõte"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "igakuiselt"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "partners"
msgstr "partnerid"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
#, python-format
msgid "prior or included in this period."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "kord kvartalis"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "kord poolaastas"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "statements"
msgstr "väljavõtted"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr "sünkroniseeri finantspositsioonid"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "tax unit [%s]"
msgstr "maksu ühik [%s]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "that are not established abroad."
msgstr "mida välismaal ei ole loodud."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#, python-format
msgid "to"
msgstr "kuni"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
#, python-format
msgid "unposted Journal Entries"
msgstr "postitamata andmike kanded"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "were carried over to this line from previous period."
msgstr "toodi üle sellele reale eelmisest perioodist. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#, python-format
msgid "which don't originate from a bank statement nor payment."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "who are not established in any of the EC countries."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "will be carried over to"
msgstr "kantakse üle"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
#, python-format
msgid "will be carried over to this line in the next period."
msgstr "viiakse üle järgmisse perioodi"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
#, python-format
msgid "without a valid intra-community VAT number."
msgstr "ilma kehtiva käibemaksunumbrita. "

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
#, python-format
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Lähtesta Odoo kursile"
