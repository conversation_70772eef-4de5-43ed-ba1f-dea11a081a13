# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder_iot
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields,field_description:quality_mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr "פעולה"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__fail
msgid "Fail"
msgstr "נכשל"

#. module: quality_mrp_workorder_iot
#: model:ir.model,name:quality_mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr "טריגר IOT"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__pass
msgid "Pass"
msgstr "עבר"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__measure
msgid "Take Measure"
msgstr ""
