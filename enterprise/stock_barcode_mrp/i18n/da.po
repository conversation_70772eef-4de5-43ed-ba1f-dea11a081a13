# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_mrp
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-23 16:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\" "
"invisible=\"product_tracking not in ['serial', 'lot']\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\" "
"invisible=\"product_tracking not in ['serial', 'lot']\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\" "
"invisible=\"tracking not in ['serial', 'lot']\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\" "
"invisible=\"tracking not in ['serial', 'lot']\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Destination "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Destination "
"Location\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center me-3\" title=\"Source "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center me-3\" title=\"Source "
"Location\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> Print stregkode eksempel ark"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
#, python-format
msgid "Add Component"
msgstr "Tilføj Komponent"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow full order validation"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "Gør det muligt at oprette nye lot/serie numre for komponenter"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow user to produce all even if no components were scanned"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
#, python-format
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.actions.client,name:stock_barcode_mrp.stock_barcode_mo_client_action
msgid "Barcode MO Client Action"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Stregkode plan"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Cancel Manufacturing Order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
#, python-format
msgid "Cancel manufacturing order?"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Konfigurér produktstregkoder"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Confirm"
msgstr "Bekræft"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_picking_type__count_mo_confirmed
msgid "Count Mo Confirmed"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "Opret nye lots/serie numre for komponenter"

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Create a new Manufacturing Order"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Destination Location"
msgstr "Destinations lokation"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Discard"
msgstr "Kassér"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__is_completed
msgid "Is Completed"
msgstr "Er fuldført"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid "Manual Consumption"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_byproduct_line_ids
msgid "Move Byproduct Line"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_raw_line_ids
msgid "Move Raw Line"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__backorder_ids
msgid "Mrp Production"
msgstr "MRP Produktion"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "New"
msgstr "Ny"

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
#, python-format
msgid "No Manufacturing Order ready for this %(barcode_type)s"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
#, python-format
msgid "No product or order found for barcode %s"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.actions.act_window,name:stock_barcode_mrp.mrp_action_kanban
msgid "Operations"
msgstr "Operationer"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Pluk type"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Print Finished Product Label (PDF)"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Print Finished Product Label (ZPL)"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Print Production Order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
#, python-format
msgid "Produce"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
#, python-format
msgid "Produce All"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Product"
msgstr "Produkt"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produkt bevægelser (Lagerbevægelse linje)"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_product_product
msgid "Product Variant"
msgstr "Varevariant"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Product not Allowed"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_mrp_production
msgid "Production Order"
msgstr "Produktionsordre"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Quantity"
msgstr "Antal"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Scan a component"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
#, python-format
msgid "Scan a product to filter the orders."
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban.xml:0
#, python-format
msgid "Scan a transfer, a product or a package to filter your records"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban.xml:0
#, python-format
msgid "Scan an order or a product to filter your records"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "Scan your final product or more components"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.stock_move_line_product_selector
msgid "Serial/Lot Number"
msgstr "Serie/lot nummer"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Source Location"
msgstr "Kilde placering"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytning"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "The Manufacturing Order has been cancelled"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_picking_model.js:0
#, python-format
msgid ""
"The lines with a kit have been replaced with their components. Please check "
"the picking before the final validation."
msgstr ""
"Linjerne med et varesæt er blevet udskiftet med deres komponenter. "
"Kontroller venligst plukningen inden den endelige validering."

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "The manufacturing order has been validated"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "This order is already done"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "This order is cancelled"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.stock_mrp_picking_type_kanban
msgid "To Process"
msgstr "At behandle"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "To produce more products create a new MO."
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Overførsler gør det muligt for dig at flytte produkter fra en lokation til "
"en anden."

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.scrap_product_selector
msgid "Unit of Measure"
msgstr "Enhed"

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
#, python-format
msgid "You can't add the final product of a MO as a byproduct."
msgstr ""
