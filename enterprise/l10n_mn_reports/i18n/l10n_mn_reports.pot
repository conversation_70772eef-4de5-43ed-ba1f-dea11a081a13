# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mn_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~16.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-04 12:37+0000\n"
"PO-Revision-Date: 2023-09-04 12:37+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1 Cash flow from operating activities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line1
msgid "1. Amount of total income (1=2+...+5)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1 Cash income total (+)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca
msgid "1.1 Current Assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line2
msgid "1.1. Amount of tax-exempt income (2)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca1
msgid "1.1.1 Cash and cash equivalents"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.1 Income from the sale of goods and services"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.2 Revenue from rights commissions, fees and payments"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca2
msgid "1.1.2 Trade Receivables"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.3 Money received from the insured spouse"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca3
msgid "1.1.3 Tax and Social security contributions receivables"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca4
msgid "1.1.4 Other Non-Trade Receivables"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.4 Tax refunds"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca5
msgid "1.1.5 Other Financial Assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.5 Subsidy and financing income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca6
msgid "1.1.6 Inventory"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.1.6 Other cash income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca7
msgid "1.1.7 Prepaid expenses/bills"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca8
msgid "1.1.8 Other Current Assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca9
msgid "1.1.9 Non-current assets held for sale (assets for sale)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2 Amount of monetary expenditure (-)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca
msgid "1.2 Non-current Assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line3
msgid "1.2. Amount of income subject to tax at a special rate (3)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca1
msgid "1.2.1 Fixed Assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.1 Paid to Employees"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca2
msgid "1.2.2 Intangible Assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.2 Paid to the Social Insurance Institution"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca3
msgid "1.2.3 Biological Assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.3 Paid for the purchase of inventory"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca4
msgid "1.2.4 Long-term Investment"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.4 Paid for Operating Expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca5
msgid "1.2.5 Exploration and evaluation assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.5 Paid for fuel, transportation fees, and spare parts"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca6
msgid "1.2.6 Deferred tax assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.6 Interest paid"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.7 Paid to the tax authorities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca7
msgid "1.2.7 Real estate for investment"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancenca8
msgid "1.2.8 Other Non-current Assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.8 Paid for insurance"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "1.2.9 Other monetary expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line4
msgid ""
"1.3 Amount of other income (unrealized income from foreign exchange rate "
"difference, etc.) (4)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line5_new
msgid "1.4 Taxable income at the common rate (5=6+...+16)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line6
msgid "1.4.1 Income from sales of goods, works and services (6)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line15
msgid "1.4.10 Actual income from foreign exchange rate difference (15)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line16
msgid "1.4.11 Other taxable income (16)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line7
msgid ""
"1.4.2 Income from technical, management, consulting and other services (7)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line8
msgid "1.4.3 Income from the use and rental of real estate (8)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line9
msgid "1.4.4 Income from use and rental of movable property (9)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line10
msgid ""
"1.4.5 Income from goods, work and services received from others without "
"payment (10)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line11
msgid ""
"1.4.6 Income from interest, interest /fines, penalties/, compensation for "
"damages received from the person who failed to fulfill his obligations under"
" the contract (11)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line12
msgid "1.4.7 Income from paid puzzles, gambling games, cash lotteries (12)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line13
msgid ""
"1.4.8 Income from the sale of shares, securities and other financial "
"instruments (13)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line14
msgid ""
"1.4.9 Income from the sale or transfer of other intangible assets and "
"movable property /except for the income specified in clauses 10.1.2 and "
"10.1.3 of the law/ (14)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line28
msgid "10. Amount of income to be taxed at the common rate (28=26-27)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line29
msgid ""
"11. Taxes that have been imposed (28 * percentage specified in 20.1 and "
"20.2.7 of the law) (29=28*10%)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line30
msgid "12. Deductible tax according to Article 22.5 of the Law (30)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line31
msgid "13. OFFICIAL TAX TO BE PAYABLE AS A TOTAL PERCENTAGE (31=29-30)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line32
msgid ""
"14. Income subject to tax at a special rate "
"(32=33+38+39+40+41+42+44+45+47+49)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line33_new
msgid ""
"15. Income from the sale and transfer of rights granted by the state "
"organization (33)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line38
msgid "16. Income from rights fees (38)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line39
msgid "17. Dividend income (39)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line40
msgid ""
"18. Funds returned in accordance with Article 9.11 of the Law on "
"Environmental Impact Assessment and Article 11.1.4, 12.5 of the Law on "
"Petroleum (40)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line41
msgid "19. Income from insurance compensation (41)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2 Cash flows from investing activities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line17_new
msgid "2. Amount of total expenses (17=18+19+20)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1 Amount of cash income (+)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line18
msgid "2.1 Cost of goods sold (18)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceliab
msgid "2.1 Liabilities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.1 Income from sale of fixed assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab
msgid "2.1.1 Short-term liabilities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab1
msgid "2.1.1.1 Trade Payables"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab10
msgid "2.1.1.10 Other short-term liabilities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab11
msgid ""
"2.1.1.11 Liabilities related to non-current assets held for sale (assets for"
" sale)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab2
msgid "2.1.1.2 Salary Payables"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab3
msgid "2.1.1.3 Tax Payables"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab4
msgid "2.1.1.4 Payables for Social Security"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab5
msgid "2.1.1.5 Short term loan"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab6
msgid "2.1.1.6 Interest payments"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab7
msgid "2.1.1.7 Dividend Payable"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab8
msgid "2.1.1.8 Prepaid income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancecliab9
msgid "2.1.1.9 Contingent liabilities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.2 Income from the sale of intangible assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancencliab
msgid "2.1.2 Long-term liabilities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancencliab1
msgid "2.1.2.1 Long-term loan"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancencliab2
msgid "2.1.2.2 Contingent liabilities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancencliab3
msgid "2.1.2.3 Deferred tax debt"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancencliab4
msgid "2.1.2.4 Other long-term liabilities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.3 Income from sale of investments"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.4 Income from the sale of other long-term assets"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.5 Repayment of loans and cash advances granted to others"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.6 Interest income received"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.1.7 Dividends Received"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line19
msgid "2.2 Administrative and selling expenses (19)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.2 Amount of monetary expenditure (-)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity
msgid "2.2 Owners' equity"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity1
msgid "2.2.1 Equity - State owned"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.2.1 Paid for acquisition and possession of fixed assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity2
msgid "2.2.2 Equity - Private"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.2.2 Paid for the acquisition and possession of intangible assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity3
msgid "2.2.3 Ownership - Equity"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.2.3 Paid for acquisition of investment"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid ""
"2.2.4 Paid for the acquisition and possession of other long-term assets"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity4
msgid "2.2.4 Treasure Shares"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "2.2.5 Loans and advances to others"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity5
msgid "2.2.5 Share Premium"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity6
msgid "2.2.6 Property Revaluation Reserve"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity7
msgid "2.2.7 General Reserve"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity8
msgid "2.2.8 Other part of Owners' Equity"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity9
msgid "2.2.9 Retained earnings"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity9_current_year_earning
msgid "2.2.9.1 Retained earnings for the current period"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceequity9_previous_year_earning
msgid "2.2.9.2 Retained earnings from previous periods"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line20
msgid "2.3 Non-core operating expenses (20)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line42
msgid "20. Interest income (42)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line44
msgid ""
"21. Interest income from loans and debt instruments drawn from foreign and "
"domestic sources of commercial banks of Mongolia (44)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line45
msgid ""
"22. Does not hold a special license for the exploration or exploitation of "
"minerals, radioactive minerals, or oil /This includes the persons specified "
"in Articles 4.1.12 and 30.1 of this law./ Primary and secondary markets for "
"foreign and domestic securities of taxpayers located in Mongolia Interest "
"income of taxpayers who have purchased publicly traded debt instruments and "
"unit rights (45)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line47
msgid "23. Income from the sale and transfer of immovable property (47)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line49
msgid "24. Earnings from paid puzzles, gambling games, cash lotteries (49)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line51
msgid "25. OFFICIAL TAX IMPOSED AT A SPECIAL RATE (51=37+43+46+48+50)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line52_new
msgid "26. Tax withheld from others according to the law (52)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line53_new
msgid ""
"27. Taxes paid in foreign countries to be deducted from the applicable tax "
"(53)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line54
msgid "28. AMOUNT OF PAYABLE TAX (54=31+51-52-53)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line55
msgid ""
"29. The amount calculated for tax refund according to Article 22.1 of the "
"Law (55)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3 Cash flow from financing activities"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line21
msgid "3. Profit before tax +, loss - (21=1-17)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.1 Amount of cash income (+)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.1.1 Received from borrowing and issuing debt securities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.1.2 Received from the issuance of shares and other equity securities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.1.3 Miscellaneous Donations"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2 Amount of monetary expenditure (-)"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2.1 Amounts paid for loans and debt securities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2.2 Payables for finance leases"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2.3 Paid for repurchase of shares"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2.4 Dividends Paid"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "3.2.5 Various donations, assistance and fines paid"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line57
msgid ""
"30. Exemption from income tax for enterprises with reduced rent payments "
"(News HM(10) column 27 total amount) (57)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line58
msgid ""
"31. Information on exemption from corporate income tax / Amount of column 5,"
" line 31 of XM-02(12)/ (58)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line59
msgid "32. Total tax amount to be paid (59=31-(57+58)+51-52-53)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line22_new
msgid "4. Amount to increase profit and loss before paying tax (22)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line23_new
msgid "5. Amount to reduce profit and loss before paying taxes (23)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line24
msgid "6. Taxable income (24=21+22-23)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line25
msgid "7. Excess of voluntary insurance premiums (25)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line26
msgid "8. Adjusted taxable income amount (26=24+25)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line27_new
msgid ""
"9. The amount transferred during the reporting period from the losses "
"confirmed by the tax authorities in the tax returns of previous years (27)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_lineA
msgid "A. Calculation of tax at common rate"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceta
msgid "ASSETS"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "All net cash flows"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_lineB
msgid "B. Calculation of special rate tax"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report,name:l10n_mn_reports.account_report_balancesheet
msgid "Balance Sheet"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_lineC
msgid "C. Computation of taxes withheld by others according to law"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Closing balance of cash and cash equivalents"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report,name:l10n_mn_reports.account_report_tax_report
msgid "Corporate Revenue Tax Report"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gross_profit_cost
msgid "Cost of Sales"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_lineD
msgid ""
"D. Calculation of tax deductions and exemptions from corporate income tax "
"according to the Law on deductions and exemptions from corporate income tax "
"(56)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_income_devidend
msgid "Dividend Income"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Exchange rate differences"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_expense_finance
msgid "Financial expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gainloss_exchange
msgid "Foreign exchange gain (loss)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gainloss_asset
msgid "Gain (loss) on fixed asset write-off"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gainloss_intangible
msgid "Gain (loss) on intangible asset write-off"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gainloss_investment
msgid "Gain (loss) on sale of investment"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_expense_admin
msgid "General and administrative expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gross_profit
msgid "Gross Profit (loss)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line35_new
msgid ""
"In case of purchase or transfer from others, the payment paid for the sale "
"or transfer according to the agreement and evidenced by the transfer "
"document (35)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line34_new
msgid ""
"In the event that a right is granted by a state organization, payments and "
"fees that can be proven by documents paid to a state organization in "
"connection with obtaining a right (34)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_profit_tax
msgid "Income Tax"
msgstr ""

#. module: l10n_mn_reports
#. odoo-python
#: code:addons/l10n_mn_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Initial balance of cash and cash equivalents"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_income_interest
msgid "Interest Income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balancele
msgid "LIABILITIES AND EQUITIES"
msgstr ""

#. module: l10n_mn_reports
#: model:ir.model,name:l10n_mn_reports.model_l10n_mn_cash_flow_report_handler
msgid "Mongolian Cash Flow Report Custom Handler"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_net_profit
msgid "Net profit (loss) for the current period"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.account_financial_report_mn_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_income_other
msgid "Other Income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_other
msgid "Other comprehensive income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_expense_other
msgid "Other expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gainloss_other
msgid "Other gain (loss)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca31
msgid "Prepaid Taxes"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca72
msgid "Prepaid bills"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca71
msgid "Prepaid expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_balanceca32
msgid "Prepaid insurance Social security contributions"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_after_tax
msgid "Profit (loss) after tax"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_retained_earn
msgid "Profit (loss) before tax"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_profit_root
msgid "Profit and Loss"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report,name:l10n_mn_reports.account_report_profit_and_loss
msgid "Profit and Loss Report"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_income_loan
msgid "Rental Income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_income_loyalty
msgid "Royalty Income"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_gross_profit_sale
msgid "Sales Revenue (NET)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_expense_sale
msgid "Sales and marketing expenses"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report,name:l10n_mn_reports.account_report_cashflow
msgid "Statement of cash flows"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line37bis
msgid "Tax assessed in addition to assessment"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line43
msgid "Tax imposed (43=(38+39+40+41+42)*10%)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line46
msgid "Tax imposed (46=(44+45)*5%)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line48
msgid "Tax imposed (48=47*2%)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line50
msgid "Tax imposed (50=49*40%)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.report_line_delayed
msgid "Tax on discontinued operations"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line37
msgid ""
"Tax on proceeds from the sale and transfer of rights ((36 * 10%) + "
"Additional tax on assessment) (37)"
msgstr ""

#. module: l10n_mn_reports
#: model:account.report.line,name:l10n_mn_reports.tax_report_line36
msgid "Taxable income (36=33-34-35)"
msgstr ""
