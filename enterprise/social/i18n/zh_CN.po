# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# Wil Odoo, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> CHEN <<EMAIL>>, 2023
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: 湘子 南 <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# 科目"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "%s (max %s chars)"
msgstr "%s（最多 %s 个字符）"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ", you must first link a social media."
msgstr "，你必须首先链接一个社交媒体。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>在尝试链接您的账户时发生了错误</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe ms-1\" title=\"查看帖文\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr "<i class=\"oi oi-arrow-left\"/> 返回 Odoo"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<small class=\"pe-1\">Clicks:</small>"
msgstr "<small class=\"pe-1\">点击：</small>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<small class=\"pe-1\">Engagement:</small>"
msgstr "<small class=\"pe-1\">参与度:</small>"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr "<strong>恭喜！几分钟后回来检查您的统计数据。</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "所需操作"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "已启用"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "活动"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add"
msgstr "添加"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "添加文章"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add a Stream"
msgstr "添加一个流媒体"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "添加一个流媒体"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Add an image"
msgstr "添加图像"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "All Companies"
msgstr "所有公司"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "所有相关的社区媒体消息"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "允许的账户"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "已存档"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "是否账户可用？"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "附加图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "附件计数"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
#, python-format
msgid "Audience"
msgstr "听众"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "受众趋势"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "作者链接"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "作者名字"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid ""
"Author link to the external social.media (ex: link to the Twitter Account)."
msgstr "作者与外部社交媒体的链接（例如：与Twitter账户的链接）。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr "在发布之前，链接将被转换为可跟踪。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "未知流媒体"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF令牌"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "日历日期"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "活动"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "活动"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr "营销营销是用来集中您的营销努力并跟踪其结果。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr "可以链接账户吗？"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Choose which <b>account</b> you would like to link first."
msgstr "首先选择你想链接的<b>账户</b>。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Click to refresh."
msgstr "点击刷新。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "点击"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Comment Image"
msgstr "评论图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "公司"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "配置"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Connecting Problem"
msgstr "连接问题"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' Twitter account"
msgstr "包含创建此帐户的人员的社交媒体句柄。例如：“@baidu.official”代表“ERP ”Twitter帐户"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr "经过后处理的社交发布消息的内容（链接被缩短，UTMs，...）。"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr "控制是否在该社交媒体上处理社交流媒体。"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "控制我们是否可以连接账户。"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "创建营销"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "创建新文章"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
#, python-format
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr "为%(media_names)s创建该公司的其他账户或要求%(company_names)s分享他们的账户"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "创建人"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "创建日期"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"定义该账户是否有受众/参与/故事统计。\n"
"        有统计资料的账户会显示在仪表板上。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "定义该账户是否有统计资料的趋向。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Delete Comment"
msgstr "删除评论"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "演示模式"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "描述"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "开发者帐户"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "Do you really want to delete %s"
msgstr "您真的要删除 %s 吗？"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "草稿"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"由于篇幅限制，以下帖子无法发布：\n"
" %s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Edit Comment"
msgstr "编辑评论"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "启用演示模式"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr "启用此选项，并加载演示数据，以测试社交媒体模块。不能用于生产数据库！"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Engagement"
msgstr "预约"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "签约趋势"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr "每次创建帐户时，都会创建一个 utm.medium 并与帐户链接"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "失败的"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "失败原因"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Feed Posts"
msgstr "Feed 帖子"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Feed帖子计数"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "格式化的发布日期"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr "社会账户的一般受众（页面赞、账户追随者......）。"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "去"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "分组方式"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "句柄/简称"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Happy with the result? Let's post it!"
msgstr "对结果满意吗？让我们把它张贴出来!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "拥有账户统计信息"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "有消息"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "有趋势吗？"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "已孵化"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "图像网址"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Image Url"
msgstr "图像地址"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "图像URL"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "与本帖分享的图像。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Insights"
msgstr "分析"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr "作者"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr "社会媒体链接似乎有问题，点击这里重新链接账户"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "It will appear in the Feed once it has posts to display."
msgstr "一旦有帖子显示，它就会出现在Feed中。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's <b>connect</b> to Facebook, LinkedIn or Twitter."
msgstr "让我们<b>连接</b>到Facebook、LinkedIn或Twitter。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "让我们创建你自己的<b>社交媒体</b>仪表板。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Let's start posting."
msgstr "让我们开始发帖吧。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Like"
msgstr "喜欢"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Likes"
msgstr "喜爱"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "链接描述"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Link Image"
msgstr "链接图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "链接图像URL"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "链接标题"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "链接网址"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Link a new account"
msgstr "关联新帐户"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "链接帐户"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "链接一个账户"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr "将一个账户与一个公司联系起来，以限制其使用，或保持空闲，让所有公司使用它。"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "目标媒体上实时帖子的链接。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "链接社交账户"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "链接到外部社交账户统计"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "与外部社交媒体的链接被破坏"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "可以为这个职位选择的账户列表。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "社交媒体的实时帖子"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Load more comments..."
msgstr "载入更多评论..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "最大柱子长度"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "媒介"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "媒体类型"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "消息"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__message_length
#: model:ir.model.fields,field_description:social.field_social_post_template__message_length
msgid "Message Length"
msgstr "消息长度"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Message posted"
msgstr "消息已发布"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr "部分信息已发布。这些是无法发布的信息：%s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "消息"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"大多数social.live.post直接从Ready到Posted/Failed，因为它们是对第三方API的一次调用的结果。\n"
"        对于那些通过批处理发送的帖子（如推送通知），也有一个 \"发布 \"状态。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "我的文章"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "我的流媒体"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "名称"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New Post"
msgstr "最新帖子"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "New content available"
msgstr "新增内容"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Next"
msgstr "下一页"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一活动日历事件"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "还没有社交账户!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "还没有社交流媒体!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "还没有添加流媒体!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "No comments yet."
msgstr "暂时没有评论."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "No social accounts configured, please contact your administrator."
msgstr "没有配置社交账户，请联系你的管理员。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Number of Followers of your channel"
msgstr "你的频道的粉丝数量"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "点击数"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr "与社交帖子的互动（喜欢、分享、评论...）数量"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "参与帖子的人数（赞、评论...）。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "参与你的帖子的人数（赞，评论，...）。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Re-tweets, ...)."
msgstr "从你的帖子创建的故事数量（分享、转发......）。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr "人们参与你的帖子的次数（喜欢、评论、分享...）。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Retweets...)"
msgstr "参与你的频道的人在他们的朋友或追随者的饲料上创建故事的次数（分享、转发......）。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Or add"
msgstr "或添加"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr "在规定的时间内，受众增加/减少的百分比。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr "在规定的时期内，参与度的增加/减少的百分比。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr "在规定时间内，故事的增加/减少的百分比。"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr "请至少指定一个账户来发布信息（用于发布ID(s) %s）。"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#, python-format
msgid "Post"
msgstr "过账"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
#, python-format
msgid "Post Image"
msgstr "文章图像"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
#, python-format
msgid "Post Images"
msgstr "文章图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "文章链接"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "发布消息"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "立即发布"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr "发布到外部社交媒体的链接（例如：链接到实际的Facebook帖子）。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "发布于"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "已过账"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "发布"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "帖子"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "按账户发帖"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Posts By Accounts"
msgstr "按账户发帖"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr "按回车键来发布。按Shift+Enter插入换行符。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "预览您的帖子"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Previous"
msgstr "上一页"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "立即发布你的帖子，或将其安排在稍后的时间。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "发布的"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "发布日期"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "发布日期"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "点评"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "就绪"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "相关社交媒体"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, Twitter, ...)."
msgstr "相关的社交媒体（Facebook, Twitter, ...）。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "相关社交账户"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Reply"
msgstr "回复"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "重试"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "安排"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "稍后安排"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "安排"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Selected accounts (%s) do not match the selected company (%s)"
msgstr "所选账户（%s）与所选公司（%s）不匹配。"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "立即发送"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "序列"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr "用于排序流媒体的序列（主要用于'Feed'看板视图）。"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr "设置帖子中可以发布的最大字符数。 0 表示无限制。"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "设置"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "社交帐户"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "社交帐户"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "社区在线提交"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "社交管理员"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
msgid "Social Marketing"
msgstr "社交营销"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#, python-format
msgid "Social Media"
msgstr "社交媒体"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "社交媒体帖子"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "社会职务"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "社交帖子模板"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "社交帖子模板"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "社交帖子"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "社交圈"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "社交流媒体帖"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "社交流媒体帖子图像附件"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "社会流媒体"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "新的社交用户电子邮件模板主题"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "社交:发布预定的帖子"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
#, python-format
msgid ""
"Something went wrong while posting the comment. \n"
"%s"
msgstr ""
"发表评论时出了问题。\n"
"%s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
#, python-format
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr "对不起，你不允许重新链接这个账户，请联系你的管理员。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "来源"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr "特殊的技术字段，持有一个包含媒体ID的实时帖子名称的数据（用于看板视图）。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "统计数据链接"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "状态"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
#, python-format
msgid "Stories"
msgstr "故事"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "故事趋势"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#, python-format
msgid "Stream Added (%s)"
msgstr "添加的流媒体（%s）。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "流媒体帖子"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "流媒体帖子图像"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "流媒体图像URL"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "流媒体类型"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "流媒体类型名称（技术性）"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "启用的流媒体"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr "将在每个选定的社交账户上发布的子帖子。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Synchronize"
msgstr "同步"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "The 'message' field is required for post ID %s"
msgstr "帖子 ID %s 必须填写 \"信息 \"字段"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "这篇帖子将在哪些账户上发布。"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr "基于第三方信息的帖子作者名称（例如：\"John Doe\"）。"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr "当帖子的所有子帖子（每个社交账户一个）都是 \"失败 \"或 \"发布 \"时，该帖子被认为是 \"发布\"。"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "该帖子的发布日期基于第三方信息。"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr "一个帖子没有成功发布在社交媒体上的原因（例如：连接错误，重复的帖子，...）。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "相关社交媒体的形象"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "与所选社交账户相连的社交媒体。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "子帖子上有帖子错误"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr "该令牌可用于验证来自社会供应商的传入请求是否是伪造的。"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "称谓"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "To add a stream"
msgstr "要添加一个流媒体"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "类型"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM营销"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM媒介"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "UTM来源"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "Unknown error"
msgstr "未知错误"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
#, python-format
msgid "Uploaded file does not seem to be a valid image."
msgstr "上传的文件似乎不是一个有效的图像。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr "在我们的社交应用程序上使用您的开发者账户。在您的专业社交媒体账户的开发者部分提供该等证书。"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr "当我们需要将某些功能限制在特定的媒体（'facebook', 'twitter', ...）时，用于进行比较。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "View"
msgstr "视图"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "时间"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr "全局帖子的发布时间。实际的子帖子发表日期可能不同，这取决于媒体的情况。"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr "会在你的帖子中附上图像（如果社交媒体支持的话）。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a comment..."
msgstr "写一个评论..."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
#, python-format
msgid "Write a message to get a preview of your post."
msgstr "为了预览效果你得写点什么字才行。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "Write a reply..."
msgstr "写一个回复…"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr "撰写一篇诱人的文章，添加图像，并安排在稍后一次在多个平台发布。"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
#, python-format
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"您无法删除这些UTM媒体，因为它们链接到社交中的以下社交帐户：\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"您无法删除这些UTM来源，因为它们链接到社交中的社交帖子：\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "You cannot reschedule a post that has already been posted."
msgstr "不能重新安排已发布的帖子。"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#, python-format
msgid "You cannot schedule a post in the past."
msgstr "不能安排之前的帖子。"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "您的帖子"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "a Stream from an existing account"
msgstr "a 来自现有帐户的流"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "在发帖之前。"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#, python-format
msgid "comment/reply"
msgstr "评论/回复"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "仪表盘"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "for"
msgstr "由于"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#, python-format
msgid "replies..."
msgstr "回复..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr "来关注你自己的帖子，并监控所有的社交活动。"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "来链接你们的账户并开始发布信息。"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "以开始从Odoo发帖。"
