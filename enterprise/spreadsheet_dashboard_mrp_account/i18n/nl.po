# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_mrp_account
# 
# Translators:
# Wil Odoo, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Available time (min)"
msgstr "Beschikbare tijd (min)"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Average Cost / Unit"
msgstr "Gemiddelde kosten / eenheid"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Average cost / unit"
msgstr "Gemiddelde kosten / eenheid"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Huidig"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_mrp_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_mrp_account.spreadsheet_dashboard_manufacturing
msgid "Manufacturing"
msgstr "Productie"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Manufacturing Orders"
msgstr "Productieorders"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Manufacturing Orders by Operation Type"
msgstr "Productieorders per bewerkingstype"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Manufacturing Orders by Product"
msgstr "Productieorders per product"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Manufacturing Orders by Responsible"
msgstr "Productieorders per verantwoordelijke"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Manufacturing orders"
msgstr "Productieorders"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Most Produced Products"
msgstr "Meest geproduceerde producten"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "OEE"
msgstr "OEE"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Operation Type"
msgstr "Bewerkingstype"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Orders"
msgstr "Orders"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Periode"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Vorige"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Product"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Productive time (min)"
msgstr "Productieve tijd (min)"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Qty Produced"
msgstr "Geproduceerde hoeveelheid"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Quantity Produced"
msgstr "Hoeveelheid geproduceerd"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Responsible"
msgstr "Verantwoordelijke"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Top Operation Types"
msgstr "Beste bewerkingssoorten"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr "Beste producten"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Top Responsibles"
msgstr "Beste verantwoordelijke"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Total quantity"
msgstr "Totale hoeveelheid"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Total time (min)"
msgstr "Totale tijd (min)"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "Weekly Production"
msgstr "Wekelijkse productie"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "avg cost - current"
msgstr "gemiddelde kosten - huidig"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "avg cost - previous"
msgstr "gemiddelde kosten - vorige"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "oee - current"
msgstr "oee - huidig"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "oee - previous"
msgstr "oee - vorige"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "sinds vorige periode"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "stats mo - current"
msgstr "statistieken po - huidig"

#. module: spreadsheet_dashboard_mrp_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_mrp_account/data/files/manufacturing_dashboard.json:0
#, python-format
msgid "stats mo - previous"
msgstr "statistieken po - vorige"
