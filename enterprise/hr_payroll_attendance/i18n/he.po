# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_attendance
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: hr_payroll_attendance
#: model:ir.model.fields,field_description:hr_payroll_attendance.field_hr_payslip__attendance_count
msgid "Attendance Count"
msgstr ""

#. module: hr_payroll_attendance
#. odoo-python
#: code:addons/hr_payroll_attendance/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll_attendance.view_hr_payslip_form_inherit_payroll_attendance
#, python-format
msgid "Attendances"
msgstr "נוכחות"

#. module: hr_payroll_attendance
#: model_terms:ir.ui.view,arch_db:hr_payroll_attendance.hr_attendance_view_inherit_filter
msgid "Employee Code"
msgstr ""

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "חוזה עובד"

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_payslip
msgid "Pay Slip"
msgstr "תלוש שכר"

#. module: hr_payroll_attendance
#: model:ir.model,name:hr_payroll_attendance.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr ""
