# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account_enterprise
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_account_enterprise
#: model:ir.model.fields,help:stock_account_enterprise.field_stock_report__valuation
msgid ""
"Note that you can only access this value in the read_group, only the sum "
"operator is supported"
msgstr ""
"Tenga en cuenta que solo puede acceder a este valor en read_group, solo se "
"admite el operador de suma"

#. module: stock_account_enterprise
#: model:ir.model.fields,help:stock_account_enterprise.field_stock_report__stock_value
msgid ""
"Note that you can only access this value in the read_group, only the sum "
"operator is supported and only date_done is used from the domain"
msgstr ""
"Tenga en cuenta que solo puede acceder a este valor en read_group, solo se "
"admite el operador de suma y solo se utiliza date_done desde el dominio"

#. module: stock_account_enterprise
#: model:ir.model,name:stock_account_enterprise.model_stock_report
msgid "Stock Report"
msgstr "Reporte de existencias"

#. module: stock_account_enterprise
#: model:ir.model.fields,field_description:stock_account_enterprise.field_stock_report__stock_value
msgid "Total Valuation of Inventory"
msgstr "Valoración total del inventario"

#. module: stock_account_enterprise
#: model:ir.model.fields,field_description:stock_account_enterprise.field_stock_report__valuation
msgid "Valuation of Inventory using a Domain"
msgstr "Valoración del inventario que utiliza un dominio"
