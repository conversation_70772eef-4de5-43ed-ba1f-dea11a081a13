# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_account_enterprise
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2018-09-18 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"Language: el\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_account_enterprise
#: model:ir.model.fields,help:stock_account_enterprise.field_stock_report__valuation
msgid "Note that you can only access this value in the read_group, only the sum operator is supported"
msgstr ""

#. module: stock_account_enterprise
#: model:ir.model.fields,help:stock_account_enterprise.field_stock_report__stock_value
msgid "Note that you can only access this value in the read_group, only the sum operator is supported and only date_done is used from the domain"
msgstr ""

#. module: stock_account_enterprise
#: model:ir.model,name:stock_account_enterprise.model_stock_report
msgid "Stock Report"
msgstr ""

#. module: stock_account_enterprise
#: model:ir.model.fields,field_description:stock_account_enterprise.field_stock_report__stock_value
msgid "Total Valuation of Inventory"
msgstr ""

#. module: stock_account_enterprise
#: model:ir.model.fields,field_description:stock_account_enterprise.field_stock_report__valuation
msgid "Valuation of Inventory using a Domain"
msgstr ""
