# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# Jonatan Gk, 2023
# Harcogourmet, 2023
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2023
# oscaryuu, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# CristianCruz<PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# Guspy12, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# jabiri7, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-26 16:09+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Santiago Payà <<EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Aquesta és la seva nova acció.\n"
"                </p>\n"
"                <p>Per defecte, conté una llista i una vista de formulari i possiblement\n"
"                   altres tipus de vista depenent de les opcions que hagi escollit pel seu model.\n"
"                </p>\n"
"                <p>\n"
"                    Pots començar personalitzant aquestes pantalles fent clic a la icona de Studio a la\n"
"                    cantonada superior dreta (també pots personalitzar aquest missatge d'ajuda aquí).\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#, python-format
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Afegir un nou informe\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Afegir un nou filtre\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "\" failed."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "%(rule_string)s (%(model_name)s)"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
#, python-format
msgid "%s Report"
msgstr "%s Informe"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#, python-format
msgid "'My model'"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Afegeix una nova llista de control d'accés\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#, python-format
msgid "A field with the same name already exists."
msgstr "Un camp amb el mateix nom ja existeix"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr "Una regla només pot ser aprovada o rebutjada un cop per registre."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr "Una regla s'ha d'aplicar a una acció o mètode (però no a ambdós)"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "Una regla s'ha d'aplicar a una acció o mètode"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "Abstracte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Access Control"
msgstr "Control d'accés"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "Access Control Lists"
msgstr "Llistes de control d'accés"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "Grups d'accés"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
#, python-format
msgid "Access records from cell"
msgstr "Accés als registres des de la cel·la"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Access records from graph"
msgstr "Accés als registres des del gràfic"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "Acció"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "Finestra d'acció"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vista de la finestra d'acció"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Action to approve:"
msgstr "Acció a aprovar:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Action to run"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Action's title"
msgstr "Títol de l'acció"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Les accions poden disparar comportaments específics com ara obrir una vista "
"de calendari o marcar automàticament com a fet quan es puja un document"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#, python-format
msgid "Activate View"
msgstr "Activar vista"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
#, python-format
msgid "Active"
msgstr "Actiu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
#, python-format
msgid "Activity"
msgstr "Activitat"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipus d'activitat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "Activity view unavailable on this model"
msgstr "La vista d'activitats no està disponible en aquest model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#, python-format
msgid "Add"
msgstr "Afegir"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
#, python-format
msgid "Add Chatter Widget"
msgstr "Afegir widget de xat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#, python-format
msgid "Add Picture"
msgstr "Afegeix imatge"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
#, python-format
msgid "Add a Button"
msgstr "Afegir un botó"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Add a button"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#, python-format
msgid "Add a description..."
msgstr "Afegir una descripció..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#, python-format
msgid "Add a pipeline status bar"
msgstr "Afegir una barra d'estat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
#, python-format
msgid "Add a priority"
msgstr "Afegir una prioritat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#, python-format
msgid "Add an approval rule"
msgstr "Afegeix una regla d'aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
#, python-format
msgid "Add an avatar"
msgstr "Afegeix un avatar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Add details to your records with an embedded list view"
msgstr "Afegir detalls als registres amb una vista de llista incrustada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Add new value"
msgstr "Afegir un nou valor"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Add record at the bottom"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Add record on top"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
#, python-format
msgid "Add tags"
msgstr "Afegir etiquetes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Additional Fields"
msgstr "Camps addicionals"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "Aggregate"
msgstr "Agrega"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#, python-format
msgid "All Day"
msgstr "Tot el dia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__group_id
#, python-format
msgid "Allowed Group"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__responsible_id
msgid ""
"An activity will be assigned to this user when an approval is requested"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "An approval for '%(rule_name)s' has been requested on %(record_name)s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Approval"
msgstr "Aprovació"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "Entrades d'aprovació"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
msgid "Approval Rule"
msgstr "Regla d'aprovació"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Approval Rules"
msgstr "Regles d'aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#, python-format
msgid "Approvals"
msgstr "Aprovacions"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Approvals can only be done on a method or an action, not both."
msgstr ""
"Les aprovacions sols poden fer-se en un mètode o en una acció, no en ambdós."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Approvals missing"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Approve"
msgstr "Aprova"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "Aprovat"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved as"
msgstr "Aprovat com"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Approved on"
msgstr "Aprovat el"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "Aprovat/rebutjat per"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Archive deprecated records"
msgstr "Arxiu de registres obsolets"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
#, python-format
msgid "Archived"
msgstr "Arxivat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Archiving"
msgstr "Arxivant"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
#, python-format
msgid "Are you sure you want to remove the selection values?"
msgstr "Esteu segur que voleu esborrar els valors de selecció?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
#, python-format
msgid "Are you sure you want to remove this %s from the view?"
msgstr "Estàs segur que vols eliminar aquest %s de la vista?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
#, python-format
msgid "Are you sure you want to reset the background image?"
msgstr "Estàs segur que vols reiniciar la imatge de fons?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
#, python-format
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"Esteu segur que voleu restaurar la vista per defecte?\r\n"
"Totes les personalitzacions fetes amb studio en aquesta vista es perdran."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Ascending"
msgstr "Ascendent"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Assign a responsible to each record"
msgstr "Assigna un responsable a cada registre"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Assign dates and visualize records in a calendar"
msgstr "Assignar dates i visualitzar registres en un calendari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Attach a picture to a record"
msgstr "Agregar una imatge a un registre"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#, python-format
msgid "Autocompletion Fields"
msgstr "Camps que es completen automàticament"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Automations"
msgstr "Automatitzacions"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "Average"
msgstr "Mitjana"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Average of %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Awaiting approval"
msgstr "En espera d'aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Backwards"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#, python-format
msgid "Bar"
msgstr "Barra"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "Base"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "Blank"
msgstr "En blanc"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Blocked"
msgstr "Bloquejat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Bold"
msgstr "Negreta"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "Business header/footer"
msgstr "Capçalera/Peu de pàgina d'empresa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
#, python-format
msgid "Buttons Properties"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Calendar"
msgstr "Calendari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Call a method"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Can Create"
msgstr "Pot crear"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Can Delete"
msgstr "Pot eliminar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Can Edit"
msgstr "Es Pot Modificar"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "Es pot aprovar"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Can't patch private methods."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancel·la"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Categorize records with custom tags"
msgstr "Categoritza els registres amb etiquetes personalitzades"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#, python-format
msgid "Change Background"
msgstr "Canviar fons"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Change Image"
msgstr "Canviar imatge"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Chatter"
msgstr "Xerrada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "CheckBox"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "Marca't si s'ha editat amb Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Choose an app name"
msgstr "Escull un nom d'aplicació "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#, python-format
msgid "Choose the name of the menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Churn"
msgstr "Índex de cancel·lació "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
#, python-format
msgid "Click to see all approval rules."
msgstr "Fes clic per a veure totes les regles d'aprovació."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#, python-format
msgid "Close"
msgstr "Tancar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Cohort"
msgstr "Cohort"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Color"
msgstr "Color"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
#, python-format
msgid "Column grouping"
msgstr "Agrupació de columnes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
#, python-format
msgid "Columns"
msgstr "Columnes"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Company"
msgstr "Empresa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#, python-format
msgid "Components"
msgstr "Components"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
#, python-format
msgid "Conditional"
msgstr "Condicional"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "Regla condicional"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
#, python-format
msgid "Configuration"
msgstr "Configuració"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#, python-format
msgid "Configure Model"
msgstr "Configura el model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#, python-format
msgid "Configure model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
#, python-format
msgid "Confirmation"
msgstr "Confirmació"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Contact"
msgstr "Contacte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Contact Field"
msgstr "Camp de contacte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
#, python-format
msgid "Contact Field Required"
msgstr "Camp de contacte obligatori"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Contact details"
msgstr "Detalls del contacte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "Context"
msgstr "Context"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
#, python-format
msgid "Could not change the background"
msgstr "No s'ha pogut canviar el fons"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
#, python-format
msgid "Create Menu"
msgstr "Crea menú"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
#, python-format
msgid "Create Model"
msgstr "Crea un model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
#, python-format
msgid "Create a new Model"
msgstr "Crea un model nou"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Create your App"
msgstr "Crear la teva aplicació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Create your app"
msgstr "Crear la teva aplicació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Create your first menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
#, python-format
msgid "Create your menu"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
msgid "Created on"
msgstr "Creat el"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "Creating this type of view is not currently supported in Studio."
msgstr ""
"La creació d'aquest tipus de vista no es pot duu a terme actualment a "
"Studio."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Currency"
msgstr "Divisa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Current model:"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
#, python-format
msgid "Custom Configuration"
msgstr "Configuració personalitzada"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "Camps personalitzats"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr " Models personalitzats"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "Informes personalitzats"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Custom Sorting"
msgstr "Ordenació personalitzada"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "Vistes personalitzades"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Custom field names cannot contain double underscores."
msgstr "Els noms de camp personalitzats no poden contenir doble subratllat."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "Camps personalitzats:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "Models personalitzats:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "Informes personalitzats:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "Vistes personalitzades:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "Les personalitzacions fetes amb Studio es perdran permanentment"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#, python-format
msgid "Customizations"
msgstr "Personalitzacions"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
#, python-format
msgid "DIFF"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Date & Calendar"
msgstr "Data & calendari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Date range & Gantt"
msgstr "Rang de dates i Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Datetime"
msgstr "Data i hora"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#, python-format
msgid "Day"
msgstr "Dia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Day Precision"
msgstr "Precisió del dia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Decimal"
msgstr "Decimal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#, python-format
msgid "Default Display Mode"
msgstr "Mode de visualització per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Default Group By"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Default Group by"
msgstr "Agrupació per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Default Scale"
msgstr "Escala predeterminada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
#, python-format
msgid "Default Values"
msgstr "Valors per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Default value"
msgstr "Valor per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Default view"
msgstr "Vista per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr ""
"Defineixi les dates d'inici i final i visualitzi els registres en un "
"diagrama de Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#, python-format
msgid "Delay Field"
msgstr "Camp de retard"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Descending"
msgstr "Descendent"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
#, python-format
msgid "Description"
msgstr "Descripció"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
#, python-format
msgid "Design your Icon"
msgstr "Dissenya la teva icona"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Disable View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Discard changes"
msgstr "Descartar canvis"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Display Total row"
msgstr "Mostra la fila total"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Display Unavailability"
msgstr "Indisponibilitat de la pantalla"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.js:0
#, python-format
msgid "Do you want to add a dropdown with colors?"
msgstr "Vols afegir un desplegable amb colors?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#, python-format
msgid "Domain"
msgstr "Domini "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Done"
msgstr "Fet"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Arrossegui un menú a la dreta per crear un submenú"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Dropdown Menu"
msgstr "Menú desplegable"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "Duplicar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Dynamic Table"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Edit"
msgstr "Modificar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
#, python-format
msgid "Edit %s view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.js:0
#, python-format
msgid "Edit Application Icon"
msgstr "Edita la icona de l'aplicació "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.js:0
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "Edit Menu"
msgstr "Modificar menú"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Edit Values"
msgstr "Editar valors"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "Edit selection"
msgstr "Editar selecció"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Edit sources"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Editar un arxiu integrat a través de l'editor no està aconsellat, ja que "
"evitarà que sigui actualitzat durant futures actualitzacions d'Apps."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Editing item:"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Email"
msgstr "Correu electrònic"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
#, python-format
msgid "Email Alias"
msgstr "Àlies de correu electrònic"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "Plantilles de correu electrònic"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "Fil de correus"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Empty List Message"
msgstr "Llista de missatges buida"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Enable Mass Editing"
msgstr "Habilitar edició massiva"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Enable Routing"
msgstr "Activa l'encaminament"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "End Date"
msgstr "Data de finalització"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "Entrades"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "Error message:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "Error name:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#, python-format
msgid "Exclusive Approval"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive approval"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
#, python-format
msgid "Existing Fields"
msgstr "Camps existents"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
#, python-format
msgid "Existing Model"
msgstr "Model existent"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#, python-format
msgid "Export"
msgstr "Exportar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "External"
msgstr "Extern"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Fadeout Speed"
msgstr "Velocitat d'esvaïment "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
#, python-format
msgid "Fast"
msgstr "Ràpid"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#, python-format
msgid "Favourites"
msgstr "Favorits "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#, python-format
msgid "Field"
msgstr "Camp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.js:0
#, python-format
msgid "Field Properties"
msgstr "Propietats del camp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
#, python-format
msgid "Field properties: %s"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "Camps"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
#, python-format
msgid "File"
msgstr "Fitxer"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "Filename for %s"
msgstr "Nom del fitxer per %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
#, python-format
msgid "Filter"
msgstr "Filtre"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Filter Rules"
msgstr "Regles de filtre"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#, python-format
msgid "Filter label"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
#, python-format
msgid "Filters"
msgstr "Filtres"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#, python-format
msgid "First Status"
msgstr "Primer estat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "First dimension"
msgstr "Primera dimensió"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Form"
msgstr "Formulari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#, python-format
msgid "Format"
msgstr "Formata"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Forward"
msgstr "Endavant"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Gantt"
msgstr "Gràfic de Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "General views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
#, python-format
msgid "Generate %s View"
msgstr "Generar %s Vistes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Get contact, phone and email fields on records"
msgstr "Obtenir camps de contacte, telèfon i correu electrònic als registres"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Go back"
msgstr "Tornar enrere"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
#, python-format
msgid "Grant Approval"
msgstr "Concedir aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Graph"
msgstr "Diagrama"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Great !"
msgstr "Genial!"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__group_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Group"
msgstr "Grup"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
#, python-format
msgid "Group By"
msgstr "Agrupar per"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#, python-format
msgid "Group by"
msgstr "Agrupar per"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Grouping is not applied while in Studio to allow editing."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Groups used in approval rules must have an external identifier."
msgstr ""
"Els grups utilitzats en les regles d'aprovació han de tenir un identificador"
" extern."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#, python-format
msgid "Half Day"
msgstr "Mig dia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#, python-format
msgid "Half Hour"
msgstr "Mitja hora"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Help Tooltip"
msgstr "Ajuda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Hide Address"
msgstr "Oculta l'adreça"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Hide Name"
msgstr "Oculta el nom"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
#, python-format
msgid "Hide by default"
msgstr "Oculta per defecte"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "High Priority"
msgstr "Prioritat alta"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "Imatge de fons del menú d'inici"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#, python-format
msgid "Hour"
msgstr "Hora"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
msgid "ID"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "IDs"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#, python-format
msgid "Icon"
msgstr "Icona"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr ""
"Si aquesta opció està activada, la regla només aplicarà en els registres que"
" coincideixin amb el domini."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr ""
"Si aquesta opció està activada, l'usuari que aprovi aquesta regla no podrà "
"aprovar altres regles per al mateix registre"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr ""
"Si no desitja crear un nou model, ha de seleccionar un model existent."

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Image"
msgstr "Imatge"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#, python-format
msgid "Import"
msgstr "Importar"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "In Progress"
msgstr "En curs"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Include header and footer"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr ""
"Indica que aquest menú va ser creat per Studio per a mantenir la "
"configuració dels submenús"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Insert a field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Insert a field..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Insert a table based on a relational field."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Integer"
msgstr "Enter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "Internal"
msgstr "Intern"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#, python-format
msgid "Interval"
msgstr "Interval"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Invalid studio_approval %s in button"
msgstr "studio_approval %s invàlid en el botó"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
#, python-format
msgid "Invisible"
msgstr "Invisible"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "És Estudi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Is default view"
msgstr "És vista per defecte"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "It lacks a method to check."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Kanban State"
msgstr "Estat kanban"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Label"
msgstr "Etiqueta"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Label of the button"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
#, python-format
msgid "Leave DIFF"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
#, python-format
msgid "Limit visibility to groups"
msgstr "Limitar visibilitat a grups"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#, python-format
msgid "Line"
msgstr "Línia "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Lines"
msgstr "Línies"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "Activitat enllaçada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "List"
msgstr "Llista"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Manually sort records in the list view"
msgstr "Ordenar manualment els registres en la vista de llista"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Many2Many"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Many2One"
msgstr "Many2One"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Map"
msgstr "Mapa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Measure"
msgstr "Mida"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#, python-format
msgid "Measure Field"
msgstr "Camp de mesura"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
#, python-format
msgid "Measures"
msgstr "Mesures"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
#, python-format
msgid "Medium"
msgstr "Mitjà"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "Menú"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Message"
msgstr "Missatge"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "Mètode"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Method to run"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "Minimal header/footer"
msgstr "Capçalera/peu de pàgina mínim"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#, python-format
msgid "Mode"
msgstr "Mode"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
#, python-format
msgid "Model"
msgstr "Model"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "Accés de model"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "Model de dades"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
msgid "Model Name"
msgstr "Nom del model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#, python-format
msgid "Model name"
msgstr "Model"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "Models"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#, python-format
msgid "Modified by:"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "Mòdul"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Desinstal· la el mòdul"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Monetary"
msgstr "Monetari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Monetary value"
msgstr "Valor monetari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Month"
msgstr "Mes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Month Precision"
msgstr "Precisió del mes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#, python-format
msgid "More"
msgstr "Més"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Multine Text"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "Multiple records views"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "My %s"
msgstr "El meu %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#, python-format
msgid "My Button"
msgstr "El meu botó"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "Les meves entrades"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "N/A"
msgstr "N/A"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
#, python-format
msgid "Name"
msgstr "Nom"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "New"
msgstr "Nou"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#, python-format
msgid "New %s"
msgstr "Nou %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#, python-format
msgid "New App"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "New Approval Entry"
msgstr "Nova entrada d'aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#, python-format
msgid "New Field"
msgstr "Camp nou"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
#, python-format
msgid "New Fields"
msgstr "Camps nous"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#, python-format
msgid "New Filter"
msgstr "Nou Filtre"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "New Lines"
msgstr "Noves Línies"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "New Menu"
msgstr "Menú nou"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
#, python-format
msgid "New Model"
msgstr "Nou Model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#, python-format
msgid "New Page"
msgstr "Pàgina Nova"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
#, python-format
msgid "New button"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
#, python-format
msgid "Next"
msgstr "Següent"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "No aggregation"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "No approval found for this rule, record and user combination."
msgstr ""
"No s'ha trobat cap aprovació per a aquesta combinació de regla, registre i "
"usuari."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
#, python-format
msgid "No header/footer"
msgstr "Sense capçalera/peu de pàgina"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#, python-format
msgid "No related many2one fields found"
msgstr "No es van trobar camps many2one relacionats"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
#, python-format
msgid "None"
msgstr "Cap"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Notes"
msgstr "Notes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#, python-format
msgid "Notification Order"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "Nombre d'entrades"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Odoo Studio"
msgstr "Odoo Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "On view (ir.ui.view):"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "One2Many"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "Only %s members can approve this rule."
msgstr "Només els membres %s poden aprovar aquesta regla."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr ""
"Només grups amb ID externa es poden fer servir aquí. Si us plau escull un "
"altre grup o assigna manualment un ID externa a aquest grup."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Open form view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Optional"
msgstr "Opcional"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Order"
msgstr "Ordre"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "Fitxer PDF"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "Configuració de format de paper"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Paper format"
msgstr "Format de paper"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
#, python-format
msgid "Parent Menu"
msgstr "Menú principal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "Parent View (inherit_id)"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Partner"
msgstr "Empresa"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Phone"
msgstr "Telèfon"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Picture"
msgstr "Imatge"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#, python-format
msgid "Pie"
msgstr "Pastís"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Pipeline stages"
msgstr "Etapes del flux"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#, python-format
msgid "Pipeline status bar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Pivot"
msgstr "Pivot"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "Placeholder"
msgstr "Indicador de posició"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "Please specify a field."
msgstr "Si us plau específica un camp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#, python-format
msgid "Preview is not available"
msgstr "La previsualització no està disponible"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Print preview"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Priority"
msgstr "Prioritat"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr ""
"No es poden restringir els mètodes privats (seria inútil, ja que no es poden"
" activar de manera remota)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#, python-format
msgid "Properties"
msgstr "Propietats"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#, python-format
msgid "Quarter Hour"
msgstr "Quart d'hora"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#, python-format
msgid "Quick Create"
msgstr "Creació ràpida"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#, python-format
msgid "Quick Create Field"
msgstr "Crea un camp ràpid"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Rainbow Effect"
msgstr "Efecte d'arc de Sant Martí"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Rainbow Man"
msgstr "Home de l'arc de Sant Martí"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
#, python-format
msgid "Readonly"
msgstr "Només lectura"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Ready"
msgstr "Preparat"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "Registre "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "ID Registre "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
#, python-format
msgid "Redo"
msgstr "Fer de nou"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "Referència"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Reject"
msgstr "Rebutjat"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "Rebutjada"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected as"
msgstr "Rebutjat com"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Rejected on"
msgstr "Rebutjat el"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Related Field"
msgstr "Camp relacionat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Reload from attachment"
msgstr "Carregar de nou des de fitxer adjunt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#, python-format
msgid "Remove from View"
msgstr "Eliminar de la vista"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "Remove selection"
msgstr "Eliminar selecció"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "Acció d'informe"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Report Tools"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
#, python-format
msgid "Report edition failed"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Report name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid "Report preview not available"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "Reporting views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Reports"
msgstr "Informes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
#, python-format
msgid "Required"
msgstr "Requerit"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#, python-format
msgid "Reset Default Background"
msgstr "Resetejar fons per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#, python-format
msgid "Reset Image"
msgstr "Reiniciar imatge"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "Reinicia la vista"

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "Assistent per a restabliment de la vista d'arquitectura"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Reset report"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__responsible_id
#, python-format
msgid "Responsible"
msgstr "Responsable"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#, python-format
msgid "Restore Default View"
msgstr "Restaurar vista per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Restrict a record to a specific company"
msgstr "Restringir un registre a una empresa especifica"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Retention"
msgstr "Retenció"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "Revoke"
msgstr "Revocar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
#, python-format
msgid "Row grouping - First level"
msgstr "Agrupar files - primer nivell"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
#, python-format
msgid "Row grouping - Second level"
msgstr "Agrupar files - segon nivell"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
msgid "Rule"
msgstr "Regla"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr ""
"No es poden eliminar les regles amb entrades existents, ja que s'esborrarien"
" les entrades d'aprovació existents. El millor és arxivar la regla."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr ""
"No es poden modificar les regles amb entrades existents, ja que es danyarien"
" les entrades d'aprovació existents. El millor és arxivar la regla i crear "
"una nova."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "Run a Server Action"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#, python-format
msgid "Save"
msgstr "Desar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
#, python-format
msgid "Saved"
msgstr "Desat "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
#, python-format
msgid "Saving"
msgstr "Guardar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
#, python-format
msgid "Saving both some report's parts and full xml is not permitted."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "Saving the report \""
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "Search"
msgstr "Cercar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
#, python-format
msgid "Search..."
msgstr "Cercar..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#, python-format
msgid "Second Status"
msgstr "Segon estat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Second dimension"
msgstr "Segona dimensió"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#, python-format
msgid "Select a related field"
msgstr "Selecciona un camp relacionat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
#, python-format
msgid "Select a related field."
msgstr "Selecciona un camp relacionat."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#, python-format
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr ""
"Seleccioneu el camp de contacte que voleu utilitzar per obtenir les "
"coordenades dels vostres registres."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#, python-format
msgid "Select the model in relation to this one"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#, python-format
msgid "Select the reciprocal ManyToOne field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Selection"
msgstr "Selecció"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
#, python-format
msgid "Send a"
msgstr "Envia un"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Send messages, log notes and schedule activities"
msgstr "Enviï missatges, registri notes i programi activitats"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#, python-format
msgid "Separator"
msgstr "Separador"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Sequence"
msgstr "Seqüència"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "Acció del Servidor "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#, python-format
msgid "Set As Default"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_cover_properties/kanban_cover_properties.xml:0
#, python-format
msgid "Set Cover Image"
msgstr ""
"Ajustar la imatge\n"
"de la coberta"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Set a price or cost on records"
msgstr "Estableix un preu o cost en els registres"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#, python-format
msgid "Set approval rules"
msgstr "Estableix les regles d'aprovació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#, python-format
msgid "Show Invisible Elements"
msgstr "Mostra els elements invisibles"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "Show Traceback"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
#, python-format
msgid "Show by default"
msgstr "Mostra per defecte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "Show in print menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Signature"
msgstr "Signatura"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
#, python-format
msgid "Slow"
msgstr "Lent"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "Sort By"
msgstr "Ordena per"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
#, python-format
msgid "Sort by"
msgstr "Ordenar per"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Sorting"
msgstr "Ordenació"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Specify all possible values"
msgstr "Especifiqueu tots els valors possibles"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Stacked graph"
msgstr "Gràfic apilat"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Stage"
msgstr "Etapa"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#, python-format
msgid "Stage Name"
msgstr "Nom de la fase"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Stage and visualize records in a custom pipeline"
msgstr "Organitzi i visualitzi registres en un flux personalitzat"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Start Date"
msgstr "Data inicial"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Start Date Field"
msgstr "Iniciar camp de data"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Stop Date Field"
msgstr "Aturar camp de data"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
msgid "Studio"
msgstr "Studio"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "Creador d'aplicacions de Studio"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "Entrades d'aprovació de Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "Entrada d'aprovació de Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "Sol·licitud d'aprovació de Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "Regla d'aprovació de Studio"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "Menú de configuració de Studio"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "Personalitzacions de Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "Studio Mixin"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
#, python-format
msgid "Suggested features for your new model"
msgstr "Funcions suggerides per al seu nou model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#, python-format
msgid "Sum"
msgstr "Suma"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
#, python-format
msgid "Sum of %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
#, python-format
msgid "Tabs"
msgstr "Pestanyes"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Tags"
msgstr "Etiquetes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "Technical Name"
msgstr "Nom tècnic"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Template '%s' not found"
msgstr "Plantilla'%s' no trobada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#, python-format
msgid "Text"
msgstr "Text"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "The fastest way to create a web application."
msgstr "La manera més ràpida de crear una aplicació web. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The field %s does not exist."
msgstr "El camp%s no existeix"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
#, python-format
msgid "The following approvals are missing:"
msgstr "Falten les següents aprovacions:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
#, python-format
msgid "The following fields are currently not in the view."
msgstr "Els següents camps actualment no estan en la vista."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The icon has not a correct format"
msgstr "La icona no té un format correcte"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The icon is not linked to an attachment"
msgstr "La icona no està enllaçada a un fitxer adjunt"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The method %s does not exist on the model %s."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
#, python-format
msgid "The method %s is private."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The model %s does not exist."
msgstr "El model %s no existeix "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The model %s doesn't exist."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The model %s doesn't support adding fields."
msgstr "El model %s no suporta agregar camps."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The operation  type \"%s\" is not supported"
msgstr "El tipus d'operació \"%s\" no és compatible"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "The related field of a button has to be a many2one to %s."
msgstr "El camp relacionat a un botó ha de ser de molts a un %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
#, python-format
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#, python-format
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr ""
"El canvi requerit ha provat un error en la vista. Pot ser a causa de que el "
"camp s'ha eliminat, però encara s'utilitza en algun lloc."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"Hi ha %s registres utilitzant valors de selecció que no figuren en els que està intentant guardar.\n"
"Està segur que desitja eliminar els valors de selecció d'aquests registres?"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "There is no method %s on the model %s (%s)"
msgstr "No hi ha cap mètode %s al model %s (%s)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#, python-format
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#, python-format
msgid "There is no record to preview"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when an approval "
"is requested"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#, python-format
msgid "Third Status"
msgstr "Tercer estat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#, python-format
msgid "This action is not editable by Studio"
msgstr "L'acció no és editable per Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"Aquesta aprovació o la que ja vas enviar et limita a una sola aprovació per aquesta acció.\n"
"Es requereix un altre usuari per aprovar aquesta acció."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"This message will be displayed to users that cannot proceed without this "
"approval"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#, python-format
msgid "This operation caused an error, probably because a xpath was broken"
msgstr ""
"Aquesta operació ha causat un erro, possiblement perquè s'ha trencat una "
"drecera. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "This rule has already been approved/rejected."
msgstr "Aquesta norma ja ha estat aprovada/rebutjada."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#, python-format
msgid "This rule limits this user to a single approval for this action."
msgstr ""
"Aquesta regla limita aquest usuari a una sola aprovació per a aquesta acció."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#, python-format
msgid "Timeline"
msgstr "Línia de temps"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "Timeline views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
#, python-format
msgid "Toggle Studio"
msgstr "Commutar vista"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
#, python-format
msgid "Type"
msgstr "Tipus"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "Type down your notes here..."
msgstr "Escrigui les seves notes aquí..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
#, python-format
msgid "Undo"
msgstr "Desfer"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid ""
"Use this field to setup multi-level validation. Next activities and "
"notifications for an approval request will only be sent once rules from "
"previous levels have been validated"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "User"
msgstr "Usuari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "User assignment"
msgstr "Assignació de l'usuari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
#, python-format
msgid "User avatar placeholder"
msgstr "Marcador de posició de l'avatar de l'usuari"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "User is not a member of the selected group."
msgstr "L'usuari no és membre del grup seleccionat."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
#, python-format
msgid "Users to notify"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
#, python-format
msgid "Uses:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "Value"
msgstr "Valor"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#, python-format
msgid "Value of list"
msgstr "Valor de la llista"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
#, python-format
msgid "View"
msgstr "Vista"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#, python-format
msgid "View in Error:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Views"
msgstr "Vistes"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
#, python-format
msgid "Waiting for approval"
msgstr "S'està esperant l'aprovació"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#, python-format
msgid "Webhook Automations"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#, python-format
msgid "Webhooks"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Week"
msgstr "Setmana"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#, python-format
msgid "Week Precision"
msgstr "Precisió de la setmana"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "Welcome to"
msgstr "Benvingut a"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#, python-format
msgid "What should the button do"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
#, python-format
msgid "When Creating Record"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "Si la regla pot ser aprovada per l'usuari actual"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "Si aquest model és abstracte"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/type_widget_properties.xml:0
#, python-format
msgid "Widget"
msgstr "Widget"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#, python-format
msgid "Write additional notes or comments"
msgstr "Escrigui notes o comentaris addicionals"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#, python-format
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#, python-format
msgid "Year"
msgstr "Any"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "You cannot cancel an approval you didn't set yourself."
msgstr ""
"No podeu cancel·lar una regla que no va ser establerta per vostè mateix"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
#, python-format
msgid "You cannot deactivate this view as it is the last one active."
msgstr "No podeu desactivar aquesta vista, ja que és l'última activa."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.js:0
#, python-format
msgid "You first need to create a many2many field in the form view."
msgstr ""
"Primer necessites crear un camp de molts a molts en la vista de formulari"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#, python-format
msgid "You just like to break things, don't you?"
msgstr "Li encanta destruir les coses, cert?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
#, python-format
msgid "domain not defined"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
#, python-format
msgid "dropdown"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#, python-format
msgid "e.g. Properties"
msgstr "p.e. Propietats"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#, python-format
msgid "e.g. Real Estate"
msgstr "p.e Immoble "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
#, python-format
msgid "or"
msgstr "o"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#, python-format
msgid "studio_approval attribute can only be set in form views"
msgstr ""
"L'atribut studio_approval només es pot establir en vistes de formulari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
#, python-format
msgid "tabsDisplay.new.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
#, python-format
msgid "tabsDisplay.properties.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
#, python-format
msgid "tabsDisplay.view.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
#, python-format
msgid "test email"
msgstr "provar correu electrònic"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
#, python-format
msgid "upload it"
msgstr "puja-ho"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
#, python-format
msgid "upload one"
msgstr "puja'n un "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#, python-format
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#, python-format
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr ""
