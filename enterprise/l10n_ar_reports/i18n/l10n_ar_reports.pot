# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-18 19:25+0000\n"
"PO-Revision-Date: 2023-12-18 19:25+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__afip_responsibility_type_id
msgid "AFIP Responsibility Type"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "AFIP responsibility"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__afip_responsibility_type_name
msgid "Afip Responsibility Type Name"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "All"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,help:l10n_ar_reports.field_account_ar_vat_line__other_taxes
msgid ""
"All the taxes tat ar not VAT taxes or iibb perceptions and that are realted "
"to documents that have VAT"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report,name:l10n_ar_reports.l10n_ar_vat_book_report
msgid "Argentinean VAT book"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model,name:l10n_ar_reports.model_l10n_ar_tax_report_handler
msgid "Argentinian Report Custom Handler"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid ""
"Can only generate TXT files using posted entries. Please remove Include "
"unposted entries filter and try again"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_city_tax
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__city_tax
msgid "City Tax"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model,name:l10n_ar_reports.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__company_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Company"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_res_company__l10n_ar_computable_tax_credit
#: model:ir.model.fields,field_description:l10n_ar_reports.field_res_config_settings__l10n_ar_computable_tax_credit
#: model:ir.model.fields,field_description:l10n_ar_reports.field_res_config_test__l10n_ar_computable_tax_credit
msgid "Computable Tax Credit: Prorate Options"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model,name:l10n_ar_reports.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Country"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__cuit
msgid "Cuit"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__company_currency_id
msgid "Currency"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__out_refund
msgid "Customer Credit Note"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__out_invoice
msgid "Customer Invoice"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_invoice_date
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__date
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Date"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Date: Last month"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__document_type_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Document Type"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Edit Country"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Edit Partner"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__move_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Entry"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__res_company__l10n_ar_computable_tax_credit__global
msgid "Global"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__base_10
msgid "Grav. 10,5%"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__base_25
msgid "Grav. 2,5%"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__base_21
msgid "Grav. 21%"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__base_27
msgid "Grav. 27%"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__base_5
msgid "Grav. 5%"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Group By..."
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__id
msgid "ID"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__invoice_date
msgid "Invoice Date"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__journal_id
msgid "Journal"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__entry
msgid "Journal Entry"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Legal Entity VAT"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__move_name
msgid "Move Name"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__move_type
msgid "Move Type"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_partner_name
msgid "Name"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Natural Person VAT"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_not_taxed
msgid "Not Taxed"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,help:l10n_ar_reports.field_account_ar_vat_line__not_taxed
msgid ""
"Not Taxed / Exempt.\\All lines that have VAT 0, Exempt, Not Taxed or Not "
"Applicable"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__not_taxed
msgid "Not taxed/ex"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Only one tax type should be selected."
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_tree
msgid "Open"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_tree
msgid "Open journal entry"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_other_taxes
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__other_taxes
msgid "Other Taxes"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__partner_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
#, python-format
msgid "Partner"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__partner_name
msgid "Partner Name"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_perc_earnings
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__perc_earnings
msgid "Perc. Earnings"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_perc_iibb
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__perc_iibb
msgid "Perc. IIBB"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__state__posted
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Posted"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Posted Journal Items"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__in_receipt
msgid "Purchase Receipt"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Purchase VAT"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Purchases"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "Sales"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__out_receipt
msgid "Sales Receipt"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Sales VAT"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__state
msgid "Status"
msgstr ""

#. module: l10n_ar_reports
#. odoo-javascript
#: code:addons/l10n_ar_reports/static/src/components/argentinian_report/filter_tax_type.xml:0
#, python-format
msgid "Tax Type:"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_taxed
msgid "Taxed"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "The country '%s' does not have a '%s' configured."
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "The partner '%s' does not have a country configured."
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_total
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__total
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_tree
#, python-format
msgid "Total"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "Type"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__state__draft
msgid "Unposted"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.res_config_settings_view_form
msgid "Used when generating the VAT Book Voucher TXT files."
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_cuit
msgid "VAT"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_10
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_10
msgid "VAT 10,5%"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_25
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_25
msgid "VAT 2,5%"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_21
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_21
msgid "VAT 21%"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_27
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_27
msgid "VAT 27%"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_5
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_5
msgid "VAT 5%"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "VAT Book (ZIP)"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_vat_per
msgid "VAT Perc"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,field_description:l10n_ar_reports.field_account_ar_vat_line__vat_per
msgid "VAT Perc."
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields,help:l10n_ar_reports.field_account_ar_vat_line__vat_per
msgid "VAT Perception"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.actions.act_window,name:l10n_ar_reports.action_account_ar_vat_line
#: model:ir.ui.menu,name:l10n_ar_reports.menu_current_account
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_form
msgid "VAT Summary"
msgstr ""

#. module: l10n_ar_reports
#: model:account.report.column,name:l10n_ar_reports.l10n_ar_vat_book_report_column_afip_responsibility_type_name
msgid "VAT cond."
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model,name:l10n_ar_reports.model_account_ar_vat_line
msgid "VAT line for Analysis in Argentinean Localization"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__in_invoice
msgid "Vendor Bill"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__account_ar_vat_line__move_type__in_refund
msgid "Vendor Credit Note"
msgstr ""

#. module: l10n_ar_reports
#: model_terms:ir.ui.view,arch_db:l10n_ar_reports.view_account_ar_vat_line_search
msgid "With Document Type"
msgstr ""

#. module: l10n_ar_reports
#: model:ir.model.fields.selection,name:l10n_ar_reports.selection__res_company__l10n_ar_computable_tax_credit__wo_prorate
msgid "Without Prorate"
msgstr ""

#. module: l10n_ar_reports
#. odoo-python
#: code:addons/l10n_ar_reports/models/l10n_ar_vat_book.py:0
#, python-format
msgid "ZIP"
msgstr ""
