# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_holidays
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
#, python-format
msgid "%(names)s are on time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
#, python-format
msgid "%(names)s is on time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
#, python-format
msgid "%(names)s requested time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#: model_terms:ir.ui.view,arch_db:project_holidays.view_task_form2_inherit_holidays
msgid "<i class=\"fa fa-user-times me-2\" role=\"img\" title=\"Leave warning\"/>"
msgstr ""

#. module: project_holidays
#: model:ir.model.fields,field_description:project_holidays.field_project_task__is_absent
msgid "Employees on Time Off"
msgstr "İzinli Çalışanlar"

#. module: project_holidays
#: model:ir.model.fields,field_description:project_holidays.field_project_task__leave_warning
msgid "Leave Warning"
msgstr "Uyarı bırakın"

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operasyon desteklenmiyor"

#. module: project_holidays
#: model:ir.model,name:project_holidays.model_project_task
msgid "Task"
msgstr "Görev"
