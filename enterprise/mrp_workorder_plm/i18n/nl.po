# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_plm
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
#, python-format
msgid "BoM feedback for not found step: %s (%s)"
msgstr "Stuklijst-feedback voor niet gevonden stap: %s (%s)"

#. module: mrp_workorder_plm
#: model:ir.model.fields,help:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Defines the type of the quality control point."
msgstr "Definieert het soort van kwaliteitscontrole punt."

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "ECO route wijzigingen"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr "Engineering Change Order (ECO)"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
#, python-format
msgid "Instruction Suggestions (%(wo_name)s)"
msgstr "Instructievoorstellen (%(wo_name)s)"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
#, python-format
msgid "New Step Suggestion: %s"
msgstr "Voorstel nieuwe stap: %s"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_production
msgid "Production Order"
msgstr "Productieorder"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_propose_change
msgid "Propose a change in the production"
msgstr "Wijziging in de productie voorstellen"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_check
msgid "Quality Check"
msgstr "Kwaliteitscontrole"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__quality_point_id
msgid "Quality Point"
msgstr "Kwaliteitscontrolepunt"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__step
msgid "Step"
msgstr "Stap"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Step Type"
msgstr "Staptype"
