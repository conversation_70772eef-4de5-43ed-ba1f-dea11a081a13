# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: planning
#: model:ir.actions.report,print_report_name:planning.report_planning_slot
msgid "'Planning'"
msgstr "「規劃」"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "(%s days span)"
msgstr "（%s 天期間）"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid ".&amp;nbsp;"
msgstr "。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 5:00 PM"
msgstr "01/01/2023 5:00 PM"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 9:00 AM"
msgstr "01/01/2023 9:00 AM"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "04:00"
msgstr "04:00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "08:00"
msgstr "08:00"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "100"
msgstr "100"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "12:00"
msgstr "12:00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "16:00"
msgstr "16:00"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "8 hours"
msgstr "8 小時"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<b class=\"o_gantt_header o_gantt_row_sidebar position-sticky top-0 bg-view "
"d-flex align-items-center justify-content-center\">Schedule</b>"
msgstr ""
"<b class=\"o_gantt_header o_gantt_row_sidebar position-sticky top-0 bg-view "
"d-flex align-items-center justify-content-center\">排期</b>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<b class=\"o_gantt_header_scale d-flex align-items-center justify-content-"
"center\">12 February 2023 - 18 February 2023</b>"
msgstr ""
"<b class=\"o_gantt_header_scale d-flex align-items-center justify-content-"
"center\">2023 年 2 月 12 日至 2023 年 2 月 18 日</b>"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr "<b class=\"tip_title\">提示:更快地記錄你的計劃</b>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "<b>Congratulations!</b></br> You are now a master of planning."
msgstr "<b>做得好！</b></br>你現在已是工作規劃專家！"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"<b>Drag & drop</b> your shift to reschedule it. <i>Tip: hit CTRL (or Cmd) to"
" duplicate it instead.</i> <b>Adjust the size</b> of the shift to modify its"
" period."
msgstr ""
"<b>拖放</b>班次至其他位置，即可改期。提示：按住 Ctrl 鍵（或 Cmd "
"鍵）可複製班次。<i></i><b>調整班次的大小</b>，可修改班次的持續時間。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "<b>Publish & send</b> your employee's planning."
msgstr "<b>發佈及傳送</b>你的員工規劃。"

#. module: planning
#: model:mail.template,body_html:planning.email_template_shift_switch_email
msgid ""
"<div>\n"
"                    <p>\n"
"                        Dear <t t-out=\"ctx.get('old_assignee_name', 'Mitchel Admin')\"></t>,\n"
"                        <br><br>\n"
"                        Your following shift was re-assigned to <t t-out=\"ctx.get('new_assignee_name', 'Marc Demo')\"></t>.\n"
"                    </p>\n"
"                    <br>\n"
"                    <table style=\"width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">From</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('start_datetime') or ''\">05/31/2021 08:00:00</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">To</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('end_datetime') or ''\">05/31/2021 16:00:00</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sale Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\"></td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p>\n"
"                        親愛的 <t t-out=\"ctx.get('old_assignee_name', 'Mitchel Admin')\"></t>：\n"
"                        <br><br>\n"
"                        下列你的班次 / 更份已重新指派給 <t t-out=\"ctx.get('new_assignee_name', 'Marc Demo')\"></t>。\n"
"                    </p>\n"
"                    <br>\n"
"                    <table style=\"width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">開始時間</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('start_datetime') or ''\">05/31/2021 08:00:00</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">結束時間</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('end_datetime') or ''\">05/31/2021 16:00:00</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">職務</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">酒保</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">專案</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">聚會</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">銷售單項目</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\"></td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">備註</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </div>\n"
"            "

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<div>\n"
"                    <p>\n"
"                    <t t-if=\"ctx.get('employee')\">\n"
"                        Dear <t t-out=\"ctx['employee'].name or ''\"></t>,\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Hello,\n"
"                    </t>\n"
"                        <br><br>\n"
"                    <t t-if=\"ctx.get('assigned_new_shift')\">\n"
"                        You have been assigned new shifts:\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Please, find your planning for the following period:\n"
"                    </t>\n"
"                    </p>\n"
"                    <br>\n"
"\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">From</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"format_date(ctx.get('start_datetime')) or ''\">05/05/2021</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">To</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"format_date(ctx.get('end_datetime')) or ''\">05/05/2021</td>\n"
"                        </tr>\n"
"                    </table>\n"
"\n"
"                    <div t-if=\"ctx.get('planning_url')\" style=\"margin: 15px;\">\n"
"                        <a t-att-href=\"ctx.get('planning_url')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View Your Planning</a>\n"
"                    </div>\n"
"\n"
"                    <div t-if=\"ctx.get('slot_unassigned')\">\n"
"                        <p>If an open shift is available and at your convenience, please assign yourself to it</p>\n"
"                    </div>\n"
"                    <br>\n"
"\n"
"                    <t t-if=\"ctx.get('message')\">\n"
"                        <p t-out=\"ctx['message'] or ''\"></p>\n"
"                    </t>\n"
"\n"
"                    <p t-if=\"hasattr(object, 'allow_self_unassign') and object.allow_self_unassign and hasattr(object, 'self_unassign_days_before') and object.self_unassign_days_before\">\n"
"                        As a small reminder, you have <t t-out=\"object.self_unassign_days_before or ''\">5</t>day(s) before the beginning of a shift to unassign yourself if you are unavailable. Thank you for your understanding.\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p>\n"
"                    <t t-if=\"ctx.get('employee')\">\n"
"                        親愛的 <t t-out=\"ctx['employee'].name or ''\"></t>：\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        你好！\n"
"                    </t>\n"
"                        <br><br>\n"
"                    <t t-if=\"ctx.get('assigned_new_shift')\">\n"
"                        你已獲編配新的工作班次 / 更份：\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        公司已規劃你下列期間的工作，請檢視詳情：\n"
"                    </t>\n"
"                    </p>\n"
"                    <br>\n"
"\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">由</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"format_date(ctx.get('start_datetime')) or ''\">05/05/2021</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">至</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"format_date(ctx.get('end_datetime')) or ''\">05/05/2021</td>\n"
"                        </tr>\n"
"                    </table>\n"
"\n"
"                    <div t-if=\"ctx.get('planning_url')\" style=\"margin: 15px;\">\n"
"                        <a t-att-href=\"ctx.get('planning_url')\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">檢視工作規劃</a>\n"
"                    </div>\n"
"\n"
"                    <div t-if=\"ctx.get('slot_unassigned')\">\n"
"                        <p>若有開放的工作班次 / 更份未編配，而你方便上班，請自行將班次 / 更份編配給自己。</p>\n"
"                    </div>\n"
"                    <br>\n"
"\n"
"                    <t t-if=\"ctx.get('message')\">\n"
"                        <p t-out=\"ctx['message'] or ''\"></p>\n"
"                    </t>\n"
"\n"
"                    <p t-if=\"hasattr(object, 'allow_self_unassign') and object.allow_self_unassign and hasattr(object, 'self_unassign_days_before') and object.self_unassign_days_before\">\n"
"                        提醒你，若無法依照已編配的更份上班，請於更份開始前 <t t-out=\"object.self_unassign_days_before or ''\">5</t> 天，自行取消編配給你的更份。多謝合作。\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<div>\n"
"                    <p>Dear <t t-out=\"ctx.get('employee_name', '')\">Anita Oliver</t>,<br><br></p>\n"
"                    <t t-if=\"ctx.get('open_shift_available')\">\n"
"                        <p>A new open shift is available:</p>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <p>You have been assigned the following schedule:</p>\n"
"                    </t>\n"
"                    <br>\n"
"                    <table style=\"width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">From</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('start_datetime') or ''\">05/31/2021 08:00:00</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">To</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('end_datetime') or ''\">05/31/2021 16:00:00</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sale Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\"></td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p>親愛的 <t t-out=\"ctx.get('employee_name', '')\">Anita Oliver</t>：<br><br></p>\n"
"                    <t t-if=\"ctx.get('open_shift_available')\">\n"
"                        <p>現有新的未編配班次 / 更份：</p>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <p>你已獲安排下列期間工作：</p>\n"
"                    </t>\n"
"                    <br>\n"
"                    <table style=\"width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">開始時間</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('start_datetime') or ''\">05/31/2021 08:00:00</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left;\">結束時間</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"ctx.get('end_datetime') or ''\">05/31/2021 16:00:00</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">職務</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">酒保</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">專案</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">聚會</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">銷售單項目</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\"></td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px;text-align: left;\">備註</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </div>\n"
"            "

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/>\n"
"                        You have successfully requested to switch your shift. You will be notified when another employee takes over your shift."
msgstr ""
"<i class=\"fa fa-check-circle text-success\"/>\n"
"                        成功申請調班。有其他員工可接替你的輪班時，會通知你。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> Request to switch shift "
"cancelled successfully."
msgstr "<i class=\"fa fa-check-circle text-success\"/> 成功取消調班請求。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> This shift is no longer "
"assigned to you."
msgstr "<i class=\"fa fa-check-circle text-success\"/> 此輪班已不再分配給你。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> You were successfully "
"assigned this open shift."
msgstr "<i class=\"fa fa-check-circle text-success\"/> 你已被成功指派至此開放班次。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"小時\" title=\"小時\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"郵件\" role=\"img\" style=\"padding-"
"right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle text-warning\"/> This shift is already "
"assigned to another employee."
msgstr "<i class=\"fa fa-exclamation-circle text-warning\"/> 此輪班已分配給另一名員工。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle text-warning\"/> You can no longer "
"unassign yourself from this shift."
msgstr "<i class=\"fa fa-exclamation-circle text-warning\"/> 你已不可取消分配此輪班給你自己。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"箭嘴圖示\" title=\"箭嘴\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" aria-label=\"箭嘴圖示\" title=\"箭嘴\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"箭嘴\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"
msgstr "<i class=\"fa fa-phone\" title=\"電話\" role=\"img\" style=\"padding-right: 2px\"/>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "<i class=\"fa small fa-minus me-1\"></i>Bartender"
msgstr "<i class=\"fa small fa-minus me-1\"></i> 酒保"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "<i class=\"fa small fa-minus me-1\"></i>Waiter"
msgstr "<i class=\"fa small fa-minus me-1\"></i> 侍應"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"me-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"me-1 fa fa-clock-o\" role=\"img\" aria-label=\"日期\" title=\"日期\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "<span aria-label=\"Close\">×</span>"
msgstr "<span aria-label=\"關閉\">×</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<span class=\"align-middle\" invisible=\"overlap_slot_count == 0\">Prepare "
"for the ultimate multi-tasking challenge:</span>"
msgstr ""
"<span class=\"align-middle\" invisible=\"overlap_slot_count == "
"0\">做好準備，接受終極多工任務挑戰：</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<span class=\"align-middle\" invisible=\"overlap_slot_count == "
"0\">conflict.</span>"
msgstr ""
"<span class=\"align-middle\" invisible=\"overlap_slot_count == "
"0\">項衝突。</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"ms-0 me-4\">Edit</span>"
msgstr "<span class=\"ms-0 me-4\">編輯</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "<span class=\"ms-2 flex-shrink-1\"> Working hours</span>"
msgstr "<span class=\"ms-2 flex-shrink-1\"> 工時</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">04:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">08:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">08:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-index-1\">12:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 PM (4h)"
" - Jeffrey Kelly</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">下午 1:00 - 5:00（4 小時） -"
" Jeffrey Kelly</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 PM "
"(4h)</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">下午 1:00 - 下午 5:00（4 "
"小時）</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 PM "
"(4h) - Doris Cole</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">上午 8:00 - 下午 12:00（4 "
"小時） - Doris Cole</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 PM "
"(4h) - Eli Lambert</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">上午 8:00 - 下午 12:00（4 "
"小時） - Eli Lambert</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 PM "
"(4h)</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">上午 8:00 - 下午 12:00（4 "
"小時）</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"
msgstr ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"箭嘴圖示\" title=\"箭嘴\"/>\n"
"                                    <span class=\"o_end_date\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
msgid "<span class=\"o_stat_text\">Planning</span>"
msgstr "<span class=\"o_stat_text\">規劃</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "<span>Doris Cole <span class=\"text-muted\">(Bartender)</span></span>"
msgstr "<span>Doris Cole <span class=\"text-muted\">（酒保）</span></span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "<span>Eli Lambert <span class=\"text-muted\">(Waiter)</span></span>"
msgstr "<span>Eli Lambert <span class=\"text-muted\">（侍應）</span></span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "<span>Jeffrey Kelly <span class=\"text-muted\">(Bartender)</span></span>"
msgstr "<span>Jeffrey Kelly <span class=\"text-muted\">（酒保）</span></span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid ""
"<span>The employee assigned would like to switch shifts with someone "
"else.</span>"
msgstr "<span>已獲指派的員工想找其他人調班。</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months ahead</span>"
msgstr "<span>個月前</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>已分配時間 — </strong>"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr "重複發生至某個特定日期的事件，必須設定該限制"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__active
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__active
msgid "Active"
msgstr "啟用"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.js:0
#, python-format
msgid "Add Shift"
msgstr "新增班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Additional message"
msgstr "附加訊息"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr "發送給員工電郵中顯示的附加訊息"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional note sent to the employee"
msgstr "發送給員工的附加說明"

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "管理員"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
#, python-format
msgid ""
"All open shifts have already been assigned, or there are no resources "
"available to take them at this time."
msgstr "所有開放班次均已指派，或目前沒有可用資源可以處理它們。"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__all
msgid "All shifts"
msgstr "所有班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr "所有之後的輪班都會刪除。確定要繼續？"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Hours"
msgstr "已分配時數"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Percentage"
msgstr "已分配百分比"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Allocated Time"
msgstr "獲分配時間"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time %"
msgstr "已分配時間 %"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_percentage
msgid "Allocated Time (%)"
msgstr "已分配時間（%）"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "Allocated hours and allocated time percentage cannot be negative."
msgstr "分配的工時及分配的時間百分比，不可是負數。"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr "分配類別"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_template_creation
msgid "Allow Template Creation"
msgstr "允許建立範本"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_allow_self_unassign
msgid "Allow Unassignment"
msgstr "允許取消指派"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "An shift must be in the same company as its recurrency."
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid ""
"Analyze the allocation of your resources across roles, projects, and sales "
"orders, and estimate future needs."
msgstr "分析不同職能角色、專案項目及銷售訂單之間的資源分配，並估算未來需求。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Archived"
msgstr "已封存"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/resource_form/resource_form_controller.js:0
#: code:addons/planning/static/src/views/resource_list/resource_list_controller.js:0
#, python-format
msgid ""
"Archiving this resource will transform all of its future shifts into open "
"shifts. Are you sure you want to continue?"
msgstr "封存此資源會將其所有未來班次轉變為開放班次。確定要繼續？"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#, python-format
msgid "Are you sure you want to delete this shift?"
msgstr "確定要刪除此輪班？"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid ""
"As there is no running contract during this period, this resource is not "
"expected to work a shift. Planned hours:"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Ask To Switch"
msgstr "要求調換"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Ask to Switch"
msgstr "要求調換"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Assign a <b>resource</b>, or leave it open for the moment. <i>Tip: Create "
"open shifts for the roles you will be needing to complete a mission. Then, "
"assign those open shifts to the resources that are available.</i>"
msgstr "分配<b>資源</b>，或暫時保持開放狀態。<i></i>提示：為完成任務所需的角色，建立開放輪班。然後，將這些開放班次分配給可用的資源。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Assignee"
msgstr "被指派人"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#, python-format
msgid "Auto Plan"
msgstr "自動規劃"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#, python-format
msgid "Automatically plan open shifts and sales orders"
msgstr "自動規劃開放班次及銷售訂單"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_filter_panel/planning_calendar_filter_panel.xml:0
#, python-format
msgid "Avatar"
msgstr "頭像"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_resource_resource__avatar_128
msgid "Avatar 128"
msgstr "頭像 128"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Bartender"
msgstr "酒保"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee_base
msgid "Basic Employee"
msgstr "一般員工"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_resource
msgid "By Resource"
msgstr "按資源"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr "按角色"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "CANCEL SWITCH"
msgstr "取消調換"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_allow_self_unassign
msgid "Can Employee Un-Assign Themselves?"
msgstr "員工可以取消指派自己嗎？"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Cancel Switch"
msgstr "取消調換"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Chat"
msgstr "聊天"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
#: model:ir.model.fields,field_description:planning.field_resource_resource__color
msgid "Color"
msgstr "顏色"

#. module: planning
#: model:planning.role,name:planning.planning_role_cm
msgid "Community Manager"
msgstr "社群管理員"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "公司"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__company_id
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Company"
msgstr "公司"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__company_id
msgid ""
"Company linked to the material resource. Leave empty for the resource to be "
"available in every company."
msgstr "連結至物料資源的公司。留空可讓該資源在每間公司都可用。"

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "配置"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__confirm_delete
msgid "Confirm Slots Deletion"
msgstr "確認刪除時段"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__conflicting_slot_ids
msgid "Conflicting Slot"
msgstr "衝突時段"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#, python-format
msgid "Copy previous"
msgstr "複製上一個"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#, python-format
msgid "Copy previous week"
msgstr "複製上星期"

#. module: planning
#: model:planning.role,name:planning.planning_role_crane
msgid "Crane"
msgstr "起重機"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
msgid "Created on"
msgstr "建立於"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_end
msgid "Date End"
msgstr "結束日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_start
msgid "Date Start"
msgstr "開始日期"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__day
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__day
msgid "Days"
msgstr "天內"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Days before shift for unassignment"
msgstr "最遲在班次/更份前多少天可取消指派"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Deadline"
msgstr "截止日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__unassign_deadline
msgid "Deadline for unassignment"
msgstr "取消指派期限"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Deadline in days for shift unassignment"
msgstr "取消輪班指派的限期（天）"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
#: model:ir.model.fields,field_description:planning.field_resource_resource__default_role_id
msgid "Default Role"
msgstr "預設角色"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
msgid "Default Roles"
msgstr "預設角色"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid ""
"Define a work email address for the following employees so they will receive"
" the planning."
msgstr "為以下員工定義工作電郵地址，讓他們收取工作計劃。"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__role_id
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you need to complete a mission. Then, "
"assign those open shifts to the resources that are available."
msgstr "定義你的資源所扮演的職能角色（如廚師、調酒師、服務員等)。為完成任務所需的角色建立開放輪班。然後，為那些開放的輪班分配給可用的資源。"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you will be needing to complete a mission. "
"Then, assign those open shifts to the resources that are available."
msgstr ""
"定義您的資源執行的角色（例如廚師、調酒師、服務生......)。為完成任務所需的角色，建立開放式輪班。 然後，將這些開放班次分配給可用資源。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
#, python-format
msgid "Delete"
msgstr "刪除"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.js:0
#, python-format
msgid "Delete Recurring Shift"
msgstr "刪除重複性班次"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__department_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Department"
msgstr "部門"

#. module: planning
#: model:ir.model,name:planning.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "離職作業"

#. module: planning
#: model:planning.role,name:planning.planning_role_developer
msgid "Developer"
msgstr "開發人員"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Discard"
msgstr "捨棄"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "Distribute your material resources across projects and sales orders."
msgstr "將物料資源分配至不同專案項目及銷售訂單。"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Doris Cole (Bartender)"
msgstr "Doris Cole（酒保）"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__draft
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#, python-format
msgid "Draft"
msgstr "草稿"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. Press CTRL (or Cmd on Mac) while dragging a shift to duplicate "
"it."
msgstr ""
"將班次拖曳至其他日子便可改期，或拖曳至另一列，以重新分配班次。拖曳班次時，可按住 Ctrl 鍵（或 Mac 電腦上的 Cmd 鍵）將班次複製。"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__duration
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration
msgid "Duration"
msgstr "時長"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration_days
msgid "Duration Days"
msgstr "持續天數"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit"
msgstr "編輯"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Eli Lambert (Waiter)"
msgstr "Eli Lambert（侍應）"

#. module: planning
#: model:mail.template,description:planning.email_template_shift_switch_email
msgid ""
"Email sent automatically when employee self assigns to an unwanted shift of "
"another employee"
msgstr "員工向另一員工分配自己不想要的班次時，自動發送電子郵件"

#. module: planning
#: model:mail.template,description:planning.email_template_slot_single
msgid "Email sent automatically when publishing a shift"
msgstr "發佈班次時自動發送電子郵件"

#. module: planning
#: model:mail.template,description:planning.email_template_planning_planning
msgid ""
"Email sent automatically when publishing the schedule of your employees"
msgstr "發佈員工工作時間表時，自動發送電子郵件"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__employee_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
msgid "Employee"
msgstr "員工"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Employee Name"
msgstr "員工姓名"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings_employee
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Employees"
msgstr "員工"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__employee_ids
msgid ""
"Employees who will receive planning by email if you click on publish & send."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Employees with no Work Email"
msgstr "沒有工作電郵的員工"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__employees_no_email
msgid "Employees without email"
msgstr "沒有電郵的員工"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "End"
msgstr "結束"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "End Date"
msgstr "結束日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__end_time
msgid "End Hour"
msgstr "結束時間"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr "錯誤：員工權杖必須獨一無二"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Every"
msgstr "每"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Every %s week(s) until %s"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr "額外訊息"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
msgid ""
"Find here your planning. Assign yourself open shifts that match your roles, "
"or indicate your unavailability."
msgstr "此處可找到你的規劃。為你自己分配與你職能角色相符的開放班次，或指明你沒空工作的班次。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Fixed Hours"
msgstr "固定工時"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Flexible Hours"
msgstr "靈活工時"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "預測"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "永遠"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Forever, every %s week(s)"
msgstr "每 %s 星期舉行，永遠重複"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Generate shifts"
msgstr "產生班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr "Give depth to your"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Group By"
msgstr "分組依據"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_base__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_public__has_slots
msgid "Has Slots"
msgstr "有空檔時段"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__request_to_switch
#: model:ir.model.fields,field_description:planning.field_planning_slot__request_to_switch
msgid "Has there been a request to switch on this shift slot?"
msgstr "是否有請求過調換此輪班時段？"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__name
msgid "Hours"
msgstr "小時"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__user
#, python-format
msgid "Human"
msgstr "人力資源"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Am Unavailable"
msgstr "我沒空"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Take It"
msgstr "我頂替"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr "我沒空"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
msgid "ID"
msgstr "識別號"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr "若勾選，表示班次內容在上次發佈之後已變更。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"If you are happy with your planning, you can now <b>send</b> it to your "
"employees."
msgstr "若你滿意此工作計劃，現在可<b>發送</b>給你的員工。"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr "包括開放班次"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr "包括開放班次"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_hatched
msgid "Is Hatched"
msgstr "Is Hatched"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_assigned_to_me
msgid "Is This Shift Assigned To The Current User"
msgstr "此班次是否已指派給目前使用者"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_past
msgid "Is This Shift In The Past?"
msgstr "此輪班是在過去的嗎？"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_users_role
msgid "Is the shifts role one of the current user roles"
msgstr "輪班角色是否目前使用者的職能角色之一"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_unassign_deadline_passed
msgid "Is unassignement deadline not past"
msgstr "取消指派期限是否已過"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Jeffrey Kelly (Bartender)"
msgstr "Jeffrey Kelly（酒保）"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__job_title
#: model:ir.model.fields,field_description:planning.field_planning_slot__job_title
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Job Title"
msgstr "職務頭銜"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__last_generated_end_datetime
msgid "Last Generated End Date"
msgstr "最後產生的結束日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#, python-format
msgid "Legend"
msgstr "圖例"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__allow_self_unassign
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr "讓員工取消指派自己"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Let employees unassign themselves from shifts"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Let's create your first <b>shift</b>. <i>Tip: use the (+) shortcut available"
" on each cell of the Gantt view to save time.</i>"
msgstr "讓我們建立你的第一個<b>班次</b>。<i></i>提示：在甘特圖檢視畫面的每個儲存格上，可使用 (+) 捷徑，節省操作時間。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Let's start managing your employees' schedule!"
msgstr "讓我們開始管理員工的工作時間表吧！"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#, python-format
msgid "List"
msgstr "清單"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Make encoding shifts easy with templates."
msgstr "利用範本，使輪班編碼更容易。"

#. module: planning
#: model:planning.role,name:planning.planning_role_management
msgid "Management"
msgstr "管理"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__manager_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Manager"
msgstr "經理"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__material
#, python-format
msgid "Material"
msgstr "物料"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_resources
#: model:ir.ui.menu,name:planning.planning_menu_settings_resource
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Materials"
msgstr "材料"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr "上次發佈後已修改"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Monday, 13"
msgstr "週一,13"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#, python-format
msgid "Month"
msgstr "月"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__month
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__month
msgid "Months"
msgstr "月"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Department"
msgstr "我的部門"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr "我的規劃"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Roles"
msgstr "我的角色"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Shifts"
msgstr "我的班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Team"
msgstr "我的團隊"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "名稱"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
#, python-format
msgid "New Shift"
msgstr "新班次"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid "No Email Address For Some Employees"
msgstr "部份員工沒有電郵地址"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_number
msgid "No Of Repetitions of the plannings"
msgstr "規劃重複次數"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "暫無資料！"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "No material resources found. Let's create one!"
msgstr "未找到物料資源，讓我們建立一個吧！"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found. Let's create one!"
msgstr "未找到角色，讓我們建立一個吧！"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found. Let's create one!"
msgstr "未找到班次範本。讓我們建立一個吧！"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found. Let's create one!"
msgstr "找不到班次。讓我們創建一個吧！"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__name
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Note"
msgstr "備註"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Now that this week is ready, let's get started on <b>next week's "
"schedule</b>."
msgstr "現在既然已準備好本週的計劃，就讓我們開始擬定<b>下週的工作日程</b>。"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__x_times
msgid "Number of Occurrences"
msgstr "出現次數"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__x_times
msgid "Number of Repetitions"
msgstr "重複次數"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Open Shift"
msgstr "開放班次"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_model.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_model.js:0
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.slot_report
#, python-format
msgid "Open Shifts"
msgstr "開放班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "Open Shifts Available"
msgstr "有開放的班次"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid ""
"Operation not supported, you should always compare overlap_slot_count to 0 "
"value with = or > operator."
msgstr "不支援該操作，應該只用 = 或 > 運算符將overlap_slot_count與0值進行比較。"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__overlap_slot_count
msgid "Overlapping Slots"
msgstr "重疊時段"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "期間"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Plan all of your shifts in one click by <b>copying the previous week's "
"schedule</b>."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr "規劃跨專案的資源分配，並更準確地估計截止日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Planned Hours"
msgstr "已計劃的時數"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model:ir.actions.report,name:planning.report_planning_slot
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:planning.slot_report
#, python-format
msgid "Planning"
msgstr "計劃"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
msgid "Planning Analysis"
msgstr "規劃分析"

#. module: planning
#: model:ir.model,name:planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "計劃分析報表"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Planning Meeting"
msgstr "規劃會議"

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr "規劃重複次數"

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr "計劃職能"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr "規劃角色清單"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr "計劃排班"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__slot_id
msgid "Planning Slot"
msgstr "計劃時段"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__slot_properties_definition
msgid "Planning Slot Properties"
msgstr "規劃時段屬性"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr "規劃："

#. module: planning
#: model:mail.template,name:planning.email_template_planning_planning
msgid "Planning: New Schedule"
msgstr "規劃：新安排"

#. module: planning
#: model:mail.template,name:planning.email_template_slot_single
msgid "Planning: New Shift"
msgstr "規劃：新班次"

#. module: planning
#: model:mail.template,name:planning.email_template_shift_switch_email
msgid "Planning: Shift Re-assigned"
msgstr "規劃：班次已重新分配"

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
msgid "Planning: generate next recurring shifts"
msgstr "規劃：產生之後的重複班次"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new open shift available on"
msgstr "規劃：新的開放更份可用，在"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new shift on"
msgstr "規劃：新的班次在"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "計劃"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__previous_template_id
msgid "Previous Template"
msgstr "上一個範本"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__module_project_forecast
msgid "Project Planning"
msgstr "專案計劃"

#. module: planning
#: model:planning.role,name:planning.planning_role_projector
msgid "Projector"
msgstr "投影機"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__slot_properties
msgid "Properties"
msgstr "屬性"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#, python-format
msgid "Publish"
msgstr "公開"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_publish_and_send
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Publish & Send"
msgstr "發佈並傳送"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__published
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__published
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#, python-format
msgid "Published"
msgstr "已發佈"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr "班次產生頻率"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrence_update
msgid "Recurrence Update"
msgstr "循環活動更新"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__recurrency_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrency_id
msgid "Recurrency"
msgstr "定期"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Recurring Shifts"
msgstr "重複性班次"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid ""
"Recurring shifts cannot be planned further than 999 days in the future. If "
"you need to schedule beyond this limit, please set the recurrence to repeat "
"forever instead."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr "相關規劃記項"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr "用於管理資源存取權限的使用者名"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Remove"
msgstr "移除"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "重複"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "重複"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr "重複類型"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_unit
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_unit
msgid "Repeat Unit"
msgstr "重複單位"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "重複直到"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr "循環間隔為每"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_number
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_number
msgid "Repetitions"
msgstr "重複次數"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "報告"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Requests to Switch"
msgstr "調班申請"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_reset_to_draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Reset to Draft"
msgstr "重設為草稿"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Resource"
msgstr "資源"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_color
msgid "Resource color"
msgstr "資源顏色"

#. module: planning
#: model:ir.model,name:planning.model_resource_resource
#: model:ir.model.fields,field_description:planning.field_planning_role__resource_ids
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
msgid "Resources"
msgstr "資源"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Role"
msgstr "角色"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__default_planning_role_id
msgid ""
"Role that will be selected by default when creating a shift for this employee.\n"
"This role will also have precedence over the other roles of the employee when planning orders."
msgstr ""
"為此員工建立班次時，預設選擇的職能角色。\n"
"規劃訂單時，此角色會較員工的其他角色優先考慮。"

#. module: planning
#: model:ir.model.fields,help:planning.field_resource_resource__default_role_id
msgid ""
"Role that will be selected by default when creating a shift for this resource.\n"
"This role will also have precedence over the other roles of the resource when planning shifts."
msgstr ""
"為此資源建立班次時，預設選擇的職能角色。\n"
"規劃班次時，此角色會較資源的其他角色優先考慮。"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
#: model:ir.model.fields,field_description:planning.field_resource_resource__role_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Roles"
msgstr "Roles"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__planning_role_ids
msgid ""
"Roles that the employee can fill in. When creating a shift for this employee, only the shift templates for these roles will be displayed.\n"
"Similarly, only the open shifts available for these roles will be sent to the employee when the schedule is published.\n"
"Additionally, the employee will only be assigned orders for these roles (with the default planning role having precedence over the other ones).\n"
"Leave empty for the employee to be assigned shifts regardless of the role."
msgstr ""
"員工可使用的職能角色。為這名員工建立班次時，只會顯示這些角色的班次範本。\n"
"同樣，發佈工作時間表時，只有這些角色的可用開放班次，才會發送給員工。\n"
"此外，員工只會獲指派這些角色的訂單（預設規劃角色會較其他角色優先考慮）。\n"
"若留空，員工可以獲指派任何角色的班次。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "儲存"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Save & Send Schedule"
msgstr "儲存並傳送工作時間表"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_creation
msgid "Save as Template"
msgstr "儲存為範本"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Save this shift once it is ready."
msgstr "完成設定後，請儲存此輪班。"

#. module: planning
#: model:planning.role,name:planning.planning_role_scanner
msgid "Scanner"
msgstr "掃瞄儀"

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "預約"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_resource
msgid "Schedule by Resource"
msgstr "按資源安排"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr "按角色安排"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr "為不同職能、專案項目及銷售訂單，安排人力及物力資源。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Search operation not supported"
msgstr "不支援此搜尋操作"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__employee_token
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
#: model:ir.model.fields,field_description:planning.field_planning_slot__access_token
msgid "Security Token"
msgstr "安全金鑰"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Send"
msgstr "發送"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr "傳送規劃"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Send Planning By Email"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#, python-format
msgid "Send schedule"
msgstr "傳送工作時間表"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__sequence
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__sequence
msgid "Sequence"
msgstr "序列號"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "設定"

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#, python-format
msgid "Shift"
msgstr "排班"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr "班次清單"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr "排班範本"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr "班次範本表單"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr "班次範本列表"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr "班次範本"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts Planned"
msgstr "已規劃班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr "班次來自"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#, python-format
msgid "Shifts in Conflict"
msgstr "互相衝突班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Department Member"
msgstr "你所屬部門的員工班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Team Member"
msgstr "你團隊的員工班次"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__slot_ids
msgid "Slot"
msgstr "時段"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr "此輪班發佈後，已有一些更改。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Start"
msgstr "開始"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Start & End Hours"
msgstr "開始及結束時間"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Start Date"
msgstr "開始日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__state
#: model:ir.model.fields,field_description:planning.field_planning_slot__state
msgid "Status"
msgstr "狀態"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr "截止日期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_autocomplete_ids
msgid "Template Autocomplete"
msgstr "範本自動完成"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_reset
msgid "Template Reset"
msgstr "範本重設"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_res_company_planning_self_unassign_days_before_positive
msgid ""
"The amount of days before unassignment must be positive or equal to zero."
msgstr "取消指派前的天數，必須為正數或等於零。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The company does not allow you to self unassign."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The deadline for unassignment has passed."
msgstr "取消指派的期限已過。"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_positive
msgid "The duration cannot be negative."
msgstr "持續時長不可為負數。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "The duration is too long."
msgstr "持續時長太長。"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "The end date of a shift should be after its start date."
msgstr "輪班的結束日期應在開始日期之後。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "重複次數不能為負數。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
#, python-format
msgid "The open shifts have been successfully assigned."
msgstr "開放的輪班已成功分配。"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "The recurrence should be greater than 0."
msgstr "重複間隔應大於 0。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The recurrence's end date should fall after the shift's start date."
msgstr "重複事件的結束日期，應在輪班的開始日期之後。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#, python-format
msgid "The recurring shifts have successfully been created."
msgstr "已成功建立重複輪班。"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid "The schedule was successfully sent to your employees."
msgstr "工作時間表已成功發送給你的員工。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "The selected duration creates a date too far into the future."
msgstr "選取的持續時長，會建立一個太久遠的未來日期。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The shift has successfully been sent."
msgstr "輪班已成功發送。"

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/static/src/views/planning_hooks.js:0
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid ""
"The shifts have already been published, or there are no shifts to publish."
msgstr "班次已發佈，或沒有需要發佈的班次。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The shifts have been successfully reset to draft."
msgstr "輪班已成功重設為草稿。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The shifts have successfully been published and sent."
msgstr "輪班已成功發佈及傳送。"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "The start hour cannot be greater than 24."
msgstr "開始時間不可大於 24。"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "The start hour cannot be negative."
msgstr "開始時間不可為負數。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "The start hour must be greater or equal to 0 and lower than 24."
msgstr "開始時間必須大於或等於 0、並小於 24。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "There are no resources available for this open shift."
msgstr "沒有資源可用於此開放輪班。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
#, python-format
msgid ""
"There are no shifts planned for the previous week, or they have already been"
" copied."
msgstr "上週沒有已規劃輪班，或者輪班已被複製。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "There are no shifts to publish and send."
msgstr "沒有需要發佈及傳送的輪班。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "There are no shifts to reset to draft."
msgstr "沒有可重設為草稿的輪班。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "This Progress Bar is not implemented."
msgstr "此進度列未被採用。"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr "此輪班複製自上週"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__subsequent
msgid "This and following shifts"
msgstr "本次及之後班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"This open shift is no longer available, or the planning has been updated in "
"the meantime. Please contact your manager for further information."
msgstr "此開放班次已不再可用，或在此期間有關規劃已更新。要獲取更多資訊，請與你的經理聯絡。"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__this
msgid "This shift"
msgstr "此班次"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
#, python-format
msgid "This shift is recurrent. Delete:"
msgstr "此為重複性班次。你想刪除："

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#, python-format
msgid "This shift was successfully saved as a template."
msgstr "輪班已成功儲存為範本。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_kanban_view
msgid "Timezone:"
msgstr "時區："

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr "提示：更快記錄你的規劃"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat
msgid ""
"To avoid polluting your database and performance issues, shifts are only "
"created for the next 6 months. They are then gradually created as time "
"passes by in order to always get shifts 6 months ahead. This value can be "
"modified from the settings of Planning, in debug mode."
msgstr ""
"為避免污染資料庫及性能問題，排班只會建立未來6個月的班次。隨時間推移，班次會逐漸創建，以確保始終提前6 "
"個月獲取班次。如要修改該值，可以打開「規劃」的設定頁面修改（請使用除錯模式）。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#, python-format
msgid "Today"
msgstr "今天"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Total"
msgstr "總計"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Tuesday, 14"
msgstr "週二,14"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_type
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_type
msgid "Type"
msgstr "類型"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "直到"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Unwanted Shifts Available"
msgstr "有其他人不想要的班次"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr "規劃應重複直至哪一天"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__user_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "使用者"

#. module: planning
#. odoo-python
#: code:addons/planning/models/hr.py:0
#: model:ir.actions.server,name:planning.model_hr_employee_action_planning_view
#: model:ir.actions.server,name:planning.model_hr_employee_public_action_planning_view
#, python-format
msgid "View Planning"
msgstr "查看計劃"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Waiter"
msgstr "侍應"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Wednesday, 15"
msgstr "週三,15"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#, python-format
msgid "Week"
msgstr "星期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__week
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__week
msgid "Weeks"
msgstr "星期"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_address_id
msgid "Work Address"
msgstr "工作地址"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "工作電郵"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_location_id
msgid "Work Location"
msgstr "工作地點"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__working_days_count
#: model:ir.model.fields,field_description:planning.field_planning_slot__working_days_count
msgid "Working Days"
msgstr "工作日數"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Write the <b>role</b> your employee will perform (<i>e.g. Chef, Bartender, "
"Waiter, etc.</i>). <i>Tip: Create open shifts for the roles you will be "
"needing to complete a mission. Then, assign those open shifts to the "
"resources that are available.</i>"
msgstr ""
"寫下你的員工會執行的<b>角色</b>（例如廚師、調酒師、侍應等<i></i>)。<i></i>提示：為完成任務所需的角色，建立開放輪班。然後，將這些開放班次分配給可用資源。"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__year
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__year
msgid "Years"
msgstr "年"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You are not allowed to reset to draft shifts."
msgstr "你不可將輪班重設為草稿。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not assign yourself to an already assigned shift."
msgstr "你不可將自己指派給已經指派的班次。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not cancel a request to switch made by another user."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid ""
"You can not request to switch a shift that is assigned to another user."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not unassign another employee than yourself."
msgstr "你不可取消指派其他員工，只可取消指派你自己。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You cannot cancel a request to switch that is in the past."
msgstr "不可取消過去的調班請求。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You cannot switch a shift that is in the past."
msgstr "調班不可調換成過去的班次。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You don't have the right to cancel a request to switch."
msgstr "你沒有權限取消調班請求。"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You don't have the right to self assign."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You don't have the right to switch shifts."
msgstr "你沒有權限調班。"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr "你已成功取消指派此班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr "你的規劃"

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from {{ format_date(ctx.get('start_datetime')) }} to {{ "
"format_date(ctx.get('end_datetime')) }}"
msgstr ""
"你的規劃，由 {{ format_date(ctx.get('start_datetime')) }} 至 {{ "
"format_date(ctx.get('end_datetime')) }}"

#. module: planning
#: model:mail.template,subject:planning.email_template_shift_switch_email
msgid ""
"Your shift on {{ ctx.get('start_datetime') }} was re-assigned to {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"
msgstr ""
"你在 {{ ctx.get('start_datetime') }} 的班次，已重新編配給 {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
#, python-format
msgid "all shifts"
msgstr "所有班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "days before the beginning of the shift"
msgstr "天（輪班開始前）"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "days span)"
msgstr "天期間）"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "e.g. Bartender"
msgstr "例：酒保"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. Cleaner"
msgstr "例：清潔員"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_form_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_tree_view_inherit
msgid "e.g. Crane"
msgstr "例：起重機"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. John Doe"
msgstr "例：陳大文"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "other shift(s)"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr "個其他班次有衝突。"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
#, python-format
msgid "this and following shifts"
msgstr "本次及之後班次"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
#, python-format
msgid "this shift"
msgstr "此班次"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr "到"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
msgstr ""
"{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
