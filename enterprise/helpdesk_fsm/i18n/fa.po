# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_fsm
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>y <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__company_id
msgid "Company"
msgstr "شرکت"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "تبدیل درخواست‌های پشتیبانی به وظیفه"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create & View Task"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create Task"
msgstr "ایجاد تکلیف"

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/models/helpdesk_ticket.py:0
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_create_fsm_task
#, python-format
msgid "Create a Field Service task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Customer"
msgstr "مشتری"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Discard"
msgstr "رها کردن"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__display_helpdesk_ticket_button
msgid "Display Ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_team__fsm_project_id
msgid "FSM Project"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Field Service"
msgstr "سرویس در محل"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_count
msgid "Fsm Task Count"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "تیم میز پشتیبانی"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "تیکیت پشتیبانی"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__id
msgid "ID"
msgstr "شناسه"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__helpdesk_ticket_id
msgid "Original Ticket"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Plan Intervention"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_team_view_form
msgid "Project"
msgstr "پروژه"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
msgid "Project in which to create the task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__helpdesk_ticket_id
msgid "Related ticket"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: model:ir.model,name:helpdesk_fsm.model_project_task
#, python-format
msgid "Task"
msgstr "وظیفه"

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_canceled
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_canceled
msgid "Task Canceled"
msgstr ""

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_done
msgid "Task Done"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
#, python-format
msgid "Task created"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: code:addons/helpdesk_fsm/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
#, python-format
msgid "Tasks"
msgstr "وظایف"

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
#, python-format
msgid "Tasks from Tickets"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
msgid "Tasks generated from this ticket"
msgstr ""

#. module: helpdesk_fsm
#. odoo-python
#: code:addons/helpdesk_fsm/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
#, python-format
msgid "Ticket"
msgstr "تیکت"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket from this task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Ticket's customer, will be linked to the task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__name
msgid "Title"
msgstr "عنوان"
