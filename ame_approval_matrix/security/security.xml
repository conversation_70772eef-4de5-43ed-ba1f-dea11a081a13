<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record model="ir.rule" id="model_almoiss_approval_matrix_sale_comp_rule">
            <field name="name">Sale Approval Matrix multi-company</field>
            <field name="model_id" ref="model_almoiss_approval_matrix_sale"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>

        <record model="ir.rule" id="model_almoiss_approval_matrix_comp_rule">
            <field name="name">Purchase Approval Matrix multi-company</field>
            <field name="model_id" ref="model_almoiss_approval_matrix"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>

         <record model="ir.rule" id="model_almoiss_approval_matrix_credit_note_comp_rule">
            <field name="name">Credit Note Approval Matrix multi-company</field>
            <field name="model_id" ref="model_almoiss_approval_matrix_credit_note"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>

    </data>
</odoo>