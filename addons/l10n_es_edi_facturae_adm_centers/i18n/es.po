# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_facturae
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~16.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-07 09:51+0000\n"
"PO-Revision-Date: 2023-12-07 10:54+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_partner__type
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_users__type
msgid ""
"- Contact: Use this to organize the contact details of employees of a given "
"company (e.g. CEO, CFO, ...).\n"
"- Invoice Address: Preferred address for all invoices. Selected by default "
"when you invoice an order that belongs to this company.\n"
"- Delivery Address: Preferred address for all deliveries. Selected by "
"default when you deliver an order that belongs to this company.\n"
"- Private: Private addresses are only visible by authorized users and "
"contain sensitive data (employee home addresses, ...).\n"
"- Other: Other address for the company (e.g. subsidiary, ...)"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_facturae_adm_centers.view_partner_form_inherit_l10n_es_edi_facturae
msgid ""
"<span>Administrative Center for Spain Public Administrations. Used in "
"Spanish electronic invoices.</span>"
msgstr ""
"<span>Centro Administrativo de Administraciones Públicas de España. "
"Utilizado en facturas electrónicas españolas.</span>"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_partner__type
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_users__type
msgid "Address Type"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model,name:l10n_es_edi_facturae_adm_centers.model_l10n_es_edi_facturae_adm_centers_ac_role_type
msgid "Administrative Center Role Type"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_04
msgid "Buyer"
msgstr "Comprador"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_center_code
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_center_code
msgid "Code"
msgstr "Código"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_logical_operational_point
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_logical_operational_point
msgid ""
"Code identifying the company. Barcode of 13 standard positions. Codes are "
"registered in Spain by AECOC. The code is made up of the country code (2 "
"positions) Spain is '84' + Company code (5 positions) + the remaining "
"positions. The last one is the product + check digit."
msgstr ""
"Punto Lógico Operacional. Código identificativo de la Empresa. Código de "
"barras de 13 posiciones estándar. Valores registrados por AECOC. Recoge el "
"código de País (2p) España es ’84’ + Empresa (5p) + los restantes - el "
"último es el producto + dígito de control."

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_center_code
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_center_code
msgid "Code of the issuing department."
msgstr "Número del Departamento Emisor."

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_08
msgid "Collection Receiver"
msgstr "Receptor del cobro"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_05
msgid "Collector"
msgstr "Cobrador"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model,name:l10n_es_edi_facturae_adm_centers.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields.selection,name:l10n_es_edi_facturae_adm_centers.selection__res_partner__type__facturae_ac
msgid "FACe Center"
msgstr "Centro FACe"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_01
msgid "Fiscal"
msgstr "Fiscal"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__id
msgid "ID"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_physical_gln
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_physical_gln
msgid ""
"Identification of the connection point to the VAN EDI (Global Location "
"Number). Barcode of 13 standard positions. Codes are registered in Spain by "
"AECOC. The code is made up of the country code (2 positions) Spain is '84' + "
"Company code (5 positions) + the remaining positions. The last one is the "
"product + check digit."
msgstr ""
"GLN Físico. Identificación del punto de conexión a la VAN EDI (Global "
"Location Number). Código de barras de 13 posiciones estándar. Valores "
"registrados por AECOC. Recoge el código de País (2p) España es ’84’ + "
"Empresa (5p) + los restantes - el último es el producto + dígito de control."

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_09
msgid "Issuer"
msgstr "Emisor"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_role_type_ids
#: model:ir.model.fields,help:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_role_type_ids
msgid ""
"It indicates the role played by the Operational Point defined as a Workplace/"
"Department.\n"
"These functions are:\n"
"- Receiver: Workplace associated to the recipient's tax identification "
"number where the invoice will be received.\n"
"- Payer: Workplace associated to the recipient's tax identification number "
"responsible for paying the invoice.\n"
"- Buyer: Workplace associated to the recipient's tax identification number "
"who issued the purchase order.\n"
"- Collector: Workplace associated to  the issuer's tax identification number "
"responsible for handling the collection.\n"
"- Fiscal: Workplace associated to the recipient's tax identification number, "
"where an Operational Point mailbox is shared by different client companies "
"with different tax identification numbers and it is necessary to "
"differentiate between where the message is received (shared letterbox) and "
"the workplace where it must be stored (recipient company)."
msgstr ""
"Indica la función de un Punto Operacional (P.O.) definido como Centro/"
"Departamento.\n"
"Estas funciones son:\n"
"- Receptor: Centro del NIF receptor destinatario de la factura..\n"
"- Pagador: Centro del NIF receptor responsable de pagar la factura.\n"
"- Comprador: Centro del NIF receptor que emitió el pedido.\n"
"- Cobrador: Centro del NIF emisor responsable de gestionar el cobro.\n"
"- Fiscal: Centro del NIF receptor de las facturas, cuando un P.O. buzón es "
"compartido por varias empresas clientes con diferentes NIF.s y es necesario "
"diferenciar el receptor del mensaje (buzón común) del lugar donde debe "
"depositarse (empresa destinataria)."

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model,name:l10n_es_edi_facturae_adm_centers.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_logical_operational_point
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_logical_operational_point
msgid "Logical Operational Point"
msgstr "Punto Lógico Operacional"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_l10n_es_edi_facturae_adm_centers_ac_role_type__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_03
msgid "Payer"
msgstr "Pagador"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_07
msgid "Payment Receiver"
msgstr "Receptor del pago"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_physical_gln
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_physical_gln
msgid "Physical GLN"
msgstr "GLN Físico"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_02
msgid "Receiver"
msgstr "Receptor"

#. module: l10n_es_edi_facturae_adm_centers
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_partner__l10n_es_edi_facturae_ac_role_type_ids
#: model:ir.model.fields,field_description:l10n_es_edi_facturae_adm_centers.field_res_users__l10n_es_edi_facturae_ac_role_type_ids
msgid "Roles"
msgstr "Roles"

#. module: l10n_es_edi_facturae_adm_centers
#: model:l10n_es_edi_facturae_adm_centers.ac_role_type,name:l10n_es_edi_facturae_adm_centers.ac_role_type_06
msgid "Seller"
msgstr "Vendedor"

#. module: l10n_es_edi_facturae_adm_centers
#. odoo-python
#: code:addons/l10n_es_edi_facturae_adm_centers/models/res_partner.py:0
#, python-format
msgid "The Logical Operational Point entered is not valid."
msgstr "El Punto Lógico Operacional ingresado no es válido."

#. module: l10n_es_edi_facturae_adm_centers
#. odoo-python
#: code:addons/l10n_es_edi_facturae_adm_centers/models/res_partner.py:0
#, python-format
msgid "The Physical GLN entered is not valid."
msgstr "El GLN Físico ingresado no es válido."
