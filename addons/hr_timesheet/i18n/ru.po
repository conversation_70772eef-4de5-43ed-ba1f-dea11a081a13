# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2023
# Collex100, 2023
# <PERSON><PERSON>, 2023
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2023
# ale<PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "#{timesheet.employee_id.name}"
msgstr "#{timesheet.employee_id.name}"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "%(effective)s %(uom_name)s"
msgstr "%(effective)s %(uom_name)s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "%(effective)s / %(allocated)s %(uom_name)s"
msgstr "%(effective)s / %(allocated)s %(uom_name)s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "%(effective)s / %(allocated)s %(uom_name)s (%(success_rate)s%%)"
msgstr "%(effective)s / %(allocated)s %(uom_name)s (%(success_rate)s%%)"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "%(exceeding_hours)s %(uom_name)s (+%(exceeding_rate)s%%)"
msgstr "%(exceeding_hours)s %(uom_name)s (+%(exceeding_rate)s%%)"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "%(name)s's Timesheets"
msgstr "%(name)s's Timesheets"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr "%s Потрачено"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(%(sign)s%(hours)s:%(minutes)s осталось)"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "(%s days remaining)"
msgstr "(%s оставшихся дней)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "(incl."
msgstr "(вкл."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "1 day"
msgstr "1 день"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2 hours"
msgstr "2 часа"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2021-09-01"
msgstr "2021-09-01"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr "<b class=\"tip_title\">Совет: Ведите учет рабочего времени быстрее</b>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<i class=\"fa fa-print\"/> View Details"
msgstr "<i class=\"fa fa-print\"/> Посмотреть детали"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">Табели</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" invisible=\"encode_uom_in_days\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" invisible=\"not encode_uom_in_days\">Days Spent on Sub-tasks:</span>"
msgstr ""
"<span class=\"text-nowrap\" invisible=\"encode_uom_in_days\">Часы, потраченные на выполнение подзадач:</span>\n"
"                                <span class=\"text-nowrap\" invisible=\"not encode_uom_in_days\">Дни, потраченные на выполнение подзадач:</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid ""
"<span invisible=\"not has_timesheet\">\n"
"                        You cannot delete employees who have timesheets.\n"
"                        <span invisible=\"not has_active_employee\">\n"
"                            You can either archive these employees or first delete all of their timesheets.\n"
"                        </span>\n"
"                        <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                            Please first delete all of their timesheets.\n"
"                        </span>\n"
"                    </span>\n"
"                    <span invisible=\"has_timesheet\">\n"
"                        Are you sure you want to delete these employees?\n"
"                    </span>"
msgstr ""
"<span invisible=\"not has_timesheet\">\n"
"                        Вы не можете удалить сотрудников, у которых есть табели учета рабочего времени.\n"
"                       <span invisible=\"not has_active_employee\">\n"
"                            Вы можете либо заархивировать этих сотрудников, либо сначала удалить все их табели учета рабочего времени.\n"
"                       </span>\n"
"                       <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                            Пожалуйста, сначала удалите все их табели учета рабочего времени.\n"
"                       </span>\n"
"                    </span>\n"
"                    <span invisible=\"has_timesheet\">\n"
"                        Вы уверены, что хотите удалить этих сотрудников?\n"
"                    </span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">Всего (дней)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">Итого (часы)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>Дата</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>Описание</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Employee</span>"
msgstr "<span>Сотрудник</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days recorded on sub-tasks: </strong>"
msgstr ""
"<strong>Количество дней, затраченных на выполнение подзадач: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr "<strong>Продолжительность: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours recorded on sub-tasks: </strong>"
msgstr "<strong>Часы, затраченные на выполнение подзадач: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>Прогресс:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Remaining Days: </strong>"
msgstr "<strong>Оставшиеся дни: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Remaining Hours: </strong>"
msgstr "<strong>Оставшиеся часы: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total: </strong>"
msgstr "<strong>Всего: </strong>"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr "Активная учетная запись Analytic"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Все"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "Все временные таблицы"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allocated_hours
msgid "Allocated Hours"
msgstr "Распределенные часы"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__allocated_time
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__allocated_hours
msgid "Allocated Time"
msgstr "Распределенное время"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr "Разрешить ведение табеля учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__amount
msgid "Amount"
msgstr "Сумма"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr "Анализ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "Аналитический Счёт"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Аналитический вход"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Аналитическая линия"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""
"Аналитический счет, к которому привязан данный проект, его задачи и временные таблицы.\n"
"Отслеживайте расходы и доходы по проекту, устанавливая этот аналитический счет в соответствующих документах (например, в заказах на продажу, счетах-фактурах, заказах на закупку, счетах поставщиков, расходах и т. д.).\n"
"При необходимости этот аналитический счет можно изменить для каждой задачи в отдельности.\n"
"Аналитический счет необходим для использования табеля учета рабочего времени."

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Analyze the projects and tasks on which your employees spend their time.<br>\n"
"                Evaluate which part is billable and what costs it represents."
msgstr ""
"Проанализируйте проекты и задачи, на которые тратят свое время ваши сотрудники.<br>\n"
"                Определите, какая часть работы является оплачиваемой и какие расходы она представляет собой."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr "Напоминание об утверждении"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Archive Employees"
msgstr "Сотрудники архива"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr "Ассоциированные временные таблицы"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Audrey Peterson"
msgstr "Одри Петерсон"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Average of Progress"
msgstr "Среднее значение прогресса"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "Работник"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "По проекту"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "По задаче"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Call client and discuss project"
msgstr "Позвоните клиенту и обсудите проект"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Совместное участие в проекте"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__company_id
msgid "Company"
msgstr "Компания"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
#, python-format
msgid "Confirmation"
msgstr "Подтверждение"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "Дата"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Days"
msgstr "Дней"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__days
msgid "Days / Half-Days"
msgstr "Дни / полудни"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr "Потраченные дни"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Значение проекта по умолчанию для табеля учета рабочего времени, "
"сформированного по типу отгулов."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""
"Определите \"почасовую стоимость\" для сотрудника, чтобы отслеживать "
"стоимость его рабочего времени."

#. module: hr_timesheet
#: model:ir.actions.server,name:hr_timesheet.unlink_employee_action
msgid "Delete"
msgstr "Удалить"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Delete Employee"
msgstr "Удалить сотрудника"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "Отдел"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Describe your activity"
msgstr "Опишите свою деятельность"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__name
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "Описание"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Discard"
msgstr "Отменить"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__display_timesheet_stats
msgid "Display Timesheet Stats"
msgstr "Отображение статистики таймшетов"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "Продолжительность"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "Эффективные часы"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "Сотрудник"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_employee_delete_wizard
msgid "Employee Delete Wizard"
msgstr "Мастер удаления сотрудников"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Напоминание сотрудникам"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Employee Termination"
msgstr "Увольнение сотрудника"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__employee_ids
msgid "Employees"
msgstr "Сотрудники"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Employees' Timesheets"
msgstr "Табели учета рабочего времени сотрудников"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Кодировать Uom в днях"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_method
msgid "Encoding Method"
msgstr "Метод кодирования"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr "Кодировка Uom"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "Extra Time"
msgstr "Дополнительное время"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Generate timesheets for validated time off requests and public holidays"
msgstr ""
"Формирование табелей учета рабочего времени для подтвержденных заявок на "
"отгулы и праздничные дни"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизация HTTP"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__has_active_employee
msgid "Has Active Employee"
msgstr "Имеет действующего сотрудника"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__has_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__has_timesheet
msgid "Has Timesheet"
msgstr "Имеет табель учета рабочего времени"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
#, python-format
msgid "Hours"
msgstr "Часов"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__hours
msgid "Hours / Minutes"
msgstr "Часы / минуты"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Hours By Task (Including Subtasks)"
msgstr "Часы по задачам (включая подзадачи)"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr "Часы, потраченные"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Hours Spent on Sub-Tasks"
msgstr "Часы, затраченные на выполнение подзадач"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr "Внутренний"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "Внутренний проект"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr "Недопустимый оператор: %s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "Неверное значение: %s"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr "Дни кодирования"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr "Внутренний проект"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__job_title
msgid "Job Title"
msgstr "Название работы"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr "Прошлый месяц"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr "На прошлой неделе"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr "В прошлом году"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
msgid "Log time on tasks"
msgstr "Отслеживание времени по задачам"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__manager_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__manager_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Manager"
msgstr "Руководитель"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr "Встреча"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "Меню"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Department's Updates"
msgstr "Обновления в моем отделе"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Team's Updates"
msgstr "Обновления моей команды"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "Мои табели учета рабочего времени"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Новые"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "Не найдено ни одного мероприятия. Давайте начнем новую!"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No data yet!"
msgstr "Пока нет данных!"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Нет"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid "Number of allocated hours minus the number of hours spent."
msgstr "Количество выделенных часов минус количество проведенных часов."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Ok"
msgstr "Ок"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__overtime
msgid "Overtime"
msgstr "Дополнительное время"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__parent_task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__parent_task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Parent Task"
msgstr "Родительская Задача"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Партнер"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Единица измерения продукта"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "Прогресс"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
#, python-format
msgid "Project"
msgstr "Проект"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "Единица измерения времени проекта"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_update
msgid "Project Update"
msgstr "Обновление проекта"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_project_overtime
msgid "Project in Overtime"
msgstr "Проект в сверхурочное время"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "Временные таблицы проекта"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__readonly_timesheet
msgid "Readonly Timesheet"
msgstr "Временная таблица, доступная только для чтения"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr "Записать новое действие"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"Ведите учет рабочего времени мгновенно, нажав Shift + соответствующую "
"горячую клавишу, чтобы добавить 15 минут к вашим проектам."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr "Оставшиеся дни"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Hours"
msgstr "Оставшееся время"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours_percentage
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours_percentage
msgid "Remaining Hours Percentage"
msgstr "Оставшиеся часы Процент"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr "Оставшееся оплаченное время"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Отчет"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "Research and Development"
msgstr "Исследования и разработки"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Research and Development/New Portal System"
msgstr "Исследования и разработки/Новая портальная система"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
msgid "Review all timesheets related to your projects"
msgstr ""
"Просматривайте все табели учета рабочего времени, связанные с вашими "
"проектами"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Поиск везде"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Поиск в описании"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr "Поиск среди сотрудников"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Поиск в проекте"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr "Поиск в задании"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "See Timesheets"
msgstr "Посмотреть табели учета рабочего времени"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "See timesheet entries"
msgstr "См. записи в табеле учета рабочего времени"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets approvers that still have "
"timesheets to validate"
msgstr ""
"Периодически отправляйте напоминание по электронной почте тем, кто "
"утверждает временные отчеты, которые еще не прошли проверку"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets users that still have "
"timesheets to encode"
msgstr ""
"Периодически отправляйте напоминание по электронной почте пользователям "
"табелей учета рабочего времени, которым еще нужно закодировать табели учета "
"рабочего времени"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Настройки"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Sub-Tasks Total Effective Hours"
msgstr "Подзадачи Всего эффективных часов"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Hours Spent"
msgstr "Потраченные часы на подзадачи"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
#, python-format
msgid "Task"
msgstr "Задача"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "Табели учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Анализ задач"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "Внутренний проект компании должен находиться в этой компании."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "Нет никаких табелей учета рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"На эти проекты ссылаются некоторые записи в табеле учета рабочего времени. "
"Прежде чем удалять эти проекты, необходимо удалить эти записи табеля учета "
"рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"На эти задачи ссылаются некоторые записи в табеле учета рабочего времени. "
"Прежде чем удалять эти задачи, необходимо удалить эти записи табеля учета "
"рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr "Этот квартал"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr "Этот месяц"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "This operator %s is not supported in this search method."
msgstr "Этот оператор %s не поддерживается в данном методе поиска."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"На этот проект ссылаются некоторые записи в табеле учета рабочего времени. "
"Перед удалением этого проекта необходимо удалить эти записи табеля учета "
"рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid ""
"This task cannot be private because there are some timesheets linked to it."
msgstr ""
"Эта задача не может быть частной, поскольку с ней связано несколько "
"временных таблиц."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"На эту задачу ссылаются некоторые записи в табеле учета рабочего времени. "
"Перед удалением этой задачи необходимо удалить эти записи в табеле учета "
"рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr "Эта неделя"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"Это установит единицу измерения, используемую в проектах и задачах. \n"
"Если вы используете расписание, ссылающееся на проекты, не забудьте установить правильную единицу измерения для ваших сотрудников."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr "Этот год"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "Кодирование времени"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "Отпуск"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""
"Время, затраченное на выполнение подзадач (и их собственных подзадач) этой "
"задачи."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task and its sub-tasks (and their own sub-tasks)."
msgstr ""
"Время, затраченное на выполнение этой задачи и ее подзадач (а также их "
"собственных подзадач)."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "Время, затраченное на выполнение этой задачи, включая ее подзадачи."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "Единица измерения времени, используемая для учета рабочего времени"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "Табель учета"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "Деятельность по ведению табеля учета рабочего времени"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "Расходы на ведение табеля учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
msgid "Timesheet Encode Uom"
msgstr "Timesheet Encode Uom"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Единица кодирования табеля"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__timesheet_percentage
msgid "Timesheet Percentage"
msgstr "Процент временных затрат"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_report_search
msgid "Timesheet Report"
msgstr "Отчет по табелю учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__timesheet_time
msgid "Timesheet Time"
msgstr "Табель учета рабочего времени"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_project_task_page
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "Расписания"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets - %s"
msgstr "Временные таблицы - %s"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
msgid "Timesheets 80%"
msgstr "Временные таблицы 80%"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_project_filter_inherit_timesheet
msgid "Timesheets >100%"
msgstr "Временные таблицы >100%"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
msgid "Timesheets Analysis"
msgstr "Анализ табелей учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Отчет об анализе временных таблиц"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "Контроль таймшетов"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "Временные таблицы по сотрудникам"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "Временные таблицы по проектам"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "Временные таблицы по задачам"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr "По этой задаче можно вести журнал учета рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets cannot be created on a private task."
msgstr "Временные таблицы не могут быть созданы для частного задания."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"Timesheets must be created on a project or a task with an active analytic "
"account."
msgstr ""
"Временные таблицы должны быть созданы в проекте или задаче с активной "
"аналитической учетной записью."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"Timesheets must be created with an active employee in the selected "
"companies."
msgstr ""
"Временные таблицы должны быть созданы с активным сотрудником в выбранных "
"компаниях."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Timesheets of %(name)s"
msgstr "Временные таблицы %(name)s"

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "Совет: Ведите учет рабочего времени быстрее"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr "Сегодня"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr "Всего"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Allocated Time"
msgstr "Общее выделенное время"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr "Всего дней"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Hours"
msgstr "Общее Кол-во Часов"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Remaining Hours"
msgstr "Всего оставшихся часов"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Общее время по табелю учета рабочего времени"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid ""
"Total number of time (in the proper UoM) recorded in the project, rounded to"
" the unit."
msgstr ""
"Общее количество времени (в соответствующем UoM), зафиксированное в проекте,"
" округленное до единицы."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "Итого:"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Отслеживайте время работы по проектам каждый день и выставляйте счета за это"
" время своим клиентам."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr "Тренинг"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__uom_id
msgid "Unit Of Measure"
msgstr "Единица измерения"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"Используйте эти ключевые слова в заголовке для постановки новых задач:\n"
"\n"
"        30h Выделите 30 часов на выполнение задачи\n"
"        #tags Установить теги на задаче\n"
"        @user Назначить задачу пользователю\n"
"        ! Установить высокий приоритет задачи\n"
"\n"
"        Убедитесь, что используете правильный формат и порядок, например, Улучшить экран конфигурации 5h #feature #v16 @Mitchell !"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__user_id
msgid "User"
msgstr "Пользователь"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "Пользователь: все временные таблицы"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "Пользователь: только собственные временные таблицы"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "View Details"
msgstr "Подробнее"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr "Виджет"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr "Вы не можете получить доступ к чужим табелям учета рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
#, python-format
msgid "You cannot delete employees who have timesheets."
msgstr ""
"Вы не можете удалить сотрудников, у которых есть табели учета рабочего "
"времени."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account. Please change this account, or reactivate the current one "
"to timesheet on the project."
msgstr ""
"Вы не можете вести учет времени в этом проекте, поскольку он связан с "
"неактивной учетной записью аналитика. Пожалуйста, измените эту учетную "
"запись или активируйте текущую, чтобы вести хронометраж по проекту."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr ""
"Вы не можете установить архивированного сотрудника на существующие табели "
"учета рабочего времени."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr ""
"Вы не можете использовать временные таблицы без аналитического аккаунта."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr "дней"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
msgid "for"
msgstr "для"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_project_task_page
msgid "for the"
msgstr "для"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr "часов"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"on\n"
"                            <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""
"на\n"
"                            <span class=\"fw-bold text-dark\"> Подзадачи</span>)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"on\n"
"                        <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""
"на\n"
"                        <span class=\"fw-bold text-dark\"> Подзадачи</span>)"
