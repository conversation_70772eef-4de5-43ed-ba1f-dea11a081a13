<eTransport xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="mfp:anaf:dgti:eTransport:declaratie:v2" xsi:schemaLocation="mfp:anaf:dgti:eTransport:declaratie:v2" codDeclarant="9000123456789" refDeclarant="receipt_picking">
	<notificare codTipOperatiune="10">
		<bunuriTransportate codScopOperatiune="201" codTarifar="00000000" denumireMarfa="product_a" cantitate="10.0" codUnitateMasura="C62" greutateNeta="10.0" greutateBruta="10.0" valoareLeiFaraTva="1000.0"/>
		<partenerComercial codTara="RO" denumire="RO Customer" cod="1234567897"/>
		<dateTransport nrVehicul="BN18CTL" nrRemorca1="B865MHO" codTaraOrgTransport="RO" codOrgTransport="8001011234567" denumireOrgTransport="RO Shipping Partner" dataTransport="2025-01-14"/>
		<locStartTraseuRutier codPtf="3"/>
		<locFinalTraseuRutier>
			<locatie codJudet="7" denumireLocalitate="Botosani" denumireStrada="Calea Nationala 85" codPostal="710052"/>
		</locFinalTraseuRutier>
		<documenteTransport tipDocument="30" dataDocument="2025-01-14" numarDocument="receipt_picking"/>
	</notificare>
</eTransport>