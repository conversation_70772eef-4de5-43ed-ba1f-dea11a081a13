# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""
"\"Añadir al\n"
"                tablero\""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Añadir a mi tablero"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
#, python-format
msgid "Are you sure that you want to remove this item?"
msgstr "¿Está seguro de que desea eliminar este elemento?"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Tablero"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Change Layout"
msgstr "Cambiar diseño"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "No se puede añadir un filtro al tablero"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Dashboard"
msgstr "Tablero"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
#, python-format
msgid "Invalid action"
msgstr "Acción no válida"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Layout"
msgstr "Diseño"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr "Mi tablero"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Vuelva a cargar su navegador para ver los cambios."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""
"Para añadir su primer informe a este tablero, vaya a cualquier\n"
"                menú, cambie a la vista de lista o gráfico y haga clic en"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""
"Puede filtrar y agrupar datos antes de agregarlos al\n"
"                tablero mediante las opciones de búsqueda."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Su tablero está vacío"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "in the extended search options."
msgstr "en las opciones de búsqueda extendida."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "“%s” added to dashboard"
msgstr "\"%s\" añadido al tablero"
