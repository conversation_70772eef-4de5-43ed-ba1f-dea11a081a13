# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tr_nilvera
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-28 16:01+0000\n"
"PO-Revision-Date: 2025-03-28 16:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid ""
"<i title=\"Go to Nilvera portal\" role=\"img\" aria-label=\"Go to Nilvera portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Nilvera portal"
msgstr ""
"<i title=\"Nilvera portalına git\" role=\"img\" aria-label=\"Nilvera portalına git\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Nilvera Portalı"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Test connection"
msgstr ""
"<i title=\"Test bağlantısı\" role=\"img\" aria-label=\"Test bağlantısı\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Test bağlantısı"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid "API KEY"
msgstr ""

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_partner__l10n_tr_nilvera_customer_alias_id
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_users__l10n_tr_nilvera_customer_alias_id
msgid "Alias"
msgstr "Takma ad"

#. module: l10n_tr_nilvera
#. odoo-python
#: code:addons/l10n_tr_nilvera/models/res_config_settings.py:0
#, python-format
msgid "An error occurred. Try again later."
msgstr "Bir hata oluştu. Daha sonra tekrar deneyin."

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_sa
msgid "Bags"
msgstr "Çuval"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_bx
msgid "Box"
msgstr "Kutu"

#. module: l10n_tr_nilvera
#: model:ir.model,name:l10n_tr_nilvera.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: l10n_tr_nilvera
#: model:ir.model,name:l10n_tr_nilvera.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid "Configure Nilvera settings"
msgstr "Nilvera ayarlarını yapılandırma"

#. module: l10n_tr_nilvera
#: model:ir.model,name:l10n_tr_nilvera.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_cr
msgid "Crate"
msgstr "Sandik"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__create_date
msgid "Created on"
msgstr "Üzerinde oluşturuldu"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_cmq
msgid "Cubic Centimeter - cm³"
msgstr "Santimetre Küp - cm³"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_mmq
msgid "Cubic Millimeter - mm³"
msgstr "Milimetre Küp - mm³"

#. module: l10n_tr_nilvera
#: model:ir.model,name:l10n_tr_nilvera.model_l10n_tr_nilvera_alias
msgid "Customer Alias on Nilvera"
msgstr "Nilvera Müşteri Takma Adları"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__display_name
msgid "Display Name"
msgstr "Ekran Adı"

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_partner__l10n_tr_nilvera_customer_status__earchive
msgid "E-Archive"
msgstr "E-Arşiv"

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_partner__l10n_tr_nilvera_customer_status__einvoice
msgid "E-Invoice"
msgstr "E-Fatura"

#. module: l10n_tr_nilvera
#: model:uom.category,name:l10n_tr_nilvera.product_uom_categ_energy
msgid "Energy"
msgstr "Enerji"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid "Environment"
msgstr "Çevre"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_partner__ubl_cii_format
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_users__ubl_cii_format
msgid "Format"
msgstr "Biçim"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_gt
msgid "Gross Ton"
msgstr "Brüt Ton"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__id
msgid "ID"
msgstr ""

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr "Gelen Faturalar Günlüğü"

#. module: l10n_tr_nilvera
#: model:ir.model,name:l10n_tr_nilvera.model_account_journal
msgid "Journal"
msgstr "Yevmiye"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_account_journal__is_nilvera_journal
msgid "Journal used for Nilvera"
msgstr "Nilvera için kullanılan dergi"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_kwt
msgid "Kilowatt"
msgstr "Kilowatt"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_kwh
msgid "Kilowatt Hour"
msgstr "Kilowatt Saat"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_partner__l10n_tr_nilvera_customer_alias_ids
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_users__l10n_tr_nilvera_customer_alias_ids
msgid "L10N Tr Nilvera Customer Alias"
msgstr "L10N Tr Nilvera Müşteri Takma Adı"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleme Tarihi"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme Tarihi"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_mwh
msgid "Megawatt Hour"
msgstr "Megawatt Saat"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_mgm
msgid "Milligram - mg"
msgstr "Miligram - mg"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_mlt
msgid "Milliliter - ml"
msgstr "Mililitre - ml"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_d61
msgid "Minute"
msgstr "Dakika"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_mon
msgid "Month"
msgstr "Ay"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__name
msgid "Name"
msgstr "İsim"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_account_journal__l10n_tr_nilvera_api_key
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_company__l10n_tr_nilvera_api_key
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_config_settings__l10n_tr_nilvera_api_key
msgid "Nilvera API key"
msgstr "Nilvera API anahtarı"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.res_config_settings_view_form
msgid "Nilvera Electronic Document Invoicing"
msgstr "Nilvera Elektronik Belge Faturalama"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_company__l10n_tr_nilvera_environment
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_config_settings__l10n_tr_nilvera_environment
msgid "Nilvera Environment"
msgstr "Nilvera Çevre"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_company__l10n_tr_nilvera_purchase_journal_id
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_config_settings__l10n_tr_nilvera_purchase_journal_id
msgid "Nilvera Purchase Journal"
msgstr "Nilvera Satın Alma Günlüğü"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_partner__l10n_tr_nilvera_customer_status
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_res_users__l10n_tr_nilvera_customer_status
msgid "Nilvera Status"
msgstr "Nilvera Durumu"

#. module: l10n_tr_nilvera
#. odoo-python
#: code:addons/l10n_tr_nilvera/models/res_config_settings.py:0
#, python-format
msgid ""
"Nilvera connection successful but the tax number on Nilvera and Odoo doesn't"
" match. Check Nilvera."
msgstr ""
"Nilvera bağlantısı başarılı ancak Nilvera ve Odoo'daki vergi numarası "
"eşleşmiyor. Nilvera'yı kontrol edin."

#. module: l10n_tr_nilvera
#. odoo-python
#: code:addons/l10n_tr_nilvera/models/res_config_settings.py:0
#, python-format
msgid "Nilvera connection successful!"
msgstr "Nilvera bağlantısı başarılı!"

#. module: l10n_tr_nilvera
#. odoo-python
#: code:addons/l10n_tr_nilvera/models/res_config_settings.py:0
#, python-format
msgid "Nilvera connection was unsuccessful, check the API key."
msgstr "Nilvera bağlantısı başarısız oldu, API anahtarını kontrol edin."

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_partner__l10n_tr_nilvera_customer_status__not_checked
msgid "Not Checked"
msgstr "Kontrol Edilmedi"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_bg
msgid "Pack"
msgstr "Poşet"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_pa
msgid "Package"
msgstr "Paket"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_pr
msgid "Pair"
msgstr "Çift"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_pf
msgid "Pallet"
msgstr "Palet"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_pk
msgid "Parcel"
msgstr "Koli"

#. module: l10n_tr_nilvera
#: model:ir.model.fields,field_description:l10n_tr_nilvera.field_l10n_tr_nilvera_alias__partner_id
msgid "Partner"
msgstr "Ortak"

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_company__l10n_tr_nilvera_environment__production
msgid "Production"
msgstr "Üretim"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_d62
msgid "Second"
msgstr "Saniye"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_set
msgid "Set"
msgstr "Set"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_cmk
msgid "Square Centimeter - cm²"
msgstr "Santimetre Kare - cm²"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_standard_cubic_meter
msgid "Standard Cubic Meter"
msgstr "Standart Metreküp"

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_company__l10n_tr_nilvera_environment__sandbox
msgid "Test"
msgstr ""

#. module: l10n_tr_nilvera
#: model:ir.model.fields.selection,name:l10n_tr_nilvera.selection__res_partner__ubl_cii_format__ubl_tr
msgid "UBL TR 1.2"
msgstr ""

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.view_partner_property_form_inherit_ubl_tr
msgid "Verify"
msgstr "Doğrulama"

#. module: l10n_tr_nilvera
#: model_terms:ir.ui.view,arch_db:l10n_tr_nilvera.view_partner_property_form_inherit_ubl_tr
msgid "Verify partner on Nilvera"
msgstr "Nilvera'daki ortağı doğrulayın"

#. module: l10n_tr_nilvera
#: model:uom.uom,name:l10n_tr_nilvera.product_uom_ann
msgid "Year"
msgstr "Yıl"
