<?xml version="1.0" encoding="utf-8"?>
<templates>
    <t t-name="spreadsheet.TextFilterValue">
        <select t-if="props.options?.length"
            t-on-change="(e) => props.onValueChanged(e.target.value)"
            class="o_input me-3"
            required="true"
        >
            <option value="">Choose a value...</option>
            <t
                t-foreach="props.options"
                t-as="option"
                t-key="option.formattedValue"
            >
                <option
                    t-att-selected="option.value === props.value"
                    t-att-value="option.value"
                    t-esc="option.formattedValue"
                />
            </t>
        </select>
        <input type="text"
            t-else=""
            class="o_input o-global-filter-text-value text-truncate"
            t-att-placeholder="translate(props.label)"
            t-att-value="props.value"
            t-on-change="(e) => props.onValueChanged(e.target.value)" />
    </t>
</templates>
