# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:02+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"Language: km\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "Google Service"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:0
#, python-format
msgid "Something went wrong during your token generation. Maybe your Authorization Code is invalid or already expired"
msgstr ""
