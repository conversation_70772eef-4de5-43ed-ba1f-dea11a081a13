# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_custom
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>doo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_transaction_status
msgid ""
"<small class=\"text-center text-wrap lh-sm\">Scan me in your banking "
"app</small>"
msgstr "<small class=\"text-center text-wrap lh-sm\">用你的銀行程式掃瞄我</small>"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_transaction_status
msgid "<strong class=\"mt-auto\">Communication: </strong>"
msgstr "<strong class=\"mt-auto\">通訊： </strong>"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__code
msgid "Code"
msgstr "程式碼"

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__code__custom
msgid "Custom"
msgstr "自訂"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "自訂模式"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__qr_code
msgid "Enable QR Codes"
msgstr "啟用QR Codes"

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "通過電匯付款時啟用QR Codes"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_transaction_status
msgid "Finalize your payment"
msgstr "最後檢查你的付款"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_transaction_status
msgid "OR"
msgstr "或"

#. module: payment_custom
#: model:ir.model.constraint,message:payment_custom.constraint_payment_provider_custom_providers_setup
msgid "Only custom providers should have a custom mode."
msgstr "只限自訂服務商才應該有自訂模式。"

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.payment_provider_form
msgid "Reload Pending Message"
msgstr "重新載入待處理訊息"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
#, python-format
msgid "The customer has selected %(provider_name)s to make the payment."
msgstr "客戶已選擇 %(provider_name)s 作付款。"

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__custom_mode__wire_transfer
msgid "Wire Transfer"
msgstr "電匯"
