# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Gut gemacht!</b> <PERSON>e haben alle Schritte dieser Tour "
"absolviert.</strong>"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr ""
"Klicken Sie auf das <i>Startsymbol</i>, um durch die Apps zu navigieren."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Close"
msgstr "Schließen"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "Verbraucht von"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Disable Tours"
msgstr "Tour deaktivieren"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Menü"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Name"
msgstr "Name"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Onboarding tours"
msgstr "Einführungstouren"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr "Fehlermenü öffnen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Path"
msgstr "Pfad"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
#, python-format
msgid "Scroll down to reach the next step."
msgstr "Nach unten scrollen, um zum nächsten Schritt zu gelangen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
#, python-format
msgid "Scroll up to reach the next step."
msgstr "Nach oben scrollen, um zum nächsten Schritt zu gelangen."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Sequence"
msgstr "Sequenz"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start"
msgstr "Start"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Start Tour"
msgstr "Tour starten"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start tour"
msgstr "Tour starten"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test tour"
msgstr "Testtour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Testing tours"
msgstr "Testtouren"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Tipp"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "Tourname"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.js:0
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
#, python-format
msgid "Tours"
msgstr "Touren"
