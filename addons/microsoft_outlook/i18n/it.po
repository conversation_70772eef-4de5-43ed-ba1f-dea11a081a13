# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 10:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connetti il tuo account Outlook"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
msgid ""
"<span invisible=\"server_type != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"server_type != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Token Outlook valido\n"
"                    </span>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span invisible=\"smtp_authentication != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"smtp_authentication != 'outlook' or not microsoft_outlook_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Token Outlook valido\n"
"                    </span>"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr "Si è verificato un errore durante il recupero dell' Access Token. %s"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "Autentica con"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr "URI di autenticazione"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Outlook account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"Collega il tuo account Outlook tramite il processo di autenticazione OAuth.  \n"
"Per impostazione predefinita, solo un utente con un indirizzo e-mail adeguato potrà utilizzare il server. Per estenderne l'utilizzo, puoi configurare un parametro di sistema di tipo \"mail.default.from\"."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your personal Outlook account using OAuth. \n"
"You will be redirected to the Outlook login page to accept the permissions."
msgstr ""
"Collega il tuo account personale Outlook utilizzando OAuth.\n"
"Verrai reindirizzato alla pagina di accesso di Outlook per accettare i permessi."

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr "Torna al tuo mail server"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID"
msgstr "ID"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID of your Outlook app"
msgstr "ID della tua app Outlook"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Server per e-mail in arrivo"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"Sicurezza di connessione non corretta per il server di posta di Outlook %r. "
"Impostala su \"TLS (STARTTLS)\"."

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr "Sono configurate le credenziali Outlook?"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr "Server di posta"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr "Microsoft Outlook Mixin"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr "Solo un amministratore può collegare un mail server di Outlook."

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr "Outlook Access Token"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr "Timestamp di scadenza del token di accesso di Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "Outlook ID client"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "Outlook segreto client"

#. module: microsoft_outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__fetchmail_server__server_type__outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__ir_mail_server__smtp_authentication__outlook
msgid "Outlook OAuth Authentication"
msgstr "Autenticazione OAuth Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr "Token di aggiornamento Outlook"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr "Configura le credenziali Outlook, per favore."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please connect with your Outlook account before using it."
msgstr "Prima di utilizzarlo accedi al tuo account Outlook."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Outlook/Office365 username "
"(your email address). This should be the same account as the one used for "
"the Outlook OAuthentication Token."
msgstr ""
"Completa il campo \"Nome utente\" con il nome utente Outlook/Office 365 (il "
"tuo indirizzo e-mail). Questo dovrebbe essere lo stesso account utilizzato "
"per il token di OAutenticazione Outlook."

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""
"Lascia il campo della password vuoto per il mail server di Outlook %r. Il "
"processo OAuth non la richiede"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Read More"
msgstr "Leggi altro"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required for the server %r."
msgstr "Per il server %r è richiesto il protocollo SSL."

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret"
msgstr "Secret"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret of your Outlook app"
msgstr "Segreto della tua app Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Tipo server"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""
"Per collegare un account impostare le credenziali API di Outlook nelle "
"impostazioni generali."

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr "L'URL per generare il codice di autorizzazione da Outlook"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr "Errore sconosciuto."
