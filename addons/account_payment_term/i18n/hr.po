# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_term
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-13 08:17+0000\n"
"PO-Revision-Date: 2024-06-22 22:00+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_payment_term
#: model:account.payment.term,name:account_payment_term.account_payment_term_advance
msgid "90 days, on the 10th"
msgstr "90 dana, na 10 dan "

#. module: account_payment_term
#: model:ir.model.fields.selection,name:account_payment_term.selection__account_payment_term_line__delay_type__days_end_of_month_on_the
msgid "Days end of month on the"
msgstr ""

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__days_next_month
msgid "Days on the next month"
msgstr ""

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__delay_type
msgid "Delay Type"
msgstr "Tip kašnjenja"

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__display_days_next_month
msgid "Display Days Next Month"
msgstr ""

#. module: account_payment_term
#: model:ir.model,name:account_payment_term.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Redak uvjeta plaćanja"

#. module: account_payment_term
#: model_terms:account.payment.term,note:account_payment_term.account_payment_term_advance
msgid "Payment terms: 90 days, on the 10th"
msgstr ""

#. module: account_payment_term
#. odoo-python
#: code:addons/account_payment_term/models/account_payment_term.py:0
#, python-format
msgid "The days added must be a number and has to be between 0 and 31."
msgstr ""

#. module: account_payment_term
#. odoo-python
#: code:addons/account_payment_term/models/account_payment_term.py:0
#, python-format
msgid "The days added must be between 0 and 31."
msgstr ""
