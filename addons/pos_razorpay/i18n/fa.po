# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_razorpay
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>a <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2024-08-17 22:00+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__all
msgid "All"
msgstr "همه"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__bharatqr
msgid "BHARATQR"
msgstr ""

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/razorpay_pos_request.py:0
#, python-format
msgid "Cannot decode Razorpay POS response"
msgstr ""

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__card
msgid "Card"
msgstr "کارت"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid ""
"Choose allow payment mode: \n"
" All/Card/UPI or QR"
msgstr ""

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid ""
"Device Serial No \n"
" ex: 7000012300"
msgstr ""

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid ""
"Payment has been queued. You may choose to wait for the payment to initiate "
"on terminal or proceed to cancel this transaction"
msgstr ""

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارشات پایانه فروش"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "روش های پرداخت پایانه فروش"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment
msgid "Point of Sale Payments"
msgstr "پرداخت های پایانه فروش"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid "Razorpay API Key"
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_authcode
msgid "Razorpay APPR Code"
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid "Razorpay Allowed Payment Modes"
msgstr "حالت‌های پرداخت مجاز Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_card_owner_name
msgid "Razorpay Card Owner Name"
msgstr "نام صاحب کارت Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_card_scheme
msgid "Razorpay Card Scheme"
msgstr "طرح کارت Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid "Razorpay Device Serial No"
msgstr "شماره سریال دستگاه Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid "Razorpay Error"
msgstr "خطای Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_issuer_card_no
msgid "Razorpay Issue Card No Last 4 digits"
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_issuer_bank
msgid "Razorpay Issuer Bank"
msgstr "بانک صادرکننده Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_reference_no
msgid "Razorpay Merchant Reference No."
msgstr "شماره مرجع تاجر Razorpay"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid ""
"Razorpay POS payment cancel request expected errorCode not found in the "
"response"
msgstr "درخواست لغو پرداخت Razorpay POS خطای مورد انتظار کد در پاسخ یافت نشد"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid ""
"Razorpay POS payment request expected errorCode not found in the response"
msgstr "درخواست پرداخت Razorpay POS خطای مورد انتظار کد در پاسخ یافت نشد"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid ""
"Razorpay POS payment status request expected errorCode not found in the "
"response"
msgstr ""
"درخواست وضعیت پرداخت Razorpay POS خطای مورد انتظار کد در پاسخ یافت نشد"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid "Razorpay POS transaction canceled successfully"
msgstr "راکنش Razorpay POS با موفقیت لغو شد"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid "Razorpay POS transaction failed"
msgstr "تراکنش Razorpay POS ناموفق بود"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_payment_method
msgid "Razorpay Payment Method"
msgstr "روش پرداخت Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_reverse_ref_no
msgid "Razorpay Reverse Reference No."
msgstr "شماره مرجع معکوس Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Razorpay Test Mode"
msgstr "حالت تست Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_username
msgid "Razorpay Username"
msgstr "نام کاربری Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid "Reference number mismatched"
msgstr "شماره مرجع مطابقت ندارد"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
#, python-format
msgid "This Payment Terminal is only valid for INR Currency"
msgstr "این پایانه پرداخت فقط برای ارز INR معتبر است"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
#, python-format
msgid "Transaction failed due to inactivity"
msgstr "تراکنش به دلیل عدم فعالیت انجام نشد"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Turn it on when in Test Mode"
msgstr "در حالت تست آن را روشن کنید"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__upi
msgid "UPI"
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid ""
"Used when connecting to Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"
msgstr ""

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_username
msgid ""
"Username(Device Login) \n"
" ex: **********"
msgstr ""
