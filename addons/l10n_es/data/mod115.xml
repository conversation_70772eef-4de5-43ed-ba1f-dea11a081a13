<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data auto_sequence="1">
    <record id="mod_115" model="account.report">
        <field name="name">Tax Report(Mod 115)</field>
        <field name="sequence">115</field>
        <field name="filter_analytic" eval="False"/>
        <field name="filter_date_range" eval="True"/>
        <field name="filter_period_comparison" eval="False"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.es"/>
        <field name="filter_multi_company">tax_units</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="column_ids">
            <record id="mod_115_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="mod_115_title_1" model="account.report.line">
                <field name="name">Withholdings and payments on account</field>
                <field name="code">aeat_mod_115_title_1</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_115_casilla_01" model="account.report.line">
                        <field name="name">[01] Nº of recipients</field>
                        <field name="code">aeat_mod_115_01</field>
                        <field name="groupby">partner_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_115_casilla_01_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="['|', '|', '|', ('tax_tag_ids', '=', '+mod115[02]'), ('tax_tag_ids', '=', '+mod115[03]'), ('tax_tag_ids', '=', '-mod115[02]'), ('tax_tag_ids', '=', '-mod115[03]')]"/>
                                <field name="subformula">count_rows</field>
                                <field name="figure_type">integer</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_115_casilla_02" model="account.report.line">
                        <field name="name">[02] Base withholdings</field>
                        <field name="code">aeat_mod_115_02</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_115_casilla_02_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod115[02]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_115_casilla_03" model="account.report.line">
                        <field name="name">[03] Withholdings</field>
                        <field name="code">aeat_mod_115_03</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_115_casilla_03_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod115[03]</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="mod_115_title_2" model="account.report.line">
                <field name="name">Total liquidation</field>
                <field name="code">aeat_mod_115_title_2</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_115_casilla_04" model="account.report.line">
                        <field name="name">[04] Previous results to report</field>
                        <field name="code">aeat_mod_115_04</field>
                        <field name="expression_ids">
                            <record id="mod_115_casilla_04_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_115_casilla_05" model="account.report.line">
                        <field name="name">[05] Result to report</field>
                        <field name="code">aeat_mod_115_05</field>
                        <field name="aggregation_formula">aeat_mod_115_03.balance - aeat_mod_115_04.balance</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</data>
</odoo>
