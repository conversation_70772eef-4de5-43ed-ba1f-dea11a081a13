# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_account
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid " days"
msgstr " Tage"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "# days"
msgstr "# Tage"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Amount"
msgstr "Betrag"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Average Invoice"
msgstr "Durchschnittliche Rechnung"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "COGS"
msgstr "HKU"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Country"
msgstr "Land"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Current year"
msgstr "Aktuelles Jahr"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "DSO"
msgstr "Außenstandsdauer"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Date"
msgstr "Datum"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Income"
msgstr "Einkommen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoiced"
msgstr "Abgerechnet"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoiced by Month"
msgstr "Abgerechnet pro Monat"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoices"
msgstr "Rechnungen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoices by Total Signed"
msgstr "Rechnungen nach unterzeichneter Summe"

#. module: spreadsheet_dashboard_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account.dashboard_invoicing
msgid "Invoicing"
msgstr "Abrechnung"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Average Invoice"
msgstr "KPI - Durchschnittliche Rechnung"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - DSO"
msgstr "KPI - Außenstandsdauer"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Income"
msgstr "KPI - Einkommen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Invoice Count"
msgstr "KPI - Anzahl Rechnungen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Unpaid Invoices"
msgstr "KPI - Unbezahlte Rechnungen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Zeitraum"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Product Category"
msgstr "Produktkategorie"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Ratio"
msgstr "Faktor"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Receivable"
msgstr "Debitoren"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Reference"
msgstr "Referenz"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr "Umsatz"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Salesperson"
msgstr "Vertriebsmitarbeiter"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Status"
msgstr "Status"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Top-Kategorien"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Countries"
msgstr "Top-Länder"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Invoices"
msgstr "Top-Rechnungen"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr "Top-Produkte"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Salespeople"
msgstr "Top-Vertriebsmitarbeiter"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "in current year"
msgstr "im aktuellen Jahr"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "unpaid"
msgstr "unbezahlt"
