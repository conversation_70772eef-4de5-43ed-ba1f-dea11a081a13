# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <i<PERSON><PERSON>.<PERSON><PERSON><PERSON>@storm.hr>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <durdica.z<PERSON><PERSON><PERSON>@storm.hr>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# hrvo<PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Naziv kata: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Aktivan"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add Floor"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add a new floor to get started."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Dodaj novi kat restorana"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Add a new table to get started."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Add a tip"
msgstr "Dodaj napojnicu"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/orderline_note_button/orderline_note_button.js:0
#, python-format
msgid "Add internal Note"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Adjust Amount"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
msgid "Allow custom Internal notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "Interna oznaka stola"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Izgled"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Arhivirano"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Are you sure?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Back"
msgstr "Natrag"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Boja pozadine"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Pozadinska slika"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Bacon Burger"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Bank"
msgstr "Banka"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Bar"
msgstr "Bar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/print_bill_button/print_bill_button.xml:0
#, python-format
msgid "Bill"
msgstr "Račun"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Ispis računa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Dijeljenje računa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "Blocked action"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Blue"
msgstr "Plava"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/product_screen.xml:0
#, python-format
msgid "Book table"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Restaurant"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/back_button/back_button.xml:0
#, python-format
msgid "Change table"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Cheese Burger"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Close Tab"
msgstr "Zatvori karticu"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Boja"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracija postavki"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Copy"
msgstr "Kopija"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Delete"
msgstr "Obriši"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Delete Error"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.xml:0
#, python-format
msgid "Dine-in Guests"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Pića"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Edit Plan"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "Fanta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Fill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Nivo"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Naziv nivoa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Floor Name ?"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Planovi nivoa"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Floor name"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr ""

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Hrana"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Green"
msgstr "Zeleno"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Grey"
msgstr "Sivo"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "Gosti"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "Guests:"
msgstr "Gosti:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "Guests?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Visina"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Vodoravni položaj"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Ako je isključeno, stol je deaktiviran i neće biti dostupan u POS "
"aplikaciji."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/orderline_note_button/orderline_note_button.xml:0
#, python-format
msgid "Internal Note"
msgstr "Interna bilješka"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Internal Notes"
msgstr "Interne bilješke"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Je li bar / restoran"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Keep Open"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Light grey"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Loading"
msgstr "Učitavanje"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "New Floor"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "No Tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Number of Seats?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#, python-format
msgid "Ok"
msgstr "U redu"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Oops! No floors available."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "Oops! No tables available."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Open"
msgstr "Otvori"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Orange"
msgstr "Narančasta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Order"
msgstr "Narudžba"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Pay"
msgstr "Plati"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Payment"
msgstr "Plaćanje"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Postavke prodajnog mjesta"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Stavke narudžbe na prodajnom mjestu"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Nalozi POS-a"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Plaćanja na prodajnom mjestu"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "Smjena POS-a"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "Prodajna mjesta"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#, python-format
msgid "Print"
msgstr "Ispis"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Purple"
msgstr "Ljubičasta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Red"
msgstr "Crveno"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Rename"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Nivo restorana"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Nivoi restorana"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Stol restorana"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "Obrnuto"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Obrni Plaćanje"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Okrugli"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Round Shape"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Sjedala"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Settle"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
#, python-format
msgid "Shape"
msgstr "Oblik"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Signature"
msgstr "Potpis"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/split_bill_button/split_bill_button.xml:0
#, python-format
msgid "Split"
msgstr "Razdijeli"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Četvrtast"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Square Shape"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "Podzbroj"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Switch Floor View"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "Table"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Naziv stola"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Table Name?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Table is not empty"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Tablice"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid ""
"The table already contains an order. Do you want to proceed and transfer the"
" order here?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Tip"
msgstr "Savjet"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Tip Amount"
msgstr "Iznos napojnice"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Tip:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Tipping"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "Total:"
msgstr "Ukupno:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/transfer_order_button/transfer_order_button.xml:0
#, python-format
msgid "Transfer"
msgstr "Prijenos"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Turquoise"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#, python-format
msgid "Username"
msgstr "Korisničko ime"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Vertikalni položaj"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Voda"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "White"
msgstr "Bijela"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Širina"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Yellow"
msgstr "Žuta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Yes"
msgstr "Da"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "You cannot delete a table with orders still in draft for this table."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/table_guests_button/table_guests_button.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#, python-format
msgid "at table"
msgstr "za stolom"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "changes"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "stavke"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
#, python-format
msgid "or book the table for later"
msgstr ""
