<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_employee_print_badge" model="ir.actions.report">
        <field name="name">Print Badge</field>
        <field name="model">hr.employee</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hr.print_employee_badge</field>
        <field name="report_file">hr.print_employee_badge</field>
        <field name="print_report_name">'Badge - %s' % (object.name).replace('/', '')</field>
        <field name="binding_model_id" ref="model_hr_employee"/>
        <field name="binding_type">report</field>
    </record>

    <template id="print_employee_badge">
        <t t-call="web.basic_layout">
            <div class="page">
                <div class="oe_structure"></div>
                <t t-foreach="docs" t-as="employee">
                    <div class="col-md-6">
                        <div class="oe_structure"></div>
                        <table style="width:243pt; height:153pt; border: 1pt solid black; border-collapse:separate; border-radius:8pt; margin:5pt">
                            <td style="width:33%;" valign="center">
                                <table style="width:77pt; height:150pt" class="table-borderless">
                                    <tr style="height:30%">
                                        <td align="center" valign="center">
                                            <span t-if="employee.company_id.logo">
                                                <img t-att-src="image_data_uri(employee.company_id.logo)" style="max-height:45pt;max-width:90%" alt="Company Logo"/>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr style="height:70%;">
                                        <td align="center" valign="center">
                                            <img t-att-src="image_data_uri(employee.avatar_1920)" style="max-height:85pt;max-width:90%" alt="Employee Image"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td style="width:67%" valign="center">
                                <table style="width:155pt; height:85pt" class="table-borderless">
                                    <tr><th><div style="font-size:15pt; margin-bottom:0pt;margin-top:0pt;" align="center"><span t-out="employee.name" data-oe-demo="Marc Demo"/></div></th></tr>
                                    <tr><td><div align="center" style="font-size:10pt;margin-bottom:5pt;"><span t-out="employee.job_id.name" data-oe-demo="Software Developer"/></div></td></tr>
                                    <tr><td><div t-if="employee.barcode" t-field="employee.barcode" t-options="{'widget': 'barcode', 'width': 600, 'height': 120, 'img_style': 'max-height:50pt;max-width:100%;', 'img_align': 'center'}" data-oe-demo="12345678901"/></td></tr>
                                </table>
                            </td>
                        </table>
                        <div class="oe_structure"></div>
                    </div>
                </t>
                <div class="oe_structure"></div>
            </div>
        </t>
    </template>
</odoo>
