# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_delivery
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# KeyVillage, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr " (Очаквана стойност: %s )"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>Превозвач:</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Тегло:</strong>\n"
"                <br/>"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_route__shipping_selectable
msgid "Applicable on Shipping Methods"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Насипно тегло"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Отказ"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier"
msgstr "Превозвач"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__company_id
msgid "Company"
msgstr "Фирма"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Cost: %(price).2f %(currency)s"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Помощник за избор на превозвач"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Помощник за избор на пакет за доставка"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Дестинация"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Отхвърлете"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__display_name
msgid "Display Name"
msgstr "Име за Показване"

#. module: stock_delivery
#: model:ir.actions.act_window,name:stock_delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Покажи линкове за проследяване"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Приблизителна цена: на клиента ще бъде фактурирана приблизителната цена на доставката.\n"
"Реална цена: на клиента ще бъде фактурирана реалната цена на доставката, цената на доставката ще бъде актуализирана в Поръчката за продажба след доставката."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Exception occurred with respect to carrier on the transfer"
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Exception:"
msgstr "Изключение:"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.product_template_hs_code
msgid "HS Code"
msgstr "HS код"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__id
msgid "ID"
msgstr "ID"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_route
msgid "Inventory Routes"
msgstr "Маршрути на инвентар"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Политика по фактуриране"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "Е пикинг за връщане"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Manual actions might be needed."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Няма интеграция на превозвач"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Пакет"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "Подробности за опаковката"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "Пакетът е прекалено тежък!"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "Пакети"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Пикинг"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Принтирай еткет за връщане"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_product_template
msgid "Product"
msgstr "Продукт"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__delivery_type
msgid "Provider"
msgstr "Доставчик"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Фактиески разходи"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Еткет за връщане"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Пикинг Рекламации"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__route_ids
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_delivery_carrier_form_inherit_stock_delivery
msgid "Routes"
msgstr "Маршрути"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:stock_delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Продажна цена"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order
msgid "Sales Order"
msgstr "Поръчка"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ред на поръчка за продажби"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Запазете"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Изпратете на износителя"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Цена за шипинг"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Информация за шипинг"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:stock_delivery.menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_location_route_view_form_inherit_stock_delivery
msgid "Shipping Methods"
msgstr "Начини на доставка"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Тегло на пратката"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,help:stock_delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for FedEx and USPS shipping providers."
msgstr ""

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move
msgid "Stock Move"
msgstr "Движение в склада"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"The ISO country code in two chars. \n"
"Можете да използвате това поле за бързо търсене."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr "Цената за доставката ще бъде посочена щом доставката бъде изпълнена."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"Теглото на пакета е по-голямо от позволения максимум за този тип пакети. "
"Моля изберете друг тип пакет."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "Няма подходящо правило за доставка."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Общо тегло на всички продукти, съдържащи се в пакета."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Общо тегло на пакета"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Общо тегло на продукти в пикинга."

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "URL за проследяване"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Проследяване"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Референция за проследяване"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "Проследяващ URL адрес"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Tracking links for shipment:"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
msgid "Tracking:"
msgstr "Проследяване:"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_picking
msgid "Transfer"
msgstr "Прехвърлете"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Тегло"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Тегло за шипинг"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Тегло за доставка"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Етикет на мерната единица за тегло"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr "Имате няколко линка за проследяване, те са налични в долния панел."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Вашият методът на доставка не пренасочва към уебсайта на куриера, за да се "
"проследи тази пратка."

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Префикс на пощенски код"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FS"
msgstr ""
