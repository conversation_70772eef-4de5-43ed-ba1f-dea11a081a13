# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx (Original)"
msgstr "%spx (original)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx (Suggested)"
msgstr "%spx (sugerido)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""
"La 'etiqueta Alt' especifica un texto alternativo para una imagen, si la "
"imagen no se puede mostrar (conexión lenta, imagen faltante, lector de "
"pantalla ...)."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""
"La 'etiqueta de título' se muestra como información sobre herramientas "
"cuando coloca el cursor sobre la imagen."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(Etiqueta ALT)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(Etiqueta TÍTULO)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(URL o incrustación)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100 %"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr "1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "2 columns"
msgstr "2 columnas"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25 %"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr "2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "3 Stars"
msgstr "3 estrellas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "3 columns"
msgstr "3 columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr "3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "4 columns"
msgstr "4 columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr "4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "5 Stars"
msgstr "5 estrellas"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50 %"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">deg</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">grados</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2 ms-3\">Y</span>"
msgstr "<span class=\"me-2 ms-3\">Y</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2\">X</span>"
msgstr "<span class=\"me-2\">X</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Basic</span>"
msgstr "<span class=\"w-100\">Básico</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Creative</span>"
msgstr "<span class=\"w-100\">Creativo</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Decorative</span>"
msgstr "<span class=\"w-100\">Decorativo</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Devices</span>"
msgstr "<span class=\"w-100\">Dispositivos</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Linear</span>"
msgstr "<span class=\"w-100\">Lineal</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr "<span>Bloques</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Customize</span>"
msgstr "<span>Personalizar</span>"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "ACCESS OPTIONS ANYWAY"
msgstr "IR A LAS OPCIONES DE TODAS FORMAS"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "AI Tools"
msgstr "Herramientas de IA"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr "Encima"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Accepts"
msgstr "Acepta"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr "Añadir columna"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "Añadir fila"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Add URL"
msgstr "Añadir URL"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section"
msgstr "Añadir una sección de bloque de cita"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button"
msgstr "Añadir un botón"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section"
msgstr "Añadir una sección de código"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link"
msgstr "Añadir un enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add an emoji"
msgstr "Añadir un emoji"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "Fino &amp; en zigzag"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy &amp; Zigs"
msgstr "Fino &amp; en zigzag"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "Alert"
msgstr "Alerta"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "Alinear al centro"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "Alinear a la izquierda"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "Alinear a la derecha"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "Alineación"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "All"
msgstr "Todos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "All documents have been loaded"
msgstr "Todos los documentos han sido cargados"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "All images have been loaded"
msgstr "Todas las imágenes han sido cargadas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Allow users to view and edit the field in HTML."
msgstr "Permita que los usuarios vean y editen el campo en HTML."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "Etiqueta Alt"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "An error occurred while fetching the entered URL."
msgstr "Ocurrió un error al recuperar la URL introducida. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "Ángulo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr "Animado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Anonymous"
msgstr "Anónimo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet %s?"
msgstr "¿Está seguro de que desea eliminar este sinppet %s?"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "Are you sure you want to delete this file?"
msgstr "¿Está seguro de que desea elimianr este archivo? "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "Relación de aspecto"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "Utilidades de activos"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "URL del archivo adjunto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr "Convertir automáticamente a enlace relativo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Autoplay"
msgstr "Reproducción automática"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Back to one column"
msgstr "Volver a una columna"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "Fondo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "Color de fondo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "Posición del fondo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Shapes"
msgstr "Formas del fondo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Banner Danger"
msgstr "Banner de peligro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Banner Info"
msgstr "Banner de información "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Banner Success"
msgstr "Banner de éxito "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Banner Warning"
msgstr "Banner de advertencia "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Banners"
msgstr "Banners"

#. module: web_editor
#: model:ir.model,name:web_editor.model_base
msgid "Base"
msgstr "Base"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Basic blocks"
msgstr "Bloques básicos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr "Debajo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading"
msgstr "Título de sección grande"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "Manchas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "Bloque"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Block &amp; Rainy"
msgstr "Bloque y lluvioso"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "Bloques y lluvioso"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "Desenfocar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "Negrita"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border"
msgstr "Borde"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr "Color del borde"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr "Estilo de borde"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr "Ancho del borde"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "Brannan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "Brillo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Brushed"
msgstr "Cepillado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr "Lista de viñetas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "Botón"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "CSS Edit"
msgstr "Editar CSS"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "Centro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "ChatGPT"
msgstr "ChatGPT"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr "Lista de verificación"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr "Elija un registro..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "Código"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Codeview"
msgstr "Codeview"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Collaborative edition"
msgstr "Edición colaborativa"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Collaborative trigger"
msgstr "Activador colaborativo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "Color"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Color filter"
msgstr "Filtro de color"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Colors"
msgstr "Colores"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr "Columna"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "Colores comunes"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.xml:0
#, python-format
msgid "Comparison"
msgstr "Comparación"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composites"
msgstr "Compuestos"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composition"
msgstr "Composición "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.xml:0
#, python-format
msgid "Content"
msgstr "Contenido"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content generated"
msgstr "Contenido generado"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "Contraste"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 2 columns"
msgstr "Convertir en 2 columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 3 columns"
msgstr "Convertir en 3 columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Convert into 4 columns"
msgstr "Convertir en 4 columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "Copie y pegue su URL o incruste su código aquí"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module %s"
msgstr "No se pudo instalar el módulo %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_service.js:0
#, python-format
msgid "Could not load the file \"%s\"."
msgstr "No fue posible subir el archivo %s. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "Portada"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "Crear"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering"
msgstr "Crear una lista numerada"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list"
msgstr "Crear una lista de viñetas sencilla"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr "Crear una URL"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "Creado el"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr "Recortar imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "Personalizado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr "Personalizado %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom Button"
msgstr "Botón personalizado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr "Rayado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Default"
msgstr "Por defecto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr "Por defecto + Redondeado"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "Defina un gradiente personalizado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr "Eliminar %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "Descripción"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Devices"
msgstr "Dispositivos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "Descartar registro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid ""
"Discover a world of awesomeness in our copyright-free image haven. No legal "
"drama, just nice images!"
msgstr ""
"Descubra un mundo increíble en su paraíso de imágenes libres de derechos de "
"autor. ¡Sin dramas legales! Solo imágenes lindas."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Display 1"
msgstr "Pantalla 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Display 2"
msgstr "Pantalla 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Display 3"
msgstr "Pantalla 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Display 4"
msgstr "Pantalla 4"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install %s App?"
msgstr "¿Desea instalar la aplicación %s?"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Documents"
msgstr "Documentos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr "Punteado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr "Doble"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr "Haga doble clic para editar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Drag and drop the building block."
msgstr "Arrastre y suelte el bloque de creación."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "Duplicar contenedor"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "Colores dinámicos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Dynamic Placeholder"
msgstr "Marcador de posición dinámico"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr ""
"ERROR: No se pudieron obtener URLs de descarga de la biblioteca de medios."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "EarlyBird"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "Editar imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr "Editar descripción del archivo multimedia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr "Incrustar Imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr "Incrustar vídeo de Youtube"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr "Incruste la imagen en el documento."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr "Incruste el vídeo de YouTube en el documento."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Emoji"
msgstr "Emoji"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr "Cita vacía"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "Extender a la esquina más cercana"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "Extender al lado más cercano"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "Extender a la esquina más lejana"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "Extender al lado más lejano"

#. module: web_editor
#: model:ir.model,name:web_editor.model_html_field_history_mixin
msgid "Field html History"
msgstr "Historial del campo html"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
#, python-format
msgid "File has been uploaded"
msgstr "Se subió el archivo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "Rellenar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr "Rellenar + Redondeado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill Color"
msgstr "Color de relleno"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Filter"
msgstr "Filtro"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "Primer panel"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "Plano"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
#, python-format
msgid "Flexible"
msgstr "Flexible"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Flip"
msgstr "Girar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "Girar horizontalmente"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "Girar verticalmente"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr "Formas flotantes"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Focus"
msgstr "Enfocar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr "Color de la fuente"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr "Tamaño de fuente"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr "Por razones técnicas, no puede soltar este bloque aquí"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Format"
msgstr "Formatear"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Full screen"
msgstr "Pantalla completa"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "Pantalla completa"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Generate or transform content with AI"
msgstr "Generar o transformar contenido con IA"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Generate or transform content with AI."
msgstr "Genere o transforme contenido con IA."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#, python-format
msgid "Generating"
msgstr "Generando"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#, python-format
msgid "Generating an alternative..."
msgstr "Generando una alternativa..."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics"
msgstr "Geometría"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Panels"
msgstr "Paneles geométricos"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Rounded"
msgstr "Geometría redondeada "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr "Gradiente"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr "Encabezado 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1 Display 1"
msgstr "Encabezado 1 Pantalla 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1 Display 2"
msgstr "Encabezado 1 Pantalla 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1 Display 3"
msgstr "Encabezado 1 Pantalla 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1 Display 4"
msgstr "Encabezado 1 Pantalla 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr "Encabezado 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr "Encabezado 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr "Encabezado 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr "Encabezado 5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr "Encabezado 6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 1"
msgstr "Titular 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 2"
msgstr "Titular 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 3"
msgstr "Titular 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 4"
msgstr "Titular 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 5"
msgstr "Titular 5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Heading 6"
msgstr "Titular 6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Height"
msgstr "Altura"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr "Ocultar logotipo de Dailymotion"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide fullscreen button"
msgstr "Ocultar botón de pantalla completa"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide player controls"
msgstr "Ocultar controles de reproductor"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Hide sharing button"
msgstr "Ocultar botón compartir"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.js:0
#, python-format
msgid "History"
msgstr "Historial"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history
msgid "History data"
msgstr "Datos del historial"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history_metadata
msgid "History metadata"
msgstr "Metadatos del historial"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Horizontal mirror"
msgstr "Espejo horizontal"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "Icono"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr "Formato de icono"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr "Tamaño de icono 1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr "Tamaño de icono 2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr "Tamaño de icono 3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr "Tamaño de icono 4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr "Tamaño de icono 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Icons"
msgstr "Iconos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Si descarta las ediciones actuales, todos los cambios no guardados se "
"perderán. Puede cancelar para volver al modo de edición."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Illustrations"
msgstr "Ilustraciones"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "Imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr "Formato de imagen"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "Altura de imagen"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "Fuente de la imagen"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "Ancho de la imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr "Relleno de imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Images"
msgstr "Imagenes"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Inkwell"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr "Texto Inline"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Insert"
msgstr "Insertar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Insert a Link / Button"
msgstr "Insertar un enlace/botón "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a danger banner"
msgstr "Insertar un banner de peligro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a horizontal rule separator"
msgstr "Insertar un separador de regla horizontal"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a rating over 3 stars"
msgstr "Insertar una calificación mayor que 3 estrellas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a rating over 5 stars"
msgstr "Insertar una calificación mayor que 5 estrellas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a success banner"
msgstr "Insertar un banner de éxito"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table"
msgstr "Insertar una tabla"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video"
msgstr "Insertar un vídeo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a warning banner"
msgstr "Insertar un banner de advertencia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert above"
msgstr "Insertar arriba"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image"
msgstr "Insertar una imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an info banner"
msgstr "Insertar un banner de información"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert below"
msgstr "Insertar abajo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert left"
msgstr "Insertar a la izquierda"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr "Insertar archivo multimedia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr "Insertar o editar enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Insert personalized content"
msgstr "Insertar contenido personalizado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert right"
msgstr "Insertar a la derecha"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert your signature"
msgstr "Insertar su firma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "Instalar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr "Instalar %s"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr "Valor de campo no válido para %s: %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr "Elementos invisibles"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr "Elemento"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Label"
msgstr "Descripción"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "Grande"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "Izquierda"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Light"
msgstr "Ligero"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "Lineal"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "Enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "Etiqueta del enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Shape"
msgstr "Forma del enlace "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Size"
msgstr "Tamaño del enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Style"
msgstr "Estilo del enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr "Enlace copiado al portapapeles."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "List"
msgstr "Lista"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Load more..."
msgstr "Carga más..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Loop"
msgstr "Bucle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "Color principal"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Marketing Tools"
msgstr "Herramientas de marketing"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Max height"
msgstr "Altura máxima"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Media"
msgstr "Medios"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "Medio"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading"
msgstr "Título de sección mediano"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Min height"
msgstr "Altura mínima"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr "Más información sobre esta aplicación."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move down"
msgstr "Mover hacia abajo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move left"
msgstr "Mover hacia la izquierda"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move right"
msgstr "Mover a la derecha"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Move up"
msgstr "Mover hacia arriba"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "My Images"
msgstr "Mis imágenes"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "Nombre"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "Navegación"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr "URL no especificada"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
#, python-format
msgid "No documents found."
msgstr "No se encontraron documentos."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.xml:0
#, python-format
msgid "No history"
msgstr "Sin historial"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "No images found."
msgstr "No se encontraron imágenes."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr "No hay ubicación donde soltar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr "No hay más registros"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
#, python-format
msgid "No pictograms found."
msgstr "No se encontraron pictogramas."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "Normal"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr "Lista numerada"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.tests
msgid "Odoo Editor Tests"
msgstr "Pruebas de editor de Odoo"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Oops, it looks like our AI is unreachable!"
msgstr "¡Uy! parece ser que no podemos acceder a nuestra IA"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Open in new window"
msgstr "Abrir en una ventana nueva"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Optimized"
msgstr "Optimizado"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Archivo adjunto original (no optimizado, sin tamaño ajustado)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "Orígenes"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "Contorno"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr "Contorno + Redondeado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Overlay"
msgstr "Superponer"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "Relleno"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Page Options"
msgstr "Opciones de página"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Panels"
msgstr "Paneles"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block"
msgstr "Bloque de párrafo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr "Pegar como URL"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "Patrones"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Position"
msgstr "Posición"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "Vista previa"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr "Primario"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
#, python-format
msgid "Progress bar"
msgstr "Barra de progreso"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Qu'il n'est pas arrivé à Toronto"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Qu'il était supposé arriver à Toronto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "Calidad"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "Cita"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Campo Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Campo Qweb Contacto"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Campo Qweb Fecha"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Campo Qweb Fecha-Hora"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Campo Qweb Duración"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Campo Qweb Flotante"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Campo Qweb HTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Campo Qweb Imagen"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Campo Qweb entero"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Campo Qweb Many to One"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Campo Qweb Monetario"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Campo Qweb Relativo"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Campo Qweb Selección"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Campo Qweb Texto"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Campo Qweb de Qweb"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "REPLACE BY NEW VERSION"
msgstr "REEMPLAZAR POR NUEVA VERSIÓN"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "Radial"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr "Campo de sólo lectura"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr ""
"Redireccione al usuario a otro lado al hacer clic en el archivo multimedia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr "Borrar (ELIMINAR)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "Eliminar bloque"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr "Eliminar actual"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "Eliminar color seleccionado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Remove columns"
msgstr "Eliminar columnas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr "Eliminar formato"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr "Eliminar enlace"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr "Renombrar %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "Repetir patrón"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "Reemplazar "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr "Reemplazar medios"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "Restablecer"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "Restablecer imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Reset Size"
msgstr "Reestablecer tamaño"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "Restablecer recorte"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "Restablecer transformación"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Resizable"
msgstr "Redimensionable"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Default"
msgstr "Redimensionar a tamaño por defecto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr "Redimensionar a tamaño completo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr "Redimensionar a tamaño medio"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr "Redimensionar a un cuarto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/history_dialog/history_dialog.xml:0
#, python-format
msgid "Restore history"
msgstr "Reestablecer historial"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "Derecha"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "Rotar a la izquierda"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "Rotar a la derecha"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate left"
msgstr "Rotar a la izquierda"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate right"
msgstr "Rotar a la derecha"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr "Fila"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Sandboxed preview"
msgstr "Vista previa en entorno de pruebas"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "Saturación"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr "Guardar e instalar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr "Guardar y actualizar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "Guardar registro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "Search a document"
msgstr "Buscar un documento"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.js:0
#, python-format
msgid "Search a pictogram"
msgstr "Buscar un pictograma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Search an image"
msgstr "Buscar una imagen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search for a block (e.g. numbers, image wall, ...)"
msgstr "Buscar un bloque (por ejemplo, números, muro de imágenes, ...)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr "Buscar registros..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr "Buscar más..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr "Buscar más registros"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr "Secundario"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr "Seleccione un bloque en su página para darle estilo."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Select a media"
msgstr "Seleccionar un medio"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#, python-format
msgid "Send a message"
msgstr "Enviar un mensaje"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Separator"
msgstr "Separador"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "Sepia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr "Sombra"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Shape"
msgstr "Forma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr "Forma: Círculo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr "Forma: Redonda"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr "Forma: Miniatura"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shapes"
msgstr "Formas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "Show optimized images"
msgstr "Mostrar imágenes optimizadas"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Show/Hide on Mobile"
msgstr "Mostrar/ocultar en móvil"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Signature"
msgstr "Firma"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "Tamaño"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "Tamaño 1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "Tamaño 2x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "Tamaño 3x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "Tamaño 4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "Tamaño 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "Pequeño"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading"
msgstr "Título de sección pequeño"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Snippets"
msgstr "Snippets"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr "Sólido"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "Sólidos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"Someone with escalated rights previously modified this area, you are "
"therefore not able to modify it yourself."
msgstr ""
"Alguien con mayores permisos modificó esta área antes, por lo que no puede "
"modificarlo."

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Sorry, we could not generate a response. Please try again later."
msgstr "No pudimos generar un respuesta. Inténtelo de nuevo mas tarde. "

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Sorry, your prompt is too long. Try to say it in fewer words."
msgstr "Su solicitud es muy larga. Intente resumirla. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "Especiales"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid ""
"Specify when the collaboration starts. 'Focus' will start the collaboration "
"session when the user clicks inside the text field (default), 'Start' when "
"the record is loaded (could impact performance if set)."
msgstr ""
"Especifique cuando la colaboración comience. 'Foco' iniciará la colaboración"
" cuando el usuario haga clic dentro del campo de texto (por defecto). "
"'Empezar' cuando el registro se cargue (si está configurado, puede impactar "
"en el rendimiento)."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid "Start"
msgstr "Empezar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Structure"
msgstr "Estructura"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr "Estilo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Suggestions"
msgstr "Sugerencias"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr "Cambiar dirección"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction"
msgstr "Cambiar la dirección del texto."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "Tabla"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr "Opciones de tabla"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "Texto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Text Color"
msgstr "Color del texto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr "Alineación del texto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr "Estilo de texto"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr "La URL no parece funcionar."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#, python-format
msgid "The URL seems valid."
msgstr "La URL parece válida."

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_qweb_fields.py:0
#, python-format
msgid "The datetime %s does not match the format %s"
msgstr "La fecha y hora %s no coincide con el formato %s"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
#, python-format
msgid ""
"The document was already saved from someone with a different history for "
"model %r, field %r with id %r."
msgstr ""
"El documento ya se guardó de alguien con un historial diferente para el "
"modelo %r, campo %r con ID %r."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr "La URL proporcionada no hace referencia a ningún vídeo admitido"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
#, python-format
msgid "The provided url is invalid"
msgstr "La URL proporcionada no es válida"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "The provided url is not valid"
msgstr "La URL proporcionada no es válida"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                            If you need to keep your changes, copy the content below and edit the new document."
msgstr ""
"Se usará la versión de la base de datos.\n"
"                    Si necesita guardar sus cambios, copie el contenido a continuación y edite el nuevo documento."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "Tema"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "Colores del tema"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr "Hay un conflicto entre su versión y la versión en la base de datos."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#, python-format
msgid "Thinking..."
msgstr "Pensando..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "Esta URL es inválido. No se puede actualizar la vista previa."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated."
msgstr "Este bloque está obsoleto."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr "¡Este documento no esta guardado!"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "This file is a public view attachment."
msgstr "Este archivo es un archivo adjunto de vista pública."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
#, python-format
msgid "This file is attached to the current record."
msgstr "Este archivo está adjunto al registro actual."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
#, python-format
msgid "This image is an external image"
msgstr "Esta imagen es una imagen externa."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""
"No se puede recortar este tipo de imagen.<br/>Si desea recortarla, primero "
"descárguela de su origen y súbala a Odoo."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "Título"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "Etiqueta de título "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""
"Para guardar un snippet primero debemos guardar todas sus modificaciones "
"anteriores y actualizar la página."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "Actividades pendientes"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "Tostadora"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr "Alternar negritas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr "Alternar lista"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr "Alternar giro de icono"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr "Alternar cursiva"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr "Alternar lista ordenada"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr "Alternar tachado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr "Alternar subrayado"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr "Alternar lista desordenada"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr "Información sobre herramientas"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist"
msgstr "Llevar el seguimiento de las tareas con una lista de pendientes"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "Transformar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "Transformar la fotografía"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""
"Transforme la imagen (haga clic dos veces para restablecer la "
"transformación)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "Traducir"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr "Colores transparentes"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
#, python-format
msgid "Try searching with other keywords."
msgstr "Intente buscar otras palabras clave."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "Tipo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr "Escriba \"/\" para comandos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "URL o correo electrónico"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "Desalinear"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#, python-format
msgid "Upload a document"
msgstr "Subir un documento"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Upload an image"
msgstr "Subir una imagen"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: "
msgstr "El formato de la imagen subida no es compatible. Intente con:"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr "El formato de la imagen subida no es compatible. Intente con: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "Valencia"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Vertical mirror"
msgstr "Espejo vertical"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Video"
msgstr "Vídeo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr "Formato de vídeo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Video code"
msgstr "Código de vídeo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
#, python-format
msgid "Videos"
msgstr "Vídeos"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr ""
"Los vídeos se silencian cuando la reproducción automática está habilitada"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "Vista"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""
"Advertencia: después de cerrar este cuadro de diálogo se descartará la "
"versión en la que estaba trabajando y nunca volverá a estar disponible."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "Ondulado"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Subprueba de convertidor del editor web"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Prueba de convertidor del editor web"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Widgets"
msgstr "Widgets"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Width"
msgstr "Ancho"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#, python-format
msgid ""
"With the option enabled, all content can only be viewed in a sandboxed "
"iframe or in the code editor."
msgstr ""
"Con la opción habilitada, solo se podrá ver el contenido desde una iframe "
"aislada o en el código del editor. "

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid ""
"Wow, it feels a bit empty in here. Upload from the button in the top right "
"corner!"
msgstr ""
"Está un poco vacío aquí. Cargue sus archivos con el botón de la esquina "
"superior derecha."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Write something..."
msgstr "Escriba algo..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr "XL"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid ""
"You can not replace a field by this image. If you want to use this image, "
"first save it on your computer and then upload it here."
msgstr ""
"No puede reemplazar un campo por esta imagen. Si desea utilizar la imagen, "
"primero guárdela en su ordenador y después súbala."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "You can not use this image in a field"
msgstr "No puede utilizar esta imagen en un campo"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "You can still access the block options but it might be ineffective."
msgstr ""
"Todavía puede acceder a las opciones de bloqueo pero podría no funcionar."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""
"Puede cargar documentos con el botón ubicado en la esquina superior "
"izquierda de la pantalla."

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"Ha alcanzado el número máximo de solicitudes para este servicio. Vuelva a "
"intentarlo más tarde."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "You might not be able to customize it anymore."
msgstr "Es posible que ya no pueda personalizarlo."

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr "Debe especificar datos o URL para crear un archivo adjunto."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Your URL"
msgstr "Su URL"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Your content was successfully generated."
msgstr "Su contenido se ha generado correctamente."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "Acercar"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Alejar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "añadir"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#, python-format
msgid "alternatives..."
msgstr "alternativas..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "and"
msgstr "y"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "automático"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "oscurecer"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "exclusión"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "aclarar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "multiplicar"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "superponer"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "px"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "pantalla"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#, python-format
msgid "videos"
msgstr "vídeos"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_websocket
msgid "websocket message handling"
msgstr "gestión de mensajes de WebSocket"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "www.example.com"
msgstr "www.example.com"
