<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M1,1H0V0H1Z"/>
    </defs>
    <svg y="7%" viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)" y="7%" transform="translate(0, -1)">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <rect width="100%" height="7%" fill="#383E45"/>
    <svg height="7%" width="100%" viewBox="0 0 100 40" preserveAspectRatio="xMinYMid meet">
        <g>
            <path d="M72.88,20a8,8,0,1,0,8-8A8,8,0,0,0,72.88,20Z" fill="#61c454"/>
            <path d="M45.08,20a8,8,0,1,0,8-8A8,8,0,0,0,45.08,20Z" fill="#f4bd50"/>
            <path d="M17.14,20a8,8,0,1,0,8-8A8,8,0,0,0,17.14,20Z" fill="#ed695e"/>
        </g>
    </svg>  
</svg>
