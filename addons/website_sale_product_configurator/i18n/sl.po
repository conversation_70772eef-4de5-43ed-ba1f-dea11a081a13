# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_product_configurator
# 
# Translators:
# J<PERSON>mina Macur <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# matja<PERSON> k <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Cena</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">Izdelek</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>Skupaj:</strong>"

#. module: website_sale_product_configurator
#: model:ir.model.fields,field_description:website_sale_product_configurator.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Akcija dodajanja v košarico"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Dodajte ga"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Add to cart"
msgstr "Dodaj v voziček"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "Možnosti:"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Continue Shopping"
msgstr "Nadaljuj z nakupovanjem"

#. module: website_sale_product_configurator
#: model:ir.model.fields.selection,name:website_sale_product_configurator.selection__website__add_to_cart_action__force_dialog
msgid "Let the user decide (dialog)"
msgstr "Naj se uporabnik odloči (pojavno okno)"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "Možnost ni na voljo"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Proceed to Checkout"
msgstr "Zaključi nakup"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Slika izdelka"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Quantity"
msgstr "Količina"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Odstrani ga"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_website
msgid "Website"
msgstr "Spletna stran"
