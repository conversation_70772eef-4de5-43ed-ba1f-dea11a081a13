# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_product_configurator
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2023
# Константи<PERSON> Коровин <<EMAIL>>, 2023
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Цена</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">Продукт</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>Общая:</strong>"

#. module: website_sale_product_configurator
#: model:ir.model.fields,field_description:website_sale_product_configurator.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Действие “В корзину”"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Добавить один"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Add to cart"
msgstr "Добавить в корзину"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "Доступные опции: "

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Continue Shopping"
msgstr "Продолжить покупки"

#. module: website_sale_product_configurator
#: model:ir.model.fields.selection,name:website_sale_product_configurator.selection__website__add_to_cart_action__force_dialog
msgid "Let the user decide (dialog)"
msgstr "Пусть пользователь сам решает (диалог)"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "Опция недоступна"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Proceed to Checkout"
msgstr "Перейти к оформлению заказа"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Изображение товара"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Quantity"
msgstr "Количество"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Удалить один"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_website
msgid "Website"
msgstr "Сайт"
