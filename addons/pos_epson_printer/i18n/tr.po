# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Halil, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Halil, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Nakit Çekmece"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"'Aygıt Kimliği' ayarı için yazıcı yapılandırmasını kontrol edin. Şu şekilde "
"ayarlanmalıdır:"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Epson Yazıcı IP'si"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Epson Printer IP Address"
msgstr "Epson Yazıcısı IP Adresi"

#. module: pos_epson_printer
#. odoo-python
#: code:addons/pos_epson_printer/models/pos_printer.py:0
#, python-format
msgid "Epson Printer IP Address cannot be empty."
msgstr ""

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Epson Makbuz Yazıcısı IP Adresi"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s. "
msgstr ""

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
#: model:ir.model.fields,help:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Epson makbuz yazıcısının yerel IP adresi."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "Yazıcı tarafından kağıt algılanmadı"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Lütfen yazıcıda yeterli kağıt olup olmadığını ve yazdırmaya hazır olup "
"olmadığını kontrol edin."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Satış Noktası Yapılandırması"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_printer
msgid "Point of Sale Printer"
msgstr ""

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_session
msgid "Point of Sale Session"
msgstr "Satış Noktası Oturumu"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "Pos Epson Yazıcı IP'si"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Yazıcı Türü"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "Printing failed"
msgstr "Yazdırma başarısız oldu"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"IoT Box'a bağlı fiş yazıcısı yerine Epson fiş yazıcısı kullanılacaktır."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "Yazıcı tarafından aşağıdaki hata kodu verildi:"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "Yazıcıya başarıyla ulaşıldı, ancak yazdırılamadı."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
"Hata nedeni hakkında daha fazla ayrıntı bulmak için lütfen çevrimiçi olarak "
"şunu arayın:"

#. module: pos_epson_printer
#: model:ir.model.fields.selection,name:pos_epson_printer.selection__pos_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Bir Epson yazıcı kullanın"
