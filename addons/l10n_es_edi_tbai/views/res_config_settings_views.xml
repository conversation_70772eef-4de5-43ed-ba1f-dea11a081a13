<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.l10n.es</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@name='spain_localization']/setting" position="attributes">
                <attribute name="string">Registro de Libros connection SII/TicketBAI</attribute>
            </xpath>
            <xpath expr="//block[@name='spain_localization']/setting/div[hasclass('content-group')]/div[hasclass('mt16')]/*" position="before">
                <label for="l10n_es_tbai_tax_agency" class="o_light_label"/>
                <field name="l10n_es_tbai_tax_agency"/>
                <div class="text-muted" invisible="l10n_es_tbai_tax_agency">
                    No tax agency selected: TicketBAI not activated.
                </div>
                <div class="text-muted" invisible="not l10n_es_tbai_tax_agency">
                    Tax agency selected: TicketBAI is activated.
                </div>
                <br/>
            </xpath>
        </field>
    </record>
</odoo>
