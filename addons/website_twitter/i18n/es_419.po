# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# Wil O<PERSON>, 2023
# Iran Villalobos López, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ", <strong>create a project</strong> with the following information:"
msgstr ""
", <strong>cree un proyecto</strong> que incluya la siguiente información:"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
", click on <strong>Elevated</strong> then on <strong>Apply</strong> and "
"finally complete the form."
msgstr ""
", haga clic en <strong>Elevado</strong>, después en <strong>Aplicar</strong>"
" y por último complete el formulario."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Muéstreme cómo obtener la clave API y la clave secreta API de Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>App Name: </strong> choose a unique name"
msgstr "<strong>Nombre de la aplicación: </strong> elija un nombre único"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>Descripción: </strong> integración de Odoo con Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>Nombre: </strong> Integración de Odoo con Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Use Case: </strong> Embedding Tweets in a website"
msgstr "<strong>Caso de uso: </strong> insertar tweets en un sitio web"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "Clave API"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "Secreto de la API"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"Faltan las credenciales de autenticación o son erróneas. Es posible que las "
"publicaciones estén protegidas."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Copy/Paste the API Key and API Key Secret values into the above fields"
msgstr ""
"Copie y pegue los valores de la clave API y la clave secreta API en los "
"campos anteriores"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "Favoritos de"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Get Elevated access by going to"
msgstr "Obtenga acceso avanzado en"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "Obtener los favoritos de este usuario"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "Error HTTP: algo está mal configurado"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "Cómo configurar el acceso a la API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr ""
"Conexión a internet interrumpida: no pudimos conectarnos a un servidor de "
"Twitter."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Log in or create an account on"
msgstr "Inicie sesión o cree una cuenta en"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "On the"
msgstr "En el "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Once connected, and if not already done, complete the Twitter portal access "
"process on"
msgstr ""
"Una vez conectado, si no lo ha hecho, termine el proceso de acceso al portal"
" de Twitter en"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"Defina un nombre de usuario de Twitter para cargar los favoritos en los "
"ajustes de Sitio web (no tiene que ser el suyo)"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr "Coloque su clave y su clave secreta API en los ajustes del sitio web."

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr "Volver a cargar"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"No se puede cumplir con la solicitud, se agotó el límite de la tasa de "
"solicitudes para el recurso."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "Nombre de la cuenta"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"Nombre de la cuenta de Twitter desde la que desea cargar los favoritos. No "
"tiene que ser la misma que la de la clave API."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"Los servidores de Twitter funcionan, pero están saturados de solicitudes. "
"Inténtelo más tarde."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"Los servidores de Twitter funcionan, pero no se pudo atender la solicitud "
"debido a algún error interno. Inténtelo más tarde."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"Se entiende la solicitud pero se ha rechazado o no tiene acceso. Revise su "
"clave API y secreto de Twitter."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"La solicitud no es válida o no se puede atender. Las solicitudes sin "
"autenticación no se consideran válidas y proporcionarán esta respuesta."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr "No hay nuevos datos que mostrar."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "ID del tweet"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "Tweets"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "Credenciales API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "Clave API de Twiter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "Clave secreta API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "Clave API de Twiter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr "Puede obtener la clave API de Twitter en https://apps.twitter.com/"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "Clave secreta API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr ""
"Puede obtener la clave secreta API de Twitter en https://apps.twitter.com/"

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr "Configuración de Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Portal"
msgstr "Portal de Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Roller"
msgstr "Roller de twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.website_twitter_snippet
msgid "Twitter Scroller"
msgstr "Panel de Twitter"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter authorization error! Please double-check your Twitter API Key and "
"Secret!"
msgstr ""
"Error de autorización de Twitter. Verifique la clave API y la clave secreta "
"API de Twitter."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter está fuera de servicio o se está actualizando."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"Parece que Twitter está fallando en este momento, inténtelo después. Le "
"sugerimos que considere publicar el problema en los foros de Twitter para "
"obtener ayuda."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "URI del servidor de Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "Tutorial de Twitter"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"El usuario de Twitter @%(username)s tiene menos de 12 tweets marcados como "
"favoritos. Agregue más o seleccione a otro usuario."

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr "Usuario de Twitter"

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: buscar nuevos favoritos"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "Twitter del sitio web"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/"
msgstr "https://developer.twitter.com/"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/en/portal/products"
msgstr "https://developer.twitter.com/en/portal/products"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/portal/"
msgstr "https://developer.twitter.com/portal/"
