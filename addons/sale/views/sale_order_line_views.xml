<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_order_line_tree" model="ir.ui.view">
        <field name="name">sale.order.line.tree</field>
        <field name="model">sale.order.line</field>
        <field name="arch" type="xml">
            <tree string="Sales Order Lines" create="false">
                <field name="order_id"/>
                <field name="order_partner_id"/>
                <field name="name"/>
                <field name="salesman_id"/>
                <field name="product_uom_qty" string="Qty"/>
                <field name="qty_delivered"/>
                <field name="qty_invoiced"/>
                <field name="qty_to_invoice"/>
                <field name="product_uom" string="Unit of Measure" groups="uom.group_uom"/>
                <field name="price_subtotal" sum="Total" widget="monetary"/>
                <field name="currency_id" column_invisible="True"/>
            </tree>
        </field>
    </record>

    <record id="sale_order_line_view_form_readonly" model="ir.ui.view">
        <field name="name">sale.order.line.form.readonly</field>
        <field name="model">sale.order.line</field>
        <field name="arch" type="xml">
            <form string="Sales Order Item" edit="false">
                <field name="display_type" invisible="1"/>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="product_updatable" invisible="1"/>
                            <field name="order_id" readonly="1" force_save="1"/>
                            <field name="product_uom_category_id" invisible="1"/>
                            <field name="product_uom_readonly" invisible="1"/>
                            <field name="qty_delivered_method" invisible="1"/>
                            <field name="state" invisible="1"/>
                            <field name="product_id"
                                readonly="id and not product_updatable"
                                required="not display_type"
                                force_save="1"
                                widget="many2one_barcode"/>
                            <field name="name"/>
                            <field name="product_uom_qty"/>
                            <field name="qty_delivered" 
                                invisible="state not in ['sale', 'done']"
                                readonly="qty_delivered_method != 'manual'"/>
                            <field name="qty_invoiced" invisible="state not in ['sale', 'done']"/>
                            <field name="product_uom"
                                force_save="1"
                                groups="uom.group_uom"
                                readonly="product_uom_readonly"
                                required="not display_type"/>
                            <field name="product_uom" groups="!uom.group_uom" invisible="1"/>
                            <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                            <field name="order_partner_id" invisible="1"/>
                            <field name="display_type" invisible="1"/>
                            <field name="product_updatable" invisible="1"/>
                        </group>
                        <group>
                            <field name="company_id" invisible="1"/>
                            <field name="tax_country_id" invisible="1"/>
                            <field name="price_unit"/>
                            <field name="discount" groups="product.group_discount_per_so_line"/>
                            <field name="price_subtotal" widget="monetary"/>
                            <field name="tax_id"
                                widget="many2many_tags"
                                options="{'no_create': True}"
                                context="{'search_view_ref': 'account.account_tax_view_search'}"
                                domain="[('type_tax_use', '=', 'sale'), ('company_id', '=', company_id), ('country_id', '=', tax_country_id)]"
                                readonly="qty_invoiced &gt; 0"/>
                            <field name="price_tax" widget="monetary"/>
                            <field name="price_total" widget="monetary"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_sales_order_line_filter" model="ir.ui.view">
        <field name="name">sale.order.line.select</field>
        <field name="model">sale.order.line</field>
        <field name="arch" type="xml">
            <search string="Search Sales Order">
                <filter string="To Invoice"
                    name="to_invoice"
                    domain="[('qty_to_invoice', '!=', 0)]"
                    help="Sales Order Lines ready to be invoiced"/>
                <separator/>
                <filter string="My Sales Order Lines"
                    name="my_sales_order_lines"
                    domain="[('salesman_id','=',uid)]"
                    help="Sales Order Lines related to a Sales Order of mine"/>
                <field name="order_id"/>
                <field name="order_partner_id" operator="child_of"/>
                <field name="product_id"/>
                <field name="salesman_id"/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="product" domain="[]" context="{'group_by':'product_id'}"/>
                    <filter string="Order" name="order" domain="[]" context="{'group_by':'order_id'}"/>
                    <filter string="Salesperson" name="salesperson" domain="[]" context="{'group_by':'salesman_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="sale_order_line_view_kanban" model="ir.ui.view">
        <field name="name">sale.order.line.kanban</field>
        <field name="model">sale.order.line</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-12">
                                <field name="display_name"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
