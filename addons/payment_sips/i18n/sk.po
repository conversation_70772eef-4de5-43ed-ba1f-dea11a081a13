# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__code
msgid "Code"
msgstr "Kód"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_version
msgid "Interface Version"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_merchant_id
msgid "Merchant ID"
msgstr "ID obchodníka"

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Platobná transakcia"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_prod_url
msgid "Production URL"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_secret
msgid "SIPS Secret Key"
msgstr ""

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_provider_form
msgid "Secret Key"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_key_version
msgid "Secret Key Version"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_provider__code__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_test_url
msgid "Test URL"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr ""
