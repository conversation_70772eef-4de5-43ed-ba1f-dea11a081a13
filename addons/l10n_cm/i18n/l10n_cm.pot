# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cm
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:00+0000\n"
"PO-Revision-Date: 2023-11-30 10:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_normal
msgid "10. Taxable operations at normal rate"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_excises
msgid "11. Amount of Excise Duty"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_other
msgid "12. Other taxable operations"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_export
msgid "13. Exports"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_exempt
msgid "14. Exempted turnover"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover_global
msgid "15. Global turnover"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_report
msgid "17. Previous credit's report"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_deductible_local_purchase
msgid "18. Deductible VAT on local purchases"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_deductible_local_service
msgid "19. Deductible VAT on local services"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_turnover
msgid "2-Turnover achieved"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_deductible_foreign_purchase
msgid "20. Deductible VAT on foreign purchases"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_deductible_foreign_service
msgid "21. Deductible VAT on services and other remuneration paid abroad"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_deductible_total
msgid "22. Total VAT deductible (L17+L18+L19+L20+L21)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_deductible
msgid "24. Adjustment of deductible VAT or VAT already withheld at source"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_state
msgid "25. Adjustment of VAT borne by the State"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_fixed
msgid "26. Adjustment on disposal of fixed assets to be repaid"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_other
msgid "27. Adjustment of VAT to be repaid and others"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_collected
msgid "28. Collected VAT (L10+L11+L12)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_deductible_vat
msgid "29. Deductible VAT (L22)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_deductible
msgid "3-Deductible VAT"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_to_deduct
msgid "30. Adjustment of VAT to be deducted (L24+L25)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment_to_pay
msgid "31. VAT adjustment to be repaid (L26+L27)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_vat_to_pay
msgid "32. VAT to pay (L28-L29-L30+L31)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_vat_credit
msgid "33. VAT credit"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_reimbursement
msgid "34. Reimbursement asked"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_credit_to_report
msgid "35. Credit to report (L33-L34)"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_adjustment
msgid "4-Exceptional adjustments"
msgstr ""

#. module: l10n_cm
#: model:account.report.line,name:l10n_cm.account_tax_report_line_cm_to_pay
msgid "5-VAT payable or VAT credit"
msgstr ""

#. module: l10n_cm
#: model:ir.model,name:l10n_cm.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_cm
#: model:account.report.column,name:l10n_cm.account_tax_report_cm_balance
msgid "Base"
msgstr ""

#. module: l10n_cm
#. odoo-python
#: code:addons/l10n_cm/models/template_cm_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_cm
#. odoo-python
#: code:addons/l10n_cm/models/template_cm.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_cm
#: model:account.report.column,name:l10n_cm.account_tax_report_cm_tax
msgid "Tax"
msgstr ""

#. module: l10n_cm
#: model:account.report,name:l10n_cm.account_tax_report_cm
msgid "VAT Report"
msgstr ""
