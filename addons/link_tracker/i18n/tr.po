# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* link_tracker
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Halil, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 10:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "%r is not a valid link, links cannot redirect to the current page."
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\"/>"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "<span class=\"o_stat_text\">Visit Page</span>"
msgstr "<span class=\"o_stat_text\">Sayfa Ziyareti</span>"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__absolute_url
msgid "Absolute URL"
msgstr "Mutlak URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__label
msgid "Button label"
msgstr "Buton Etiketi"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Campaign"
msgstr "Kampanya"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_click_action_statistics
msgid "Click Statistics"
msgstr "Tıklama İstatistikleri"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "Clicks"
msgstr "Tıklama"

#. module: link_tracker
#: model:ir.model.constraint,message:link_tracker.constraint_link_tracker_code_code
msgid "Code must be unique."
msgstr "`kod` eşşiz olmalı."

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr "Kodlar"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Country"
msgstr "Ülke"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid "Create a link tracker"
msgstr "Bağlantı izleyici oluşturma"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Creating a Link Tracker without URL is not possible"
msgstr "URL olmadan bir Bağlantı İzleyici oluşturmak mümkün değildir"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Group By"
msgstr "Grupla"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr "Kısa URL sunucusu"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "ID"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr "İnternet Protokolü"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Link"
msgstr "Bağlantı"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_form
msgid "Link Click"
msgstr "Bağlantı Tıklaması"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_graph
msgid "Link Clicks"
msgstr "Bağlantı Tıklamaları"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.link_tracker_menu_main
msgid "Link Tracker"
msgstr "Bağlantı İzleyici"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Bağlantı İzleyici Tıklaması"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr "Bağlantı İzleyici Kodu"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid ""
"Link Tracker values (URL, campaign, medium and source) must be unique (%s, "
"%s, %s, %s)."
msgstr ""
"Bağlantı İzleyici değerleri (URL, kampanya, aracı ve kaynak) benzersiz "
"olmalıdır (%s, %s, %s %s)."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Links"
msgstr "Bağlantılar"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_tree
msgid "Links Clicks"
msgstr "Bağlantı Tıklamaları"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mail Render Mixin"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Medium"
msgstr "Aracı:"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_click_action_statistics
msgid "No data yet!"
msgstr "Henüz veri yok!"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr "Tıklanma Sayısı"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_utm_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "Kampanya tarafından oluşturulan tıklamaların sayısı"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "Sayfa Başlığı"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr "Yönlendirilmiş URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr "Kısa URL Kodu"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr "Kısa URL kodu"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Source"
msgstr "Kaynak"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action_campaign
msgid "Statistics of Clicks"
msgstr "Tıklanma İstatistikleri"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr "Hedef URL"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
#: model:ir.model.fields,help:link_tracker.field_link_tracker_click__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Farklı kampanya çabalarınızı izlemenize yardımcı olacak bir isimdir Örn: "
"Sonbahar_Sürüşü, Yılbaşı_Özel"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "Bu teslimat yöntemidir, örn: Posta kartı, E-posta ya da Afiş reklamı"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Bu bağlantının kaynağıdır. örn: Arama Motoru, başka alan adı yada bir "
"e-posta listesi adı"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Title and URL"
msgstr "Başlık ve URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
msgid "Tracked URL"
msgstr "İzlenen URL"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid ""
"Trackers are used to collect count stat about click on links and generate "
"short URLs."
msgstr ""
"İzleyiciler, bağlantılara ilişkin tıklama istatistiklerini toplamak ve kısa "
"URL'ler oluşturmak için kullanılır."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "URL"
msgstr "URL"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "UTM"
msgstr "UTM"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_utm_campaign
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__campaign_id
msgid "UTM Campaign"
msgstr "UTM Kampanyası"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Visit Page"
msgstr "Sayfa Ziyareti"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage"
msgstr "Web Sayfasını Ziyareti"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Website Link"
msgstr "Website Linki"
