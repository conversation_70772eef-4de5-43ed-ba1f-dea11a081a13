<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_move_form_l10n_pl" model="ir.ui.view">
        <field name="name">account.move.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <notebook position="inside">
                <page id="pl_extra" string="PL Extra" name="page_pl_extra" invisible="move_type not in ('out_invoice', 'out_refund') or country_code != 'PL'">
                    <group>
                        <group>
                            <field name="l10n_pl_vat_b_spv_dostawa" readonly="state != 'draft'"/>
                            <field name="l10n_pl_vat_b_mpv_prowizja" readonly="state != 'draft'"/>
                        </group>
                        <group>
                            <field name="l10n_pl_vat_b_spv" readonly="state != 'draft'"/>
                        </group>
                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo>
