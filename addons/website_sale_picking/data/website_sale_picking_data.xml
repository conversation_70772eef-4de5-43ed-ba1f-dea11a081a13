<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="onsite_delivery_product" model="product.product">
        <field name="name">On site picking</field>
        <field name="description">Pay in Store</field>
        <field name="type">service</field>
        <field name="list_price">0</field>
        <field name="purchase_ok">false</field>
        <field name="sale_ok">false</field>
    </record>

    <record model="delivery.carrier" id="website_sale_picking.default_onsite_carrier">
        <field name="name">[On Site Pick] My Shop 1</field>
        <field name="delivery_type">onsite</field>
        <field name="website_published">true</field>
        <field name="product_id" ref="website_sale_picking.onsite_delivery_product"/>
        <field name="website_id" ref="website.default_website"/>
    </record>

</odoo>
