<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="crm_activity_report_view_graph" model="ir.ui.view">
            <field name="name">crm.activity.report.graph</field>
            <field name="model">crm.activity.report</field>
            <field name="arch" type="xml">
                <graph string="Activities Analysis" sample="1">
                    <field name="mail_activity_type_id"/>
                    <field name="date" interval="month"/>
                </graph>
            </field>
        </record>

        <record id="crm_activity_report_view_pivot" model="ir.ui.view">
            <field name="name">crm.activity.report.pivot</field>
            <field name="model">crm.activity.report</field>
            <field name="arch" type="xml">
                <pivot string="Activities Analysis" sample="1">
                    <field name="mail_activity_type_id" type="col"/>
                    <field name="date" interval="month" type="row"/>
                </pivot>
            </field>
        </record>

        <record id="crm_activity_report_view_tree" model="ir.ui.view">
            <field name="name">crm.activity.report.tree</field>
            <field name="model">crm.activity.report</field>
            <field name="arch" type="xml">
                <tree default_order="date desc">
                    <field name="date"/>
                    <field name="author_id" widget="many2one_avatar"/>
                    <field name="mail_activity_type_id"/>
                    <field name="body" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="tag_ids" string="Lead Tags" optional="show" widget="many2many_tags" options="{'color_field': 'color'}"/>
                </tree>
            </field>
        </record>

        <record id="crm_activity_report_view_search" model="ir.ui.view">
            <field name="name">crm.activity.report.search</field>
            <field name="model">crm.activity.report</field>
            <field name="arch" type="xml">
                <search string="Activities Analysis">
                    <field name="mail_activity_type_id" string="Activity Type"/>
                    <field name="lead_id" string="Opportunity"/>
                    <field name="user_id" string="Salesperson"/>
                    <field name="team_id" context="{'invisible_team': False}"/>
                    <field name="author_id" string="Assigned To"/>
                    <field name="tag_ids" string="Lead Tags"/>
                    <separator groups="crm.group_use_lead"/>
                    <filter string="Leads" name="lead" domain="[('lead_type', '=', 'lead')]" help="Show only lead" groups="crm.group_use_lead"/>
                    <filter string="Opportunities" name="opportunity" domain="[('lead_type', '=', 'opportunity')]" help="Show only opportunity" groups="crm.group_use_lead"/>
                    <separator/>
                    <filter string="Won" name="won" domain="[('stage_id.is_won', '=', True)]"/>
                    <separator/>
                    <filter string="Trailing 12 months" name="completion_date" domain="[
                        ('date', '>=', (datetime.datetime.combine(context_today() + relativedelta(days=-365), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                        ('date', '&lt;=', (datetime.datetime.combine(context_today(), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                    <separator/>
                    <filter name="filter_date" date="date"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="1" string="Group By">
                        <filter string="Activity" name="group_by_activity_type" context="{'group_by': 'mail_activity_type_id'}"/>
                        <filter string="Type" name="group_by_subtype" context="{'group_by': 'subtype_id'}"/>
                        <filter string="Assigned To" name="group_by_author_id" context="{'group_by': 'author_id'}"/>
                        <filter string="Completion Date" name="group_by_completion_date" context="{'group_by': 'date:month'}"/>
                        <separator/>
                        <filter string="Salesperson" name="group_by_user_id" context="{'group_by': 'user_id'}"/>
                        <filter string="Sales Team" name="saleschannel" context="{'group_by': 'team_id'}"/>
                        <filter string="Stage" name="stage" context="{'group_by': 'stage_id'}"/>
                        <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Creation Date" name="group_by_lead_date_creation" context="{'group_by': 'lead_create_date'}"/>
                        <filter string="Expected Closing" name="group_by_date_deadline" context="{'group_by': 'date_deadline'}"/>
                        <filter string="Closed Date" name="group_by_date_closed" context="{'group_by': 'date_closed'}"/>
                    </group>
                </search>
            </field>
        </record>

       <record id="crm_activity_report_action" model="ir.actions.act_window">
           <field name="name">Activities</field>
           <field name="res_model">crm.activity.report</field>
           <field name="view_mode">graph,pivot,tree</field>
           <field name="context">{
                'search_default_completion_date': 1,
                'pivot_column_groupby': ['subtype_id', 'mail_activity_type_id'],
                'pivot_row_groupby': ['date:month'],
                'graph_mode': 'bar',
                'graph_groupbys': ['date:month', 'subtype_id'],
            }</field>
            <field name="domain">[]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p><p>
                    Start scheduling activities on your opportunities
                </p>
            </field>
       </record>

        <record id="crm_activity_report_action_team" model="ir.actions.act_window">
            <field name="name">Pipeline Activities</field>
            <field name="res_model">crm.activity.report</field>
            <field name="view_mode">graph,pivot,tree</field>
            <field name="context">{'search_default_team_id': active_id}</field>
            <field name="domain">[]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p><p>
                    Start scheduling activities on your opportunities
                </p>
            </field>
        </record>

</odoo>
