<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="biometric_device_sync_wiz" model="ir.ui.view">
          <field name="name">Device Sync</field>
          <field name="model">biometric.device.wizard</field>
          <field name="arch" type="xml">
            <form string="Device Sync">
                <label for="server_id" nolabel="1" colspan="4" string="This wizard will help you for importing device data"/>
                <img src='/hr_biometric/static/img/fingerprint.png' alt="fingerprint"/>
                <group>
                    <field name="server_id" options="{'no_open':True, 'no_create':True}"/>
                </group>
              <footer>
                <button
                    class="oe_highlight" name="import_devices"
                    string="Sync Devices" type="object"/>
                or
                <button string="Cancel" class="oe_link" special="cancel"/>
              </footer>
            </form>
          </field>
        </record>

        <record id="action_biometric_device_sync_wiz" model="ir.actions.act_window">
          <field name="name">Device Sync</field>
          <field name="type">ir.actions.act_window</field>
          <field name="res_model">biometric.device.wizard</field>
          <field name="view_mode">form</field>
          <field name="target">new</field>
          <field name="view_id" ref="biometric_device_sync_wiz"/>
        </record>
        
        <menuitem
            action="action_biometric_device_sync_wiz"
            id="menu_biometric_device_sync_wiz"
            sequence="3" groups="hr_biometric.group_biometric_manager"
            parent="menu_biometric_attendance_sync"/>
        
    </data>
</odoo>


